using DrMuscle.UITests.Extensions;
using DrMuscle.UITests.Helpers;
using DrMuscle.UITests.PageObjects;

namespace DrMuscle.UITests.Tests
{
    [TestFixture]
    public class StartWorkoutFlow
    {
        private IApp? _app;

        [SetUp]
        public void SetUp()
        {
            _app = AppInitializer.StartApp();
        }

        [Test]
        public void UserCanStartWorkoutFromHomePage()
        {
            // Arrange - First complete signup to have an account
            var (email, password) = TestAccount.Generate();
            var welcomePage = new WelcomePage(_app!);

            // Act - Sign up first
            _app!.Screenshot("Welcome Page");
            welcomePage.WaitForPageToLoad();

            var registrationPage = welcomePage.TapGetStarted();
            registrationPage.WaitForPageToLoad();
            registrationPage.EnterEmail(email);
            registrationPage.EnterPassword(password);

            var onboardingPage = registrationPage.TapSignUp();
            var mainPage = onboardingPage.CompleteOnboarding();
            _app.Screenshot("Main Page After Signup");

            // Verify we're on the main page
            mainPage.WaitForPageToLoad();
            Assert.That(mainPage.IsStartWorkoutButtonVisible(), Is.True,
                "Start Workout button should be visible");

            // Start a workout
            var workoutPage = mainPage.TapStartWorkout();
            _app.Screenshot("Workout Page");

            // Assert - Verify we navigated to the workout page
            workoutPage.WaitForPageToLoad();
            Assert.That(workoutPage.IsWorkoutPageVisible(), Is.True,
                "Workout page should be visible after tapping Start Workout");
        }
    }
}
