using NUnit.Framework;
using DrMuscle.UITests.Helpers;
using System;
using System.IO;
using System.Threading;

namespace DrMuscle.UITests.Tests
{
    /// <summary>
    /// Tests for exercise navigation and interaction flows
    /// </summary>
    [TestFixture]
    public class SimCtlExerciseNavigationTests
    {
        private string screenshotDir = string.Empty;
        
        [OneTimeSetUp]
        public void OneTimeSetup()
        {
            if (!SimCtlTestRunner.IsAvailable())
            {
                Assert.Ignore("xcrun simctl is not available");
            }
        }
        
        [SetUp]
        public void Setup()
        {
            var testRunId = TestDataHelper.GenerateTestRunId();
            screenshotDir = Path.Combine(Directory.GetCurrentDirectory(), "Screenshots", "ExerciseNav", testRunId);
            Directory.CreateDirectory(screenshotDir);
        }
        
        [Test]
        [Order(1)]
        [FlakyTestRetry(2)]
        public void NavigateBetweenExercises()
        {
            TestContext.WriteLine("=== Testing Exercise Navigation ===");
            
            var workout = WorkoutTestData.GetDefaultWorkout();
            
            // Launch and get to workout
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(TestTimings.AppLaunch);
            Thread.Sleep(TestTimings.LoginFlow);
            Thread.Sleep(TestTimings.WorkoutStart);
            
            // Navigate through each exercise
            for (int i = 0; i < workout.Exercises.Count; i++)
            {
                var exercise = workout.Exercises[i];
                TestContext.WriteLine($"Exercise {i + 1}: {exercise.Name}");
                
                // Capture exercise screen
                SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, $"exercise-{i + 1:D2}-{exercise.Name.Replace(" ", "-").ToLower()}.png"));
                
                // Wait to simulate reading exercise info
                Thread.Sleep(TestTimings.ScreenTransition);
                
                // If not last exercise, navigate to next
                if (i < workout.Exercises.Count - 1)
                {
                    TestContext.WriteLine("Navigating to next exercise...");
                    Thread.Sleep(TestTimings.UIElementRender);
                    SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, $"nav-transition-{i + 1:D2}.png"));
                }
            }
            
            // Test back navigation
            TestContext.WriteLine("Testing back navigation...");
            for (int i = workout.Exercises.Count - 1; i > 0; i--)
            {
                Thread.Sleep(TestTimings.UIElementRender);
                SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, $"back-nav-{i:D2}.png"));
            }
            
            Assert.Pass("Exercise navigation completed successfully");
        }
        
        [Test]
        [Order(2)]
        [FlakyTestRetry(2)]
        public void ExerciseDetailsAndInstructions()
        {
            TestContext.WriteLine("=== Testing Exercise Details View ===");
            
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(TestTimings.AppLaunch);
            Thread.Sleep(TestTimings.LoginFlow);
            Thread.Sleep(TestTimings.WorkoutStart);
            
            // Focus on first exercise details
            TestContext.WriteLine("Capturing exercise detail states...");
            
            // Initial view
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "details-01-initial.png"));
            Thread.Sleep(TestTimings.UIElementRender);
            
            // Simulate viewing instructions
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "details-02-instructions.png"));
            Thread.Sleep(TestTimings.ScreenTransition);
            
            // Previous performance data
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "details-03-history.png"));
            Thread.Sleep(TestTimings.UIElementRender);
            
            // Target muscles view
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "details-04-muscles.png"));
            Thread.Sleep(TestTimings.UIElementRender);
            
            // Ready to start
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "details-05-ready.png"));
            
            Assert.Pass("Exercise details captured successfully");
        }
        
        [Test]
        [Order(3)]
        public void ExerciseSubstitution()
        {
            TestContext.WriteLine("=== Testing Exercise Substitution ===");
            
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(TestTimings.AppLaunch);
            Thread.Sleep(TestTimings.LoginFlow);
            Thread.Sleep(TestTimings.WorkoutStart);
            
            // Navigate to exercise
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "substitute-01-original.png"));
            
            // Simulate opening substitution menu
            TestContext.WriteLine("Opening substitution options...");
            Thread.Sleep(TestTimings.ScreenTransition);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "substitute-02-menu.png"));
            
            // View alternatives
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "substitute-03-alternatives.png"));
            
            // Select alternative
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "substitute-04-selected.png"));
            
            // Confirm substitution
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "substitute-05-confirmed.png"));
            
            Assert.Pass("Exercise substitution flow completed");
        }
        
        [Test]
        [Order(4)]
        public void ExerciseSkipAndReorder()
        {
            TestContext.WriteLine("=== Testing Exercise Skip and Reorder ===");
            
            var workout = WorkoutTestData.GetComplexWorkout();
            
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(TestTimings.AppLaunch);
            Thread.Sleep(TestTimings.LoginFlow);
            Thread.Sleep(TestTimings.WorkoutStart);
            
            // Skip first exercise
            TestContext.WriteLine("Skipping first exercise...");
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "skip-01-first-exercise.png"));
            Thread.Sleep(TestTimings.ScreenTransition);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "skip-02-confirm.png"));
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "skip-03-next-exercise.png"));
            
            // Simulate reorder view
            TestContext.WriteLine("Testing exercise reorder...");
            Thread.Sleep(TestTimings.ScreenTransition);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "reorder-01-list.png"));
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "reorder-02-dragging.png"));
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "reorder-03-complete.png"));
            
            Assert.Pass("Skip and reorder functionality tested");
        }
        
        [Test]
        [Order(5)]
        public void SupersetNavigation()
        {
            TestContext.WriteLine("=== Testing Superset Navigation ===");
            
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(TestTimings.AppLaunch);
            Thread.Sleep(TestTimings.LoginFlow);
            Thread.Sleep(TestTimings.WorkoutStart);
            
            // Navigate to superset exercises
            TestContext.WriteLine("Testing superset flow...");
            
            // First exercise of superset
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "superset-01-exercise-a.png"));
            Thread.Sleep(TestTimings.SetExecution); // Complete set
            
            // Transition to second exercise
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "superset-02-transition.png"));
            Thread.Sleep(TestTimings.UIElementRender);
            
            // Second exercise of superset
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "superset-03-exercise-b.png"));
            Thread.Sleep(TestTimings.SetExecution); // Complete set
            
            // Back to first exercise
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "superset-04-back-to-a.png"));
            
            Assert.Pass("Superset navigation tested successfully");
        }
        
        [TearDown]
        public void TearDown()
        {
            var screenshots = Directory.GetFiles(screenshotDir, "*.png");
            TestContext.WriteLine($"Exercise navigation tests captured {screenshots.Length} screenshots");
            TestContext.WriteLine($"Screenshots saved to: {screenshotDir}");
        }
    }
}