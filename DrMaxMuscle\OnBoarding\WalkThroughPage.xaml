<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             NavigationPage.HasNavigationBar="False"
             x:Class="DrMaxMuscle.OnBoarding.WalkThroughPage"
             xmlns:local="clr-namespace:DrMaxMuscle.Cells"
             
             Title="WalkThroughPage">
    <ContentPage.Resources
        >
        <ResourceDictionary>
            <local:CaroselDataTemplateSelector
            x:Key="CaroselTemplateSelector"  >
            </local:CaroselDataTemplateSelector>
        </ResourceDictionary>
    </ContentPage.Resources>
    <Grid BackgroundColor="Black">
        <CarouselView x:Name="carouserView" AutomationId="OnboardingCarousel" BackgroundColor="Transparent" Loop="False" IsSwipeEnabled="True" IsBounceEnabled="True"  VerticalOptions="FillAndExpand" ItemTemplate="{StaticResource CaroselTemplateSelector}" PositionChanged="carouserView_PositionChanged" >
        </CarouselView>
        <StackLayout VerticalOptions="End">
            <IndicatorView HorizontalOptions="Center" AutomationId="OnboardingIndicator" IsEnabled="False" VerticalOptions="EndAndExpand" IndicatorColor="Gray" SelectedIndicatorColor="White" x:Name="indicatorView" />
            <Button x:Name="btnContinue" AutomationId="GetStartedButton" HorizontalOptions="FillAndExpand" Text="Get started" TextColor="White" BackgroundColor="Transparent" CornerRadius="0" BorderWidth="2" BorderColor="White" HeightRequest="66" Margin="25" VerticalOptions="End" Clicked="btnContinue_Clicked" FontSize="17" />
        </StackLayout>
    </Grid>
</ContentPage>