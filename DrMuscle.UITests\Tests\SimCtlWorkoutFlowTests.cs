using NUnit.Framework;
using DrMuscle.UITests.Helpers;
using System;
using System.IO;
using System.Threading;

namespace DrMuscle.UITests.Tests
{
    /// <summary>
    /// Tests for complete workout flows using SimCtl
    /// </summary>
    [TestFixture]
    public class SimCtlWorkoutFlowTests
    {
        private string screenshotDir = string.Empty;
        private TestUser testUser = null!;
        
        [OneTimeSetUp]
        public void OneTimeSetup()
        {
            if (!SimCtlTestRunner.IsAvailable())
            {
                Assert.Ignore("xcrun simctl is not available");
            }
        }
        
        [SetUp]
        public void Setup()
        {
            testUser = TestDataHelper.CreateTestUser();
            var testRunId = TestDataHelper.GenerateTestRunId();
            screenshotDir = Path.Combine(Directory.GetCurrentDirectory(), "Screenshots", "WorkoutFlow", testRunId);
            Directory.CreateDirectory(screenshotDir);
            
            TestContext.WriteLine($"Test run ID: {testRunId}");
            TestContext.WriteLine($"Using test user: {testUser.Email}");
        }
        
        [Test]
        [Order(1)]
        [FlakyTestRetry(3)]
        [UserJourney(TestCategories.WorkoutJourney, TestCategories.WorkoutStart)]
        [Description("Tests the complete happy path flow of starting and finishing a workout")]
        public void CompleteWorkoutHappyPath()
        {
            TestContext.WriteLine("=== Testing Complete Workout Happy Path ===");
            
            var workout = WorkoutTestData.GetDefaultWorkout();
            var expectedDuration = WorkoutTestData.CalculateExpectedDuration(workout);
            var expectedVolume = WorkoutTestData.CalculateTotalVolume(workout);
            
            TestContext.WriteLine($"Expected workout duration: {expectedDuration / 1000}s");
            TestContext.WriteLine($"Expected total volume: {expectedVolume} lbs");
            
            // 1. Launch and capture initial state
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(TestTimings.AppLaunch);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "01-app-launched.png"));
            
            // 2. Simulate login flow timing
            TestContext.WriteLine("Simulating login flow...");
            Thread.Sleep(TestTimings.LoginFlow);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "02-logged-in.png"));
            
            // 3. Navigate to home and start workout
            TestContext.WriteLine("Starting workout...");
            Thread.Sleep(TestTimings.ScreenTransition);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "03-home-screen.png"));
            
            Thread.Sleep(TestTimings.WorkoutStart);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "04-workout-started.png"));
            
            // 4. Complete each exercise
            int exerciseNumber = 1;
            foreach (var exercise in workout.Exercises)
            {
                TestContext.WriteLine($"Simulating exercise {exerciseNumber}: {exercise.Name}");
                
                // Capture exercise start
                Thread.Sleep(TestTimings.ScreenTransition);
                SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, $"05-exercise-{exerciseNumber:D2}-start.png"));
                
                // Complete each set
                for (int set = 1; set <= exercise.Sets; set++)
                {
                    TestContext.WriteLine($"  Set {set}/{exercise.Sets}");
                    
                    // Simulate set execution
                    Thread.Sleep(TestTimings.SetExecution);
                    SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, $"06-exercise-{exerciseNumber:D2}-set-{set:D2}-complete.png"));
                    
                    // Rest period (except after last set)
                    if (set < exercise.Sets)
                    {
                        Thread.Sleep(TestTimings.RestPeriod);
                        SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, $"07-exercise-{exerciseNumber:D2}-rest-{set:D2}.png"));
                    }
                }
                
                // Exercise complete
                SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, $"08-exercise-{exerciseNumber:D2}-complete.png"));
                exerciseNumber++;
            }
            
            // 5. Capture workout summary
            TestContext.WriteLine("Capturing workout summary...");
            Thread.Sleep(TestTimings.ScreenTransition);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "09-workout-summary.png"));
            
            // 6. Verify workout saved
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "10-workout-saved.png"));
            
            // Verify screenshots were created
            var screenshots = Directory.GetFiles(screenshotDir, "*.png");
            Assert.That(screenshots.Length, Is.GreaterThan(10), "Should have captured multiple screenshots");
            
            TestContext.WriteLine($"✅ Workout completed successfully with {screenshots.Length} screenshots");
        }
        
        [Test]
        [Order(2)]
        [FlakyTestRetry(2)]
        [UserJourney(TestCategories.WorkoutJourney, TestCategories.WorkoutStart)]
        [Description("Tests starting a workout and then abandoning it before completion")]
        public void StartAndAbandonWorkout()
        {
            TestContext.WriteLine("=== Testing Workout Abandonment ===");
            
            // Launch and login
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(TestTimings.AppLaunch);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "abandon-01-launched.png"));
            
            Thread.Sleep(TestTimings.LoginFlow);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "abandon-02-logged-in.png"));
            
            // Start workout
            Thread.Sleep(TestTimings.WorkoutStart);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "abandon-03-workout-started.png"));
            
            // Complete one set
            Thread.Sleep(TestTimings.SetExecution);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "abandon-04-one-set-done.png"));
            
            // Simulate back navigation (abandon)
            TestContext.WriteLine("Simulating workout abandonment...");
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "abandon-05-confirmation.png"));
            
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "abandon-06-back-home.png"));
            
            Assert.Pass("Workout abandonment flow completed");
        }
        
        [Test]
        [Order(3)]
        [FlakyTestRetry(2)]
        [UserJourney(TestCategories.WorkoutJourney, TestCategories.WorkoutStart)]
        [Description("Tests completing a quick single-exercise workout")]
        public void QuickWorkoutFlow()
        {
            TestContext.WriteLine("=== Testing Quick Workout ===");
            
            var quickWorkout = WorkoutTestData.GetQuickWorkout();
            
            // Launch and setup
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(TestTimings.AppLaunch);
            Thread.Sleep(TestTimings.LoginFlow);
            
            // Start quick workout
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "quick-01-start.png"));
            
            // Complete the single exercise
            var exercise = quickWorkout.Exercises[0];
            TestContext.WriteLine($"Completing {exercise.Name}: {exercise.Sets} sets");
            
            for (int set = 1; set <= exercise.Sets; set++)
            {
                Thread.Sleep(TestTimings.SetExecution);
                SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, $"quick-02-set-{set}.png"));
                
                if (set < exercise.Sets)
                {
                    Thread.Sleep(TestTimings.RestPeriod);
                }
            }
            
            // Complete workout
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "quick-03-complete.png"));
            
            Assert.Pass("Quick workout completed successfully");
        }
        
        [Test]
        [Order(4)]
        [UserJourney(TestCategories.WorkoutJourney, TestCategories.WorkoutStart)]
        [Description("Captures screenshots of all workout screen states for documentation")]
        public void CaptureWorkoutScreenStates()
        {
            TestContext.WriteLine("=== Capturing All Workout Screen States ===");
            
            // This test focuses on documenting UI states
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(TestTimings.AppLaunch);
            
            // Document each screen state with appropriate delays
            var screenStates = new[]
            {
                ("launch-screen", TestTimings.UIElementRender),
                ("login-screen", TestTimings.AuthScreenLoad),
                ("home-tab", TestTimings.UIElementRender),
                ("workout-selection", TestTimings.UIElementRender),
                ("exercise-overview", TestTimings.ScreenTransition),
                ("weight-picker", TestTimings.UIElementRender),
                ("rep-picker", TestTimings.UIElementRender),
                ("rest-timer-start", TestTimings.UIElementRender),
                ("rest-timer-mid", TestTimings.RestPeriod),
                ("rest-timer-end", TestTimings.UIElementRender),
                ("exercise-complete", TestTimings.UIElementRender),
                ("workout-summary", TestTimings.ScreenTransition),
                ("achievements", TestTimings.UIElementRender),
                ("history-updated", TestTimings.UIElementRender)
            };
            
            foreach (var (state, delay) in screenStates)
            {
                TestContext.WriteLine($"Capturing {state}...");
                Thread.Sleep(delay);
                SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, $"state-{state}.png"));
            }
            
            var capturedStates = Directory.GetFiles(screenshotDir, "state-*.png");
            Assert.That(capturedStates.Length, Is.EqualTo(screenStates.Length), 
                "All workout screen states should be captured");
        }
        
        [TearDown]
        public void TearDown()
        {
            // Log screenshot directory for reference
            TestContext.WriteLine($"Screenshots saved to: {screenshotDir}");
            
            var screenshots = Directory.GetFiles(screenshotDir, "*.png");
            TestContext.WriteLine($"Total screenshots captured: {screenshots.Length}");
        }
    }
}