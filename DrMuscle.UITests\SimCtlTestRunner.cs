using System;
using System.Diagnostics;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using DrMuscle.UITests.Helpers;

namespace DrMuscle.UITests
{
    /// <summary>
    /// Alternative test runner using xcrun simctl commands when Appium is not available
    /// </summary>
    public static class SimCtlTestRunner
    {
        private static string? _deviceId;
        private static string? _bundleId = "com.drmaxmuscle.max";
        
        public static bool IsAvailable()
        {
            try
            {
                var result = RunCommand("xcrun", "simctl list devices booted");
                return !string.IsNullOrEmpty(result);
            }
            catch
            {
                return false;
            }
        }
        
        public static string GetBootedDeviceId()
        {
            if (_deviceId != null) return _deviceId;
            
            var output = RunCommand("xcrun", "simctl list devices booted");
            var lines = output.Split('\n', StringSplitOptions.RemoveEmptyEntries);
            
            foreach (var line in lines)
            {
                if (line.Contains("(Booted)") && line.Contains("iPhone"))
                {
                    // Extract device ID from line like: iPhone 14 (ABC123...) (Booted)
                    var start = line.IndexOf('(');
                    var end = line.IndexOf(')', start);
                    if (start > 0 && end > start)
                    {
                        _deviceId = line.Substring(start + 1, end - start - 1);
                        return _deviceId;
                    }
                }
            }
            
            throw new InvalidOperationException("No booted iOS device found");
        }
        
        public static void LaunchApp(string? bundleId = null)
        {
            bundleId ??= _bundleId;
            var deviceId = GetBootedDeviceId();
            
            // Terminate app if running
            RunCommand("xcrun", $"simctl terminate {deviceId} {bundleId}", throwOnError: false);
            Thread.Sleep(TestTimings.AppTerminate);
            
            // Launch app
            RunCommand("xcrun", $"simctl launch {deviceId} {bundleId}");
            Thread.Sleep(TestTimings.AppLaunch);
        }
        
        public static void TerminateApp(string? bundleId = null)
        {
            bundleId ??= _bundleId;
            var deviceId = GetBootedDeviceId();
            
            // Terminate app
            RunCommand("xcrun", $"simctl terminate {deviceId} {bundleId}", throwOnError: false);
            Thread.Sleep(TestTimings.AppTerminate);
        }
        
        public static void InstallApp(string appPath)
        {
            var deviceId = GetBootedDeviceId();
            Console.WriteLine($"[SimCtlTestRunner] Installing app from: {appPath}");
            Console.WriteLine($"[SimCtlTestRunner] Target device: {deviceId}");
            
            if (!File.Exists(appPath) && !Directory.Exists(appPath))
            {
                Console.WriteLine($"[SimCtlTestRunner] WARNING: App bundle not found at: {appPath}");
                throw new FileNotFoundException($"App bundle not found at: {appPath}");
            }
            
            try
            {
                RunCommand("xcrun", $"simctl install {deviceId} \"{appPath}\"");
                Console.WriteLine($"[SimCtlTestRunner] App installed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[SimCtlTestRunner] Failed to install app: {ex.Message}");
                throw;
            }
        }
        
        public static bool IsAppInstalled(string? bundleId = null)
        {
            bundleId ??= _bundleId;
            if (string.IsNullOrEmpty(bundleId)) return false;
            
            var deviceId = GetBootedDeviceId();
            var output = RunCommand("xcrun", $"simctl listapps {deviceId}");
            return output.Contains(bundleId);
        }
        
        public static void TakeScreenshot(string outputPath)
        {
            var deviceId = GetBootedDeviceId();
            RunCommand("xcrun", $"simctl io {deviceId} screenshot \"{outputPath}\"");
        }
        
        public static string GetAppState(string? bundleId = null)
        {
            bundleId ??= _bundleId;
            var deviceId = GetBootedDeviceId();
            var output = RunCommand("xcrun", $"simctl launch {deviceId} {bundleId} --console", throwOnError: false);
            return output;
        }
        
        public static void OpenUrl(string url)
        {
            var deviceId = GetBootedDeviceId();
            RunCommand("xcrun", $"simctl openurl {deviceId} \"{url}\"");
        }
        
        public static void SendPushNotification(string payload)
        {
            var deviceId = GetBootedDeviceId();
            var tempFile = Path.GetTempFileName();
            File.WriteAllText(tempFile, payload);
            
            try
            {
                RunCommand("xcrun", $"simctl push {deviceId} {_bundleId} \"{tempFile}\"");
            }
            finally
            {
                File.Delete(tempFile);
            }
        }
        
        private static string RunCommand(string command, string arguments, bool throwOnError = true)
        {
            using var process = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = command,
                    Arguments = arguments,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                }
            };
            
            process.Start();
            var output = process.StandardOutput.ReadToEnd();
            var error = process.StandardError.ReadToEnd();
            process.WaitForExit();
            
            if (throwOnError && process.ExitCode != 0)
            {
                throw new InvalidOperationException($"Command failed: {command} {arguments}\nError: {error}");
            }
            
            return output;
        }
    }
}