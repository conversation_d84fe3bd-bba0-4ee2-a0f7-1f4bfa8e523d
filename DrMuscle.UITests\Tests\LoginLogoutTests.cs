using NUnit.Framework;
using OpenQA.Selenium;
using OpenQA.Selenium.Support.UI;
using DrMuscle.UITests.Helpers;
using System;
using System.Threading;

namespace DrMuscle.UITests.Tests
{
    [TestFixture]
    [User<PERSON><PERSON><PERSON>(TestCategories.ReturningUser)]
    [Description("Tests for user login and logout functionality")]
    public class LoginLogoutTests : AppiumSetup
    {
        private string? _testEmail;
        private string? _testPassword;
        
        [OneTimeSetUp]
        public new void OneTimeSetup()
        {
            // Generate a test account that will be reused for login/logout tests
            // In a real scenario, you might want to create this account first
            var account = TestAccount.GenerateWithSeed("loginlogout");
            _testEmail = account.email;
            _testPassword = account.password;
            
            Console.WriteLine($"Using test account: {_testEmail}");
            
            // Call base setup
            base.OneTimeSetup();
        }
        
        [Test, Order(1)]
        [Description("Test logging in with an existing account")]
        public void LoginWithExistingAccount()
        {
            // Wait for app to load
            Thread.Sleep(3000);
            TakeScreenshot("01-welcome-screen");
            
            // Enter email
            var emailField = Driver?.FindElement(MobileBy.AccessibilityId("EmailEntry"));
            if (emailField == null)
            {
                emailField = Driver?.FindElement(By.XPath("//XCUIElementTypeTextField[contains(@value,'email')]"));
            }
            Assert.That(emailField, Is.Not.Null, "Email field should be found");
            emailField?.Clear();
            emailField?.SendKeys(_testEmail);
            
            // Enter password
            var passwordField = Driver?.FindElement(MobileBy.AccessibilityId("PasswordEntry"));
            if (passwordField == null)
            {
                passwordField = Driver?.FindElement(By.XPath("//XCUIElementTypeSecureTextField[contains(@value,'password')]"));
            }
            Assert.That(passwordField, Is.Not.Null, "Password field should be found");
            passwordField?.Clear();
            passwordField?.SendKeys(_testPassword);
            
            TakeScreenshot("02-filled-login-form");
            
            // Tap login button
            var loginButton = Driver?.FindElement(MobileBy.AccessibilityId("LoginButton"));
            if (loginButton == null)
            {
                loginButton = Driver?.FindElement(By.XPath("//XCUIElementTypeStaticText[@name='Log in']"));
            }
            Assert.That(loginButton, Is.Not.Null, "Login button should be found");
            loginButton?.Click();
            
            // Wait for login to complete
            Thread.Sleep(5000);
            TakeScreenshot("03-after-login");
            
            // Verify login success by checking for elements that appear after login
            // This will depend on your app's post-login UI
            var pageSource = Driver?.PageSource;
            Assert.That(pageSource, Is.Not.Null.And.Not.Empty, "Should have page content after login");
            
            Console.WriteLine("Login completed successfully");
        }
        
        [Test, Order(2)]
        [Description("Test logging out from the app")]
        public void LogoutFromApp()
        {
            // This test assumes we're already logged in from the previous test
            Thread.Sleep(2000);
            TakeScreenshot("01-logged-in-state");
            
            // Look for menu or settings button to access logout
            // This will vary based on your app's UI structure
            // Common patterns:
            // - Hamburger menu button
            // - Profile/Settings tab
            // - Three dots menu
            
            // Try to find a menu button (adjust selector based on your app)
            var menuButton = Driver?.FindElement(By.XPath("//XCUIElementTypeButton[@name='Menu']"));
            if (menuButton == null)
            {
                // Try other common patterns
                menuButton = Driver?.FindElement(By.XPath("//XCUIElementTypeButton[contains(@name,'menu')]"));
            }
            
            if (menuButton != null)
            {
                menuButton.Click();
                Thread.Sleep(2000);
                TakeScreenshot("02-menu-opened");
                
                // Look for logout option
                var logoutButton = Driver?.FindElement(By.XPath("//XCUIElementTypeStaticText[contains(@name,'Log out') or contains(@name,'Logout') or contains(@name,'Sign out')]"));
                if (logoutButton != null)
                {
                    logoutButton.Click();
                    Thread.Sleep(3000);
                    TakeScreenshot("03-after-logout");
                    
                    // Verify we're back at welcome screen
                    var welcomeTitle = Driver?.FindElement(MobileBy.AccessibilityId("WelcomeTitle"));
                    Assert.That(welcomeTitle, Is.Not.Null, "Should be back at welcome screen after logout");
                }
                else
                {
                    Assert.Inconclusive("Could not find logout button. App may have different navigation.");
                }
            }
            else
            {
                Assert.Inconclusive("Could not find menu button. App may have different navigation structure.");
            }
        }
        
        [Test, Order(3)]
        [Description("Test logging back in after logging out")]
        public void LoginAfterLogout()
        {
            // This test verifies we can log back in after logging out
            Thread.Sleep(2000);
            
            // Reuse the login logic from the first test
            var emailField = Driver?.FindElement(MobileBy.AccessibilityId("EmailEntry"));
            Assert.That(emailField, Is.Not.Null, "Email field should be available after logout");
            emailField?.Clear();
            emailField?.SendKeys(_testEmail);
            
            var passwordField = Driver?.FindElement(MobileBy.AccessibilityId("PasswordEntry"));
            Assert.That(passwordField, Is.Not.Null, "Password field should be available after logout");
            passwordField?.Clear();
            passwordField?.SendKeys(_testPassword);
            
            var loginButton = Driver?.FindElement(MobileBy.AccessibilityId("LoginButton"));
            Assert.That(loginButton, Is.Not.Null, "Login button should be available after logout");
            loginButton?.Click();
            
            Thread.Sleep(5000);
            TakeScreenshot("relogin-success");
            
            Assert.Pass("Successfully logged back in after logout");
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private void TakeScreenshot(string name)
        {
            TakeScreenshot(Driver, name);
        }
        #pragma warning restore CA1822 // Mark members as static
        
        private static void TakeScreenshot(AppiumDriver? driver, string name)
        {
            try
            {
                var screenshot = driver?.GetScreenshot();
                if (screenshot != null)
                {
                    var screenshotPath = Path.Combine(
                        TestContext.CurrentContext.WorkDirectory, 
                        $"{TestContext.CurrentContext.Test.Name}_{name}.png"
                    );
                    screenshot.SaveAsFile(screenshotPath);
                    TestContext.AddTestAttachment(screenshotPath, name);
                    Console.WriteLine($"Screenshot saved: {name}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to take screenshot {name}: {ex.Message}");
            }
        }
    }
}