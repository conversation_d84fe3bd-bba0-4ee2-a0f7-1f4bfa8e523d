using OpenQA.Selenium;
using OpenQA.Selenium.Appium;
using OpenQA.Selenium.Support.UI;
using System;

namespace DrMuscle.UITests.Pages
{
    /// <summary>
    /// Page object for Settings screens
    /// </summary>
    public class SettingsPage
    {
        private readonly AppiumDriver? _driver;
        private readonly WebDriverWait _wait;
        
        public SettingsPage(AppiumDriver driver)
        {
            _driver = driver ?? throw new ArgumentNullException(nameof(driver));
            _wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
        }
        
        // Navigation elements
        private AppiumElement? BackButton => FindElement(MobileBy.AccessibilityId("BackButton"));
        private AppiumElement? SaveButton => FindElement(MobileBy.AccessibilityId("SaveSettings"));
        
        // Training mode elements
        private AppiumElement? TrainingModeSection => FindElement(MobileBy.AccessibilityId("TrainingModeSection"));
        private AppiumElement? MuscleBuilidingOption => FindElement(MobileBy.AccessibilityId("MuscleBuildingMode"));
        private AppiumElement? StrengthOption => FindElement(MobileBy.AccessibilityId("StrengthMode"));
        private AppiumElement? PowerBuildingOption => FindElement(MobileBy.AccessibilityId("PowerBuildingMode"));
        
        // Timer settings elements
        private AppiumElement? TimerSettingsButton => FindElement(MobileBy.AccessibilityId("TimerSettings"));
        private AppiumElement? AutoStartTimerSwitch => FindElement(MobileBy.AccessibilityId("AutoStartTimer"));
        private AppiumElement? DefaultRestTimeInput => FindElement(MobileBy.AccessibilityId("DefaultRestTime"));
        private AppiumElement? ExerciseRestTimeList => FindElement(MobileBy.AccessibilityId("ExerciseRestTimes"));
        private AppiumElement? AddCustomRestButton => FindElement(MobileBy.AccessibilityId("AddCustomRest"));
        
        // Timer notification settings
        private AppiumElement? TimerSoundSwitch => FindElement(MobileBy.AccessibilityId("TimerSound"));
        private AppiumElement? TimerVibrationSwitch => FindElement(MobileBy.AccessibilityId("TimerVibration"));
        private AppiumElement? TimerNotificationSwitch => FindElement(MobileBy.AccessibilityId("TimerNotification"));
        private AppiumElement? NotificationTimeInput => FindElement(MobileBy.AccessibilityId("NotificationTime"));
        
        // Custom rest time dialog
        private AppiumElement? ExerciseSearchInput => FindElement(MobileBy.AccessibilityId("ExerciseSearch"));
        private AppiumElement? RestTimeInput => FindElement(MobileBy.AccessibilityId("CustomRestTimeInput"));
        private AppiumElement? ConfirmCustomRestButton => FindElement(MobileBy.AccessibilityId("ConfirmCustomRest"));
        
        // Actions
        public void SelectTrainingMode(string mode)
        {
            switch (mode.ToLower())
            {
                case "muscle building":
                case "muscle":
                    MuscleBuilidingOption?.Click();
                    break;
                case "strength":
                    StrengthOption?.Click();
                    break;
                case "powerbuilding":
                case "power building":
                    PowerBuildingOption?.Click();
                    break;
                default:
                    throw new ArgumentException($"Unknown training mode: {mode}");
            }
        }
        
        public void OpenTimerSettings()
        {
            TimerSettingsButton?.Click();
            _wait.Until(d => AutoStartTimerSwitch != null || DefaultRestTimeInput != null);
        }
        
        public void SetAutoStartTimer(bool enabled)
        {
            if (AutoStartTimerSwitch != null)
            {
                var currentState = AutoStartTimerSwitch.GetAttribute("value") == "1";
                if (currentState != enabled)
                {
                    AutoStartTimerSwitch.Click();
                }
            }
        }
        
        public void EnableTimerSound(bool enabled)
        {
            SetSwitchState(TimerSoundSwitch, enabled);
        }
        
        public void EnableTimerVibration(bool enabled)
        {
            SetSwitchState(TimerVibrationSwitch, enabled);
        }
        
        public void EnableTimerNotification(bool enabled)
        {
            SetSwitchState(TimerNotificationSwitch, enabled);
        }
        
        public void SetNotificationTime(string seconds)
        {
            NotificationTimeInput?.Clear();
            NotificationTimeInput?.SendKeys(seconds);
        }
        
        public void SetDefaultRestTime(string seconds)
        {
            DefaultRestTimeInput?.Clear();
            DefaultRestTimeInput?.SendKeys(seconds);
        }
        
        public void SelectExerciseForCustomRest(string exerciseName)
        {
            AddCustomRestButton?.Click();
            Thread.Sleep(500);
            
            ExerciseSearchInput?.Clear();
            ExerciseSearchInput?.SendKeys(exerciseName);
            Thread.Sleep(500);
            
            // Select first matching exercise
            var exerciseOption = FindElement(MobileBy.XPath($"//XCUIElementTypeCell[contains(@name, '{exerciseName}')]"));
            exerciseOption?.Click();
        }
        
        public void SetCustomRestTime(string seconds)
        {
            RestTimeInput?.Clear();
            RestTimeInput?.SendKeys(seconds);
            
            ConfirmCustomRestButton?.Click();
        }
        
        public void SaveTimerSettings()
        {
            SaveButton?.Click();
            Thread.Sleep(500);
        }
        
        public void NavigateBack()
        {
            BackButton?.Click();
        }
        
        public void SetSupersetTransitionTime(string seconds)
        {
            var transitionTimeInput = FindElement(MobileBy.AccessibilityId("SupersetTransitionTime"));
            transitionTimeInput?.Clear();
            transitionTimeInput?.SendKeys(seconds);
        }
        
        public void SetSupersetRoundRestTime(string seconds)
        {
            var roundRestInput = FindElement(MobileBy.AccessibilityId("SupersetRoundRestTime"));
            roundRestInput?.Clear();
            roundRestInput?.SendKeys(seconds);
        }
        
        // Verifications
        public bool IsTimerSettingsOpen()
        {
            return AutoStartTimerSwitch != null && AutoStartTimerSwitch.Displayed;
        }
        
        public string GetCurrentTrainingMode()
        {
            if (MuscleBuilidingOption?.GetAttribute("selected") == "true")
                return "Muscle Building";
            if (StrengthOption?.GetAttribute("selected") == "true")
                return "Strength";
            if (PowerBuildingOption?.GetAttribute("selected") == "true")
                return "PowerBuilding";
            
            return "";
        }
        
        public bool IsAutoStartEnabled()
        {
            return AutoStartTimerSwitch?.GetAttribute("value") == "1";
        }
        
        // Helper methods
        private AppiumElement? FindElement(By by)
        {
            try
            {
                return _driver?.FindElement(by) as AppiumElement;
            }
            catch (NoSuchElementException)
            {
                return null;
            }
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private void SetSwitchState(AppiumElement? switchElement, bool enabled)
        #pragma warning restore CA1822 // Mark members as static
        {
            if (switchElement != null)
            {
                var currentState = switchElement.GetAttribute("value") == "1";
                if (currentState != enabled)
                {
                    switchElement.Click();
                }
            }
        }
    }
}