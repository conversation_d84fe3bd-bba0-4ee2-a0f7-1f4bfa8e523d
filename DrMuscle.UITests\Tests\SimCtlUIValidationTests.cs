using NUnit.Framework;
using System;
using System.IO;
using System.Threading;
using DrMuscle.UITests.Helpers;

namespace DrMuscle.UITests.Tests
{
    /// <summary>
    /// UI validation tests using SimCtl screenshots
    /// Since we can't interact with UI elements directly, we verify app states via screenshots
    /// </summary>
    [TestFixture]
    public class SimCtlUIValidationTests
    {
        private string _screenshotDir = null!;
        
        [OneTimeSetUp]
        public void Setup()
        {
            if (!SimCtlTestRunner.IsAvailable())
            {
                Assert.Ignore("xcrun simctl is not available");
            }
            
            // Ensure app is installed
            if (!SimCtlTestRunner.IsAppInstalled())
            {
                var appPath = Environment.GetEnvironmentVariable("IOS_APP_BUNDLE");
                if (!string.IsNullOrEmpty(appPath) && Directory.Exists(appPath))
                {
                    SimCtlTestRunner.InstallApp(appPath);
                }
                else
                {
                    Assert.Ignore("App is not installed and no app bundle path available");
                }
            }
            
            _screenshotDir = Path.Combine(TestContext.CurrentContext.WorkDirectory, "Screenshots");
            Directory.CreateDirectory(_screenshotDir);
        }
        
        [Test, Order(1)]
        [Retry(2)] // Welcome screen validation might need retry
        public void ValidateWelcomeScreenLaunch()
        {
            // Launch app fresh
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(5000); // Wait for app to fully load
            
            // Take screenshot of welcome screen
            var screenshotPath = Path.Combine(_screenshotDir, "welcome-screen.png");
            SimCtlTestRunner.TakeScreenshot(screenshotPath);
            
            Assert.That(File.Exists(screenshotPath), Is.True, "Welcome screen screenshot should exist");
            TestContext.AddTestAttachment(screenshotPath, "Welcome Screen");
            
            // Verify file size indicates a real screenshot (not blank/error)
            var fileInfo = new FileInfo(screenshotPath);
            Assert.That(fileInfo.Length, Is.GreaterThan(50000), "Screenshot should have reasonable size");
            
            Console.WriteLine($"Welcome screen screenshot captured: {fileInfo.Length} bytes");
        }
        
        [Test, Order(2)]
        public void CaptureMultipleAppStates()
        {
            // Ensure app is running
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(3000);
            
            // Capture initial state
            var screenshot1 = Path.Combine(_screenshotDir, "app-state-1.png");
            SimCtlTestRunner.TakeScreenshot(screenshot1);
            TestContext.AddTestAttachment(screenshot1, "App State 1");
            
            // Wait and capture again (app might show different content)
            Thread.Sleep(5000);
            var screenshot2 = Path.Combine(_screenshotDir, "app-state-2.png");
            SimCtlTestRunner.TakeScreenshot(screenshot2);
            TestContext.AddTestAttachment(screenshot2, "App State 2");
            
            // Verify both screenshots exist and have content
            Assert.That(File.Exists(screenshot1), Is.True);
            Assert.That(File.Exists(screenshot2), Is.True);
            
            var file1Size = new FileInfo(screenshot1).Length;
            var file2Size = new FileInfo(screenshot2).Length;
            
            Console.WriteLine($"Screenshot 1: {file1Size} bytes");
            Console.WriteLine($"Screenshot 2: {file2Size} bytes");
            
            // Check if screenshots are different (indicating app is responsive)
            if (Math.Abs(file1Size - file2Size) > 1000)
            {
                Console.WriteLine("Screenshots show different content - app appears responsive");
            }
        }
        
        [Test, Order(3)]
        public void ValidateAppRelaunch()
        {
            // Terminate app
            SimCtlTestRunner.LaunchApp(); // This terminates first, then launches
            Thread.Sleep(2000);
            
            // Capture state after relaunch
            var screenshotPath = Path.Combine(_screenshotDir, "app-relaunch.png");
            SimCtlTestRunner.TakeScreenshot(screenshotPath);
            TestContext.AddTestAttachment(screenshotPath, "App After Relaunch");
            
            Assert.That(File.Exists(screenshotPath), Is.True);
            Console.WriteLine("App successfully relaunched and screenshot captured");
        }
        
        [Test, Order(4)]
        public void CaptureAppLifecycle()
        {
            // Document app lifecycle with screenshots
            var lifecycleScreenshots = new[]
            {
                ("launch", 3000),
                ("loaded", 5000),
                ("idle", 3000)
            };
            
            SimCtlTestRunner.LaunchApp();
            
            foreach (var (stage, delay) in lifecycleScreenshots)
            {
                Thread.Sleep(delay);
                var screenshotPath = Path.Combine(_screenshotDir, $"lifecycle-{stage}.png");
                SimCtlTestRunner.TakeScreenshot(screenshotPath);
                
                if (File.Exists(screenshotPath))
                {
                    TestContext.AddTestAttachment(screenshotPath, $"Lifecycle: {stage}");
                    Console.WriteLine($"Captured lifecycle stage: {stage}");
                }
            }
            
            Assert.Pass("App lifecycle documented with screenshots");
        }
        
        [Test, Order(5)]
        public void ValidateSimulatorRotation()
        {
            // Note: SimCtl doesn't support programmatic rotation, but we can document current state
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(3000);
            
            // Capture current orientation
            var screenshotPath = Path.Combine(_screenshotDir, "current-orientation.png");
            SimCtlTestRunner.TakeScreenshot(screenshotPath);
            
            if (File.Exists(screenshotPath))
            {
                TestContext.AddTestAttachment(screenshotPath, "Current Orientation");
                var fileInfo = new FileInfo(screenshotPath);
                
                // Typical portrait dimensions result in taller images
                Console.WriteLine($"Screenshot captured: {fileInfo.Length} bytes");
                Console.WriteLine("Note: Manual rotation testing required for full validation");
            }
            
            Assert.Pass("Orientation screenshot captured");
        }
    }
}