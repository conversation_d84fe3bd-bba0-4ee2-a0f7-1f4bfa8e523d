# LLM Codegen Process Guide

> A repeatable process for using LLMs to generate production-ready code, based on <PERSON>'s workflows and our automated testing project experience.

## Overview

This guide combines proven LLM codegen workflows from [<PERSON>'s blog posts](https://harper.blog/2025/05/08/basic-claude-code/) with our practical experience implementing the DrMaxMuscle MAUI automated testing project. The result is a battle-tested, repeatable process for any software development project.

## Core Principles

1. **Discrete Loops**: Break everything into small, manageable chunks
2. **Test-Driven Development (TDD)**: The robots LOVE TDD - it prevents hallucination and scope drift
3. **Documentation First**: Always start with specs and plans
4. **Commit Early and Often**: Use git as your safety net
5. **Defensive Coding**: Linting, testing, and pre-commit hooks are your friends

## The Complete Process

### Phase 1: Idea Honing & Specification

#### Step 1.1: Interactive Brainstorming
Use a conversational LLM (ChatGPT 4o, Claude, o3) with this prompt:

```prompt
Ask me one question at a time so we can develop a thorough, step-by-step spec for this idea. Each question should build on my previous answers, and our end goal is to have a detailed specification I can hand off to a developer. Let's do this iteratively and dig into every relevant detail. Remember, only one question at a time.

Here's the idea:
<YOUR IDEA>
```

#### Step 1.2: Compile Specification
After brainstorming reaches natural conclusion:

```prompt
Now that we've wrapped up the brainstorming process, can you compile our findings into a comprehensive, developer-ready specification? Include all relevant requirements, architecture choices, data handling details, error handling strategies, and a testing plan so a developer can immediately begin implementation.
```

Save this as `spec.md` in your project root.

### Phase 2: Planning & Prompt Generation

#### Step 2.1: Generate Implementation Blueprint
Pass the spec to a reasoning model (o1, o3, r1, Claude 3.5 Sonnet) with this TDD-focused prompt:

```prompt
Draft a detailed, step-by-step blueprint for building this project. Then, once you have a solid plan, break it down into small, iterative chunks that build on each other. Look at these chunks and then go another round to break it into small steps. Review the results and make sure that the steps are small enough to be implemented safely with strong testing, but big enough to move the project forward. Iterate until you feel that the steps are right sized for this project.

From here you should have the foundation to provide a series of prompts for a code-generation LLM that will implement each step in a test-driven manner. Prioritize best practices, incremental progress, and early testing, ensuring no big jumps in complexity at any stage. Make sure that each prompt builds on the previous prompts, and ends with wiring things together. There should be no hanging or orphaned code that isn't integrated into a previous step.

Make sure and separate each prompt section. Use markdown. Each prompt should be tagged as text using code tags. The goal is to output prompts, but context, etc is important as well.

<SPEC>
[paste spec.md content here]
</SPEC>
```

Save this as `prompt_plan.md` in your project root.

### Phase 3: Execution with Claude Code (or Similar)

#### Step 3.1: The Magic Prompt
This is Harper Reed's battle-tested execution prompt for Claude Code:

```prompt
1. Open **@prompt_plan.md** and identify any prompts not marked as completed.
2. For each incomplete prompt:
    - Double-check if it's truly unfinished (if uncertain, ask for clarification).
    - If you confirm it's already done, skip it.
    - Otherwise, implement it as described.
    - Make sure the tests pass, and the program builds/runs
    - Commit the changes to your repository with a clear commit message.
    - Update **@prompt_plan.md** to mark this prompt as completed.
3. After you finish each prompt, pause and wait for user review or feedback.
4. Repeat with the next unfinished prompt as directed by the user.
```

#### Step 3.2: The Cookie Clicker Phase
- Type "continue" or "yes" as Claude works through the prompts
- Review each implementation before proceeding
- Use the downtime productively (listen to music, plan next project, etc.)

### Phase 4: Defensive Coding Setup

#### Step 4.1: Testing Infrastructure
- **Always use TDD**: Write tests first, then implementation
- **Mock before real**: Build mocks, then replace with real implementation
- **Test coverage**: Aim for >80% coverage on business logic

#### Step 4.2: Linting & Formatting
Set up your preferred tools:
- Python: `ruff`, `black`
- JavaScript/TypeScript: `biome`, `eslint`, `prettier`
- Rust: `clippy`, `rustfmt`
- C#/.NET: `dotnet format`, Roslyn analyzers

#### Step 4.3: Pre-commit Hooks
Install and configure pre-commit:
```bash
# Python projects
uv tools install pre-commit
# or
pip install pre-commit

# Create .pre-commit-config.yaml
pre-commit install
```

This prevents broken commits and keeps the robots honest.

### Phase 5: Project Organization

#### Step 5.1: Essential Files
```
project-root/
├── spec.md              # The specification from Phase 1
├── prompt_plan.md       # The prompt plan from Phase 2
├── CLAUDE.md           # Claude-specific instructions (optional)
├── docs/
│   ├── architecture.md  # Technical architecture
│   ├── status.md       # Current project status
│   └── testing.md      # Test documentation
└── .pre-commit-config.yaml
```

#### Step 5.2: CLAUDE.md Template
```markdown
# Project-Specific Instructions for Claude

## Code Style
- Use descriptive variable names
- Keep functions under 50 lines
- Always include docstrings/comments

## Testing
- Write tests FIRST (TDD)
- Each feature needs unit tests
- Integration tests for API endpoints

## Git Commits
- Use conventional commits (feat:, fix:, docs:, etc.)
- Keep commits atomic
- Reference issue numbers when applicable

## Project-Specific Rules
[Add your project-specific guidelines here]
```

## Workflow Variations

### For Legacy/Existing Codebases

1. **Start with understanding**: Have the LLM analyze existing code first
2. **Create a current-state spec**: Document what exists before planning changes
3. **Use smaller chunks**: Legacy code requires more careful, incremental changes
4. **Add tests first**: Before refactoring, ensure test coverage exists

### For Team Projects

1. **Branch strategy**: Each prompt/chunk on its own branch
2. **Review process**: Human review between each major chunk
3. **Documentation**: Update docs/ with each significant change
4. **Conflict resolution**: Plan for merge conflicts in prompt_plan.md

## Common Pitfalls & Solutions

### Problem: LLM Scope Drift
**Solution**: Strict TDD and small chunks prevent wandering implementations

### Problem: Package Management Confusion
**Solution**: Be explicit about package managers (npm, pip, uv, cargo, etc.)

### Problem: Accumulating Technical Debt
**Solution**: Regular refactoring prompts in your plan

### Problem: Lost Context
**Solution**: Always refer back to spec.md and maintain status.md

## Success Metrics

- **Speed**: Greenfield projects complete in 30-45 minutes
- **Quality**: >95% test pass rate
- **Coverage**: >80% code coverage
- **Commits**: Clean git history with atomic commits
- **Documentation**: Up-to-date spec, status, and architecture docs

## Example Projects

### Our MAUI Testing Project
- **Spec**: Comprehensive UI testing for DrMaxMuscle app
- **Plan**: 14 discrete prompts from setup to quality gates
- **Result**: Full test harness with CI/CD integration

### Harper's Basic Interpreter
- **Language**: C (which he didn't know!)
- **Time**: Working version in 1 hour
- **Process**: Same workflow produced production-ready code

## Tools & Resources

### Recommended LLMs
- **Brainstorming**: ChatGPT 4o, Claude 3.5 Sonnet
- **Planning**: o1-pro, o3, Claude 3.5 Sonnet
- **Execution**: Claude Code, Cursor, Continue.dev

### Supporting Tools
- **Version Control**: Git with conventional commits
- **Testing**: Language-specific (pytest, jest, cargo test, etc.)
- **CI/CD**: GitHub Actions, GitLab CI, CircleCI
- **Documentation**: Markdown, with consistent structure

## Conclusion

This process transforms LLM code generation from a chaotic experiment into a predictable, high-quality development workflow. The key is maintaining discipline around documentation, testing, and incremental progress.

Remember Harper's wisdom: "The robots LOVE TDD. Seriously. They eat it up."

## References

1. [Harper Reed - Basic Claude Code](https://harper.blog/2025/05/08/basic-claude-code/)
2. [Harper Reed - My LLM codegen workflow atm](https://harper.blog/2025/02/16/my-llm-codegen-workflow-atm/)
3. Our DrMaxMuscle MAUI Testing Implementation (this repo)

---

*"The code must flow."* - Harper Reed 