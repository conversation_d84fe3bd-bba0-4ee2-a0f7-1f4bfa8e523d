<<<<<<< Updated upstream:DrMuscleWatch/v0/DrMuscleWatch.WatchOSApp/DrMuscleWatch.WatchOSApp.csproj
﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">iPhoneSimulator</Platform>
    <ProjectTypeGuids>{A9A6EAC2-568B-4F3E-869D-32CBBA122DF2};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <ProjectGuid>{15C97B55-2876-44EC-B41A-DC9E32F9CF52}</ProjectGuid>
    <TemplateGuid>{baaaf399-9f1e-41ac-a256-56b2550b9b13}</TemplateGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>DrMuscleWatch.WatchOSApp</RootNamespace>
    <AssemblyName>DrMuscleWatchWatchOSApp</AssemblyName>
    <IPhoneResourcePrefix>Resources</IPhoneResourcePrefix>
    <MtouchEnableSGenConc>true</MtouchEnableSGenConc>
    <MtouchHttpClientHandler>NSUrlSessionHandler</MtouchHttpClientHandler>
    <RestoreProjectStyle>PackageReference</RestoreProjectStyle>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|iPhoneSimulator' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\iPhoneSimulator\Debug</OutputPath>
    <DefineConstants>DEBUG;</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodesignKey>iPhone Developer</CodesignKey>
    <MtouchDebug>true</MtouchDebug>
    <MtouchFastDev>true</MtouchFastDev>
    <MtouchLink>None</MtouchLink>
    <MtouchArch>i386</MtouchArch>
    <DeviceSpecificBuild>false</DeviceSpecificBuild>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|iPhone' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\iPhone\Release</OutputPath>
    <DefineConstants>
    </DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodesignKey>iPhone Distribution: Dr. Muscle (7AAXZ47995)</CodesignKey>
    <MtouchUseLlvm>true</MtouchUseLlvm>
    <MtouchFloat32>true</MtouchFloat32>
    <MtouchEnableBitcode>true</MtouchEnableBitcode>
    <CodesignEntitlements>Entitlements.plist</CodesignEntitlements>
    <MtouchLink>SdkOnly</MtouchLink>
    <MtouchArch>ARMv7k</MtouchArch>
    <CodesignProvision>Jignesh iOS Distribution Dr. Muscle Watch</CodesignProvision>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|iPhoneSimulator' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\iPhoneSimulator\Release</OutputPath>
    <DefineConstants>
    </DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodesignKey>iPhone Developer</CodesignKey>
    <MtouchLink>None</MtouchLink>
    <MtouchArch>i386</MtouchArch>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|iPhone' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\iPhone\Debug</OutputPath>
    <DefineConstants>DEBUG;</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodesignKey>iPhone Distribution: Dr. Muscle (7AAXZ47995)</CodesignKey>
    <DeviceSpecificBuild>true</DeviceSpecificBuild>
    <MtouchDebug>true</MtouchDebug>
    <MtouchFastDev>true</MtouchFastDev>
    <MtouchFloat32>true</MtouchFloat32>
    <CodesignEntitlements>Entitlements.plist</CodesignEntitlements>
    <MtouchLink>SdkOnly</MtouchLink>
    <MtouchArch>ARMv7k</MtouchArch>
    <IOSDebugOverWiFi>true</IOSDebugOverWiFi>
    <IOSDebuggerPort>10001</IOSDebuggerPort>
    <CodesignProvision>Jignesh iOS Distribution Dr. Muscle Watch</CodesignProvision>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="Xamarin.WatchOS" />
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\icon172.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\icon196.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\icon48.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\icon55.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\icon58.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\icon80.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\icon87.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\icon88.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\icon100.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\icon216.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\icon1024.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Resources\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Info.plist" />
    <None Include="Entitlements.plist" />
  </ItemGroup>
  <ItemGroup>
    <InterfaceDefinition Include="Interface.storyboard" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\DrMuscleWatch.WatchOSExtension\DrMuscleWatch.WatchOSExtension.csproj">
      <Name>DrMuscleWatch.WatchOSExtension</Name>
      <Project>{746A5706-CF13-47CB-93E1-A18E38A05383}</Project>
      <IsAppExtension>true</IsAppExtension>
      <IsWatchApp>false</IsWatchApp>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath)\Xamarin\WatchOS\Xamarin.WatchOS.App.CSharp.targets" />
=======
﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">iPhoneSimulator</Platform>
    <ProjectTypeGuids>{A9A6EAC2-568B-4F3E-869D-32CBBA122DF2};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <ProjectGuid>{15C97B55-2876-44EC-B41A-DC9E32F9CF52}</ProjectGuid>
    <TemplateGuid>{baaaf399-9f1e-41ac-a256-56b2550b9b13}</TemplateGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>DrMuscleWatch.WatchOSApp</RootNamespace>
    <AssemblyName>DrMuscleWatchWatchOSApp</AssemblyName>
    <IPhoneResourcePrefix>Resources</IPhoneResourcePrefix>
    <MtouchEnableSGenConc>true</MtouchEnableSGenConc>
    <MtouchHttpClientHandler>NSUrlSessionHandler</MtouchHttpClientHandler>
    <RestoreProjectStyle>PackageReference</RestoreProjectStyle>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|iPhoneSimulator' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\iPhoneSimulator\Debug</OutputPath>
    <DefineConstants>DEBUG;</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodesignKey>iPhone Developer</CodesignKey>
    <MtouchDebug>true</MtouchDebug>
    <MtouchFastDev>true</MtouchFastDev>
    <MtouchLink>None</MtouchLink>
    <MtouchArch>i386</MtouchArch>
    <DeviceSpecificBuild>false</DeviceSpecificBuild>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|iPhone' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\iPhone\Release</OutputPath>
    <DefineConstants>
    </DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodesignKey>iPhone Distribution: Dr. Muscle (7AAXZ47995)</CodesignKey>
    <MtouchUseLlvm>true</MtouchUseLlvm>
    <MtouchFloat32>true</MtouchFloat32>
    <MtouchEnableBitcode>true</MtouchEnableBitcode>
    <CodesignEntitlements>Entitlements.plist</CodesignEntitlements>
    <MtouchLink>SdkOnly</MtouchLink>
    <MtouchArch>ARMv7k</MtouchArch>
    <CodesignProvision>Jignesh iOS Distribution Dr. Muscle Watch</CodesignProvision>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|iPhoneSimulator' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\iPhoneSimulator\Release</OutputPath>
    <DefineConstants>
    </DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodesignKey>iPhone Developer</CodesignKey>
    <MtouchLink>None</MtouchLink>
    <MtouchArch>i386</MtouchArch>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|iPhone' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\iPhone\Debug</OutputPath>
    <DefineConstants>DEBUG;</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodesignKey>iPhone Distribution: Dr. Muscle (7AAXZ47995)</CodesignKey>
    <DeviceSpecificBuild>true</DeviceSpecificBuild>
    <MtouchDebug>true</MtouchDebug>
    <MtouchFastDev>true</MtouchFastDev>
    <MtouchFloat32>true</MtouchFloat32>
    <CodesignEntitlements>Entitlements.plist</CodesignEntitlements>
    <MtouchLink>SdkOnly</MtouchLink>
    <MtouchArch>ARMv7k</MtouchArch>
    <IOSDebugOverWiFi>true</IOSDebugOverWiFi>
    <IOSDebuggerPort>10001</IOSDebuggerPort>
    <CodesignProvision>Jignesh iOS Distribution Dr. Muscle Watch</CodesignProvision>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="Xamarin.WatchOS" />
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\icon172.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\icon196.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\icon48.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\icon55.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\icon58.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\icon80.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\icon87.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\icon88.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\icon100.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\icon216.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\icon1024.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Resources\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Info.plist" />
    <None Include="Entitlements.plist" />
  </ItemGroup>
  <ItemGroup>
    <InterfaceDefinition Include="Interface.storyboard" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\DrMuscleWatch.WatchOSExtension\DrMuscleWatch.WatchOSExtension.csproj">
      <Name>DrMuscleWatch.WatchOSExtension</Name>
      <Project>{746A5706-CF13-47CB-93E1-A18E38A05383}</Project>
      <IsAppExtension>true</IsAppExtension>
      <IsWatchApp>false</IsWatchApp>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath)\Xamarin\WatchOS\Xamarin.WatchOS.App.CSharp.targets" />
>>>>>>> Stashed changes:DrMuscleWatch/DrMuscleWatch.WatchOSApp/DrMuscleWatch.WatchOSApp.csproj
</Project>