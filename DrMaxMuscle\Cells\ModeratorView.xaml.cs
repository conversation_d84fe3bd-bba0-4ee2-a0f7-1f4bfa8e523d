using Acr.UserDialogs;
using CommunityToolkit.Maui.Alerts;
using CommunityToolkit.Maui.Core;
using DrMaxMuscle.Constants;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Message;
using DrMaxMuscle.Resx;
using DrMaxMuscle.Utility;
using DrMaxMuscle.Views;
using DrMuscleWebApiSharedModel;
using System.Threading.Tasks;

namespace DrMaxMuscle.Cells;

public partial class ModeratorView : ContentView
{
    public ModeratorView()
    {
        InitializeComponent();
    }


    protected override void OnBindingContextChanged()
    {
        base.OnBindingContextChanged();
        try
        {

            var message = (DrMaxMuscle.Helpers.Messages)this.BindingContext;
            if (message == null)
                return;
            //show share and report options because reply is from AI
            // uncomment code please
            ImageButtonShare.IsVisible = message.IsFromAI;
            ImageButtonReport.IsVisible = message.IsFromAI;
            if (message.UserId.ToLower().Equals("<EMAIL>"))
            {
                imgInProfilePic.IsVisible = true;
                FrmProfile.IsVisible = false;
                imgInProfilePic.Source = "icon_1.png";
                if (message.IsFromAI)
                {
                    imgInProfilePic.Source = "icon_1.png";
                    nameLabel.Text = "Dr. Muscle AI";
                }
                else
                {
                    imgInProfilePic.Source = "victoriaprofileround.png";// "adminprofile.png";
                    nameLabel.Text = "Victoria from Dr. Muscle";

                }

            }
            else
            {
                if (!string.IsNullOrEmpty(message.ProfileUrl) && message.ProfileUrl.ToLower().Contains("facebook") || message.ProfileUrl.ToLower().Contains("google"))
                {
                    imgInProfilePic.IsVisible = true;
                    FrmProfile.IsVisible = false;
                    imgInProfilePic.Source = message.ProfileUrl;
                }
                else
                {
                    imgInProfilePic.Source = null;
                    imgInProfilePic.IsVisible = false;
                    FrmProfile.IsVisible = true;

                    //LblProfileText.Text = message.Nickname.Length > 0 ? message.Nickname.Substring(0, 1).ToUpper() : "";
                    //Color backColor = AppThemeConstants.RandomColor;
                    //if (AppThemeConstants.ProfileColor.ContainsKey(message.UserId))
                    //{
                    //    FrmProfile.BackgroundColor = AppThemeConstants.ProfileColor[message.UserId];
                    //}
                    //else
                    //{
                    //    AppThemeConstants.ProfileColor.Add(message.UserId, backColor);
                    //    FrmProfile.BackgroundColor = backColor;
                    //}
                }
                if (message.IsFromAI)
                {
                    imgInProfilePic.IsVisible = true;
                    FrmProfile.IsVisible = false;
                    imgInProfilePic.Source = "icon_1.png";
                    nameLabel.Text = "Dr. Muscle AI";
                }
            }

        }
        catch (Exception ex)
        {

        }
    }
    async void Username_Tapped(object sender, EventArgs e)
    {
        var message = (DrMaxMuscle.Helpers.Messages)this.BindingContext;
        if (message == null)
            return;
        if (message.ChatType == ChannelType.Group)
            return;
        bool isMuted = App.MutedUserList.Contains(message.UserId);
        bool IsAdmin = LocalDBManager.Instance.GetDBSetting("email").Value.ToLower().Equals("<EMAIL>") || LocalDBManager.Instance.GetDBSetting("email").Value.ToLower().Equals("<EMAIL>");
        if (!message.UserId.ToLower().Equals("<EMAIL>") && IsAdmin)
        {

            ActionSheetConfig config = new ActionSheetConfig();
            config.AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray);
            config.Add($"Get {message.UserId}", async () =>
            {
                Clipboard.SetTextAsync(message.UserId);
                CancellationTokenSource cancellationTokenSource = new CancellationTokenSource();
                var toast = Toast.Make("Email id copied to clipboard", ToastDuration.Short, 14);

                await toast.Show(cancellationTokenSource.Token);
                //Plugin.Toast.CrossToastPopUp.Current.ShowToastMessage("Email id copied to clipboard", Plugin.Toast.Abstractions.ToastLength.Short);
            });
            config.Add(isMuted ? $"Unmute {message.UserId}" : $"Mute {message.UserId}", () =>
            {
                var unmuteUserMessage = new MuteUnmuteUserMessage();
                unmuteUserMessage.IsMuted = isMuted;
                unmuteUserMessage.UserId = message.UserId;
                MessagingCenter.Send(unmuteUserMessage, "MuteUnmuteUserMessage");
            });
            config.Add("Delete message", () =>
            {
                var deleteMessage = new DeleteChatMessage();
                deleteMessage.FullMessage = message;
                MessagingCenter.Send(deleteMessage, "DeleteChatMessage");

            });
            config.SetCancel(AppResources.Cancel, null);

            config.SetTitle(isMuted ? $"Get email or unmute {message.UserId}?" : $"Get email or mute {message.UserId}?");
            UserDialogs.Instance.ActionSheet(config);


        }
    }

    async void ImageButtonShare_Clicked(System.Object sender, System.EventArgs e)
    {
        await HelperClass.ShareApp("AI_Msg", "Share_AI_Msg", lblOutMessage.Text);
    }

    async void ImageButtonReport_Clicked(System.Object sender, System.EventArgs e)
    {
        try
        {
            var message = (DrMaxMuscle.Helpers.Messages)this.BindingContext;
            if (message == null || !message.IsFromAI)
                return;

            // Show report confirmation dialog
            var reportDialog = await HelperClass.DisplayCustomPopupForResult(
                "Report",
                "Offensive, harmful, or wrong?",
                "Report",
                "Cancel");

            if (reportDialog == PopupAction.OK) // User chose "Report"
            {
                await ReportAIContent(message);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error in ImageButtonReport_Clicked: {ex.Message}");
        }
    }

    private async Task ReportAIContent(DrMaxMuscle.Helpers.Messages message)
    {
        try
        {
            // Create report model
            var reportModel = new ReportModel()
            {
                MessageId = message.MessageId,
                MessageContent = message.Message,
                ReportReason = "User reported as offensive, harmful, or wrong",
                ReportType = "AI_CONTENT",
                CreatedAt = DateTime.UtcNow,
                IsProcessed = false
            };

            // Send report to API
            var result = await DrMuscleRestClient.Instance.ReportAIContent(reportModel);

            if (result != null && (bool)result?.api?.Result == true)
            {
                // Show success toast
                CancellationTokenSource cancellationTokenSource = new CancellationTokenSource();
                var toast = Toast.Make("Thanks! We'll review this.", ToastDuration.Short, 14);
                await toast.Show(cancellationTokenSource.Token);

                // Add confirmation message to chat
                DrMuscleRestClient.Instance.SendMessage(new ChatModel()
                {
                    ReceiverId = AppThemeConstants.ChatReceiverId,
                    Message = "Reported last answer by AI."
                });
            }
            else
            {
                // Show error message
                CancellationTokenSource cancellationTokenSource = new CancellationTokenSource();
                var toast = Toast.Make("Failed to submit report. Please try again.", ToastDuration.Short, 14);
                await toast.Show(cancellationTokenSource.Token);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error in ReportAIContent: {ex.Message}");

            // Show error message
            CancellationTokenSource cancellationTokenSource = new CancellationTokenSource();
            var toast = Toast.Make("Failed to submit report. Please try again.", ToastDuration.Short, 14);
            await toast.Show(cancellationTokenSource.Token);
        }
    }
}
