# Dr. Muscle MAUI Mobile App - Architecture

## Purpose

This document describes the architecture for the Dr. Muscle cross-platform mobile application built with .NET MAUI, targeting iOS and Android platforms.

## Architecture Overview

The application is a **cross-platform MAUI app**, built using **.NET 8** and **MAUI framework**. It communicates with the Dr. Muscle backend API for user authentication, workout data synchronization, and progress tracking. The app follows a hybrid architecture pattern combining dependency injection, custom navigation, and extensive local data persistence.

## Key Components & Patterns

1.  **UI Layer (MAUI XAML + Custom Controls):**
    *   User interface built using MAUI XAML with extensive custom control library (`DrMuscleEntry`, `DrMuscleButton`, `DrMusclePage`)
    *   Platform-specific renderers for iOS and Android in `Platforms/` directories
    *   Tabbed navigation using `MainTabbedPage` with 3 main sections: Home, Progress, AI Chat
    *   Custom navigation system via `PagesFactory` pattern for page management and caching
    *   Popup management using `RGPopup.Maui` for overlays and modal dialogs

2.  **Data Flow & State Management:**
    *   Singleton pattern for data managers (`LocalDBManager`, `DrMuscleRestClient`)
    *   Local data persistence using SQLite with custom wrapper (`LocalDBManager.Instance`)
    *   Application-wide state management through `App.xaml.cs` with context objects
    *   Messaging patterns using `MessagingCenter` for cross-component communication
    *   No formal MVVM framework - direct code-behind patterns with business logic separation

3.  **API Client & Authentication:**
    *   Centralized REST client (`DrMuscleRestClient`) handling all backend communication
    *   OAuth 2.0-style authentication with JWT token management
    *   Support for multiple authentication methods:
        *   Email/password login
        *   Google OAuth integration
        *   Apple Sign In (iOS only)
        *   Facebook authentication (legacy)
    *   Automatic token refresh and session management
    *   Comprehensive API error handling and user feedback

4.  **Local Storage & Data Persistence:**
    *   SQLite database via `LocalDBManager` singleton for key-value storage
    *   Three main data tables: `DBSetting`, `DBReco`, `DBExercise1RM`
    *   Extensive user preference and session data caching
    *   Workout context persistence for offline functionality
    *   Platform-specific SQLite implementations (`SQLite_Android`, `SQLite_iOS`)

5.  **Dependency Injection & Services:**
    *   MAUI dependency injection container in `MauiProgram.cs`
    *   Platform-specific service registration for iOS and Android
    *   Key services include:
        *   `ISQLite` - Database connectivity
        *   `IFirebase` - Analytics and crash reporting
        *   `IAlarmAndNotificationService` - Local notifications
        *   `IAppleSignInService` - iOS authentication
        *   `IDrMuscleSubcription` - In-app purchases

6.  **Navigation Architecture:**
    *   Custom `PagesFactory` pattern for page lifecycle management
    *   `MainTabbedPage` as root container with lazy-loaded tabs
    *   `NoAnimationNavigationPage` for performance optimization
    *   Page caching and reuse strategy
    *   Custom navigation behaviors and lifecycle hooks (`OnBeforeShow`, `OnShow`)

7.  **Platform Integration:**
    *   Platform-specific implementations in `Platforms/iOS` and `Platforms/Android`
    *   Custom renderers for enhanced UI components
    *   Native platform services (notifications, file system, device info)
    *   HealthKit integration (iOS) for workout tracking
    *   Firebase integration for analytics and push notifications

8.  **Testing Strategy:**
    *   Unit testing approach: Focus on business logic in service layers and data managers
    *   Integration testing: Test API client interactions and local data persistence
    *   UI testing: Planned automated UI tests using MAUI UITest framework targeting critical user flows
    *   Manual testing: Extensive platform-specific testing on iOS and Android devices

## Dependencies

*   **.NET 8** - Runtime and base framework
*   **Microsoft.Maui** - Cross-platform framework
*   **SQLite** - Local database (`sqlite-net-pcl`)
*   **Newtonsoft.Json** - JSON serialization
*   **CommunityToolkit.Maui** - Enhanced MAUI controls
*   **RGPopup.Maui** - Popup management
*   **Acr.UserDialogs** - Native dialogs
*   **Sentry.Maui** - Error tracking
*   **Plugin.Firebase** - Analytics and authentication
*   **FFImageLoading.Maui** - Image optimization
*   **Microcharts.Maui** - Data visualization
*   **OxyPlot.Maui.Skia** - Advanced charting

## Architecture Patterns & Conventions

*   **Page Lifecycle**: All pages inherit from `DrMusclePage` with standardized lifecycle hooks
*   **Data Access**: Singleton `LocalDBManager` for all local data operations
*   **API Communication**: Single `DrMuscleRestClient` instance with centralized error handling
*   **Authentication Flow**: Token-based system with automatic refresh and secure storage
*   **Navigation Pattern**: Factory-based page management with caching and reuse
*   **Configuration**: Environment-based configuration with production/staging API endpoints
*   **Error Handling**: Comprehensive exception management with user-friendly messaging

## Key Architectural Decisions

*   **Hybrid Authentication**: Supports both traditional and social login methods
*   **Extensive Local Storage**: Heavy reliance on SQLite for offline functionality
*   **Custom Navigation**: Factory pattern chosen over built-in Shell navigation for greater control
*   **Platform-Specific Services**: Dependency injection used for platform abstractions
*   **Performance Optimization**: Page caching, lazy loading, and optimized image handling
*   **Backwards Compatibility**: Support for legacy user data and migration patterns

## Open Questions / Future Considerations

*   **API Response Caching**: Consider implementing response caching for improved offline experience
*   **State Management**: Evaluate migration to formal MVVM pattern (MVVM Community Toolkit)
*   **Performance Monitoring**: Enhanced performance metrics and monitoring integration
*   **Code Quality**: Integration of static analysis tools and automated testing
*   **Accessibility**: Comprehensive accessibility support across platforms
*   **Modularization**: Consider breaking down large service classes into smaller, focused components