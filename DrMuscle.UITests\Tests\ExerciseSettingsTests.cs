using NUnit.Framework;
using DrMuscle.UITests.Helpers;
using DrMuscle.UITests.Pages;
using System;
using System.Threading;
using System.Linq;
using OpenQA.Selenium.Appium;

namespace DrMuscle.UITests.Tests
{
    /// <summary>
    /// Comprehensive tests for Exercise Settings & Modifications
    /// Tests exercise settings, substitutions, disabling exercises, and custom exercise creation
    /// </summary>
    [TestFixture]
    [Category(TestCategories.AdvancedFeatures)]
    public class ExerciseSettingsTests : AppiumSetup
    {
        private WorkoutPage workoutPage = null!;
        private ExerciseSettingsPage exerciseSettingsPage = null!;
        
        [SetUp]
        public void TestSetup()
        {
            workoutPage = new WorkoutPage(Driver!);
            exerciseSettingsPage = new ExerciseSettingsPage(Driver!);
            
            // Login and start workout
            LoginAndStartWorkout();
        }
        
        [Test]
        [Order(1)]
        [Description("Tests modifying exercise settings")]
        public void TEST_Modify_Exercise_Settings()
        {
            TestContext.WriteLine("=== Testing Exercise Settings Modification ===");
            
            // Navigate to exercise
            workoutPage.SelectExerciseByName("Bench Press");
            Thread.Sleep(2000);
            
            // Long-press to access settings
            exerciseSettingsPage.LongPressExercise("Bench Press");
            Thread.Sleep(1000);
            
            Assert.That(exerciseSettingsPage.IsSettingsMenuVisible(), Is.True,
                "Settings menu should appear on long press");
            
            TakeScreenshot("01-exercise-settings-menu");
            
            // Open exercise settings
            exerciseSettingsPage.OpenExerciseSettings();
            Thread.Sleep(1000);
            
            // Test modifying sets count
            TestContext.WriteLine("Modifying sets count...");
            var originalSets = exerciseSettingsPage.GetSetsCount();
            TestContext.WriteLine($"Original sets: {originalSets}");
            
            exerciseSettingsPage.SetSetsCount("5");
            Thread.Sleep(500);
            
            // Test modifying rep ranges
            TestContext.WriteLine("Modifying rep range...");
            exerciseSettingsPage.SetMinReps("6");
            exerciseSettingsPage.SetMaxReps("10");
            Thread.Sleep(500);
            
            TakeScreenshot("02-modified-sets-reps");
            
            // Test modifying rest time
            TestContext.WriteLine("Modifying rest time...");
            exerciseSettingsPage.SetRestTime("120"); // 2 minutes
            Thread.Sleep(500);
            
            // Test adding tempo
            TestContext.WriteLine("Adding tempo...");
            exerciseSettingsPage.SetTempo("3-1-2-1"); // 3 sec eccentric, 1 pause, 2 concentric, 1 pause
            Thread.Sleep(500);
            
            // Test adding notes
            TestContext.WriteLine("Adding notes...");
            exerciseSettingsPage.AddNotes("Focus on controlled eccentric, pause at bottom");
            Thread.Sleep(500);
            
            TakeScreenshot("03-all-settings-modified");
            
            // Save settings
            exerciseSettingsPage.SaveSettings();
            Thread.Sleep(1000);
            
            // Verify changes apply immediately
            var updatedSets = workoutPage.GetTotalSets();
            var updatedReps = workoutPage.GetTargetReps();
            
            TestContext.WriteLine($"Updated sets: {updatedSets}");
            TestContext.WriteLine($"Updated target reps: {updatedReps}");
            
            Assert.That(updatedSets, Does.Contain("5"),
                "Sets count should be updated");
            Assert.That(updatedReps, Does.Contain("6-10") | Does.Contain("8"),
                "Rep range should be updated");
            
            // Start set and verify tempo indicator
            if (exerciseSettingsPage.HasTempoIndicator())
            {
                var tempoDisplay = exerciseSettingsPage.GetTempoDisplay();
                TestContext.WriteLine($"Tempo display: {tempoDisplay}");
                TakeScreenshot("04-tempo-indicator");
            }
            
            // Complete set and verify custom rest time
            workoutPage.EnterSet("8", "135");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            var restTime = new TimerPage(Driver!).GetRemainingTime();
            TestContext.WriteLine($"Custom rest time: {restTime}");
            
            TakeScreenshot("05-custom-rest-applied");
        }
        
        [Test]
        [Order(2)]
        [Description("Tests exercise substitution functionality")]
        public void TEST_Exercise_Substitution()
        {
            TestContext.WriteLine("=== Testing Exercise Substitution ===");
            
            // Select an exercise
            workoutPage.SelectExerciseByName("Barbell Row");
            Thread.Sleep(2000);
            
            // Access substitution option
            exerciseSettingsPage.OpenSubstituteOption();
            Thread.Sleep(1000);
            
            Assert.That(exerciseSettingsPage.IsSubstitutionMenuVisible(), Is.True,
                "Substitution menu should be visible");
            
            TakeScreenshot("06-substitution-menu");
            
            // Browse alternatives
            var alternatives = exerciseSettingsPage.GetAlternativeExercises();
            TestContext.WriteLine($"Available alternatives: {string.Join(", ", alternatives.Take(5))}");
            
            Assert.That(alternatives.Count, Is.GreaterThan(0),
                "Should have alternative exercises available");
            
            // Filter by equipment
            exerciseSettingsPage.FilterByEquipment("Dumbbell");
            Thread.Sleep(500);
            
            var dumbbellAlternatives = exerciseSettingsPage.GetAlternativeExercises();
            TestContext.WriteLine($"Dumbbell alternatives: {string.Join(", ", dumbbellAlternatives.Take(3))}");
            
            Assert.That(dumbbellAlternatives.All(ex => ex.Contains("Dumbbell") || ex.Contains("DB")),
                "Filtered exercises should be dumbbell-based");
            
            TakeScreenshot("07-equipment-filtered");
            
            // Select similar exercise
            exerciseSettingsPage.SelectAlternative("Dumbbell Row");
            Thread.Sleep(1000);
            
            // Verify maintains program logic
            var substituteInfo = exerciseSettingsPage.GetSubstitutionInfo();
            TestContext.WriteLine($"Substitution info: {substituteInfo}");
            
            Assert.That(substituteInfo, Does.Contain("similar") | Does.Contain("replacement"),
                "Should indicate this is a substitution");
            
            TakeScreenshot("08-exercise-substituted");
            
            // Verify sets/reps maintained
            var maintainedSets = workoutPage.GetTotalSets();
            var maintainedReps = workoutPage.GetTargetReps();
            
            TestContext.WriteLine($"Maintained sets: {maintainedSets}");
            TestContext.WriteLine($"Maintained reps: {maintainedReps}");
            
            // Complete a set with substitute
            workoutPage.EnterSet("10", "50");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            // Test equipment-based substitution
            TestContext.WriteLine("Testing equipment-based substitution...");
            exerciseSettingsPage.OpenSubstituteOption();
            exerciseSettingsPage.SelectSubstitutionReason("No Equipment");
            Thread.Sleep(500);
            
            var noEquipmentAlternatives = exerciseSettingsPage.GetAlternativeExercises();
            TestContext.WriteLine($"No-equipment alternatives: {string.Join(", ", noEquipmentAlternatives.Take(3))}");
            
            TakeScreenshot("09-no-equipment-alternatives");
        }
        
        [Test]
        [Order(3)]
        [Description("Tests disabling exercises")]
        public void TEST_Disable_Exercise()
        {
            TestContext.WriteLine("=== Testing Exercise Disable Functionality ===");
            
            // Navigate to exercise management
            exerciseSettingsPage.OpenExerciseLibrary();
            Thread.Sleep(2000);
            
            // Search for specific exercise
            exerciseSettingsPage.SearchExercise("Deadlift");
            Thread.Sleep(1000);
            
            TakeScreenshot("10-exercise-library");
            
            // Disable exercise
            exerciseSettingsPage.DisableExercise("Deadlift");
            Thread.Sleep(1000);
            
            // Select reason (injury/limitation)
            exerciseSettingsPage.SelectDisableReason("Lower Back Injury");
            exerciseSettingsPage.ConfirmDisable();
            Thread.Sleep(1000);
            
            TakeScreenshot("11-exercise-disabled");
            
            // Verify removed from workouts
            workoutPage.NavigateToHome();
            workoutPage.StartWorkout();
            Thread.Sleep(2000);
            
            var exerciseList = workoutPage.GetExerciseList();
            Assert.That(exerciseList.Any(ex => ex.Contains("Deadlift")), Is.False,
                "Disabled exercise should not appear in workouts");
            
            // Check disabled exercises section
            exerciseSettingsPage.OpenExerciseLibrary();
            exerciseSettingsPage.ShowDisabledExercises();
            Thread.Sleep(1000);
            
            var disabledList = exerciseSettingsPage.GetDisabledExercises();
            TestContext.WriteLine($"Disabled exercises: {string.Join(", ", disabledList)}");
            
            Assert.That(disabledList, Does.Contain("Deadlift"),
                "Deadlift should appear in disabled list");
            
            TakeScreenshot("12-disabled-list");
            
            // Test re-enabling
            TestContext.WriteLine("Testing re-enable...");
            exerciseSettingsPage.EnableExercise("Deadlift");
            Thread.Sleep(1000);
            
            // Verify history maintained
            exerciseSettingsPage.ViewExerciseHistory("Deadlift");
            Thread.Sleep(1000);
            
            var historyInfo = exerciseSettingsPage.GetHistoryInfo();
            TestContext.WriteLine($"History info: {historyInfo}");
            
            Assert.That(historyInfo, Is.Not.Empty,
                "Exercise history should be maintained after re-enabling");
            
            TakeScreenshot("13-history-maintained");
        }
        
        [Test]
        [Order(4)]
        [Description("Tests custom exercise creation")]
        public void TEST_Custom_Exercise_Creation()
        {
            TestContext.WriteLine("=== Testing Custom Exercise Creation ===");
            
            // Open exercise library
            exerciseSettingsPage.OpenExerciseLibrary();
            Thread.Sleep(1000);
            
            // Search for non-existent exercise
            exerciseSettingsPage.SearchExercise("Custom Cable Twist");
            Thread.Sleep(1000);
            
            // Verify not found
            var searchResults = exerciseSettingsPage.GetSearchResults();
            Assert.That(searchResults.Count, Is.EqualTo(0),
                "Custom exercise should not exist yet");
            
            TakeScreenshot("14-exercise-not-found");
            
            // Create custom exercise
            exerciseSettingsPage.CreateCustomExercise();
            Thread.Sleep(1000);
            
            // Enter exercise details
            exerciseSettingsPage.SetExerciseName("Custom Cable Twist");
            exerciseSettingsPage.SetExerciseDescription("Rotational core exercise using cable machine");
            
            // Set muscle groups
            exerciseSettingsPage.SelectPrimaryMuscle("Abs");
            var secondaryMuscles = new[] { "Obliques", "Lower Back" };
            exerciseSettingsPage.SelectSecondaryMuscles(secondaryMuscles);
            Thread.Sleep(500);
            
            TakeScreenshot("15-custom-exercise-muscles");
            
            // Set equipment needed
            exerciseSettingsPage.SelectEquipment("Cable Machine");
            Thread.Sleep(500);
            
            // Set exercise type
            exerciseSettingsPage.SetExerciseType("Isolation");
            exerciseSettingsPage.SetMovementPattern("Rotation");
            
            // Add custom tags
            var tags = new[] { "Core", "Functional", "Athletic" };
            exerciseSettingsPage.AddTags(tags);
            
            TakeScreenshot("16-custom-exercise-details");
            
            // Save custom exercise
            exerciseSettingsPage.SaveCustomExercise();
            Thread.Sleep(1000);
            
            // Add to current workout
            exerciseSettingsPage.AddToCurrentWorkout();
            Thread.Sleep(1000);
            
            // Verify exercise added
            var currentExercises = workoutPage.GetExerciseList();
            Assert.That(currentExercises.Any(ex => ex.Contains("Custom Cable Twist")), Is.True,
                "Custom exercise should be added to workout");
            
            TakeScreenshot("17-custom-exercise-added");
            
            // Start custom exercise
            workoutPage.SelectExerciseByName("Custom Cable Twist");
            Thread.Sleep(2000);
            
            // Verify tracking works
            workoutPage.EnterSet("15", "50");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            var setInfo = workoutPage.GetLastSetDetails();
            TestContext.WriteLine($"Custom exercise set: {setInfo}");
            
            Assert.That(setInfo, Does.Contain("15") & Does.Contain("50"),
                "Custom exercise should track sets normally");
            
            TakeScreenshot("18-custom-exercise-tracking");
        }
        
        [Test]
        [Order(5)]
        [Description("Tests exercise notes and form cues")]
        public void TEST_Exercise_Notes_And_Cues()
        {
            TestContext.WriteLine("=== Testing Exercise Notes and Form Cues ===");
            
            workoutPage.SelectExerciseByName("Squat");
            Thread.Sleep(2000);
            
            // Add form cues
            exerciseSettingsPage.OpenFormCues();
            Thread.Sleep(1000);
            
            exerciseSettingsPage.AddFormCue("Keep chest up and core tight");
            exerciseSettingsPage.AddFormCue("Drive through heels");
            exerciseSettingsPage.AddFormCue("Knees tracking over toes");
            exerciseSettingsPage.SaveFormCues();
            Thread.Sleep(1000);
            
            TakeScreenshot("19-form-cues-added");
            
            // Verify cues display during exercise
            var displayedCues = exerciseSettingsPage.GetDisplayedFormCues();
            TestContext.WriteLine($"Form cues: {string.Join("; ", displayedCues)}");
            
            Assert.That(displayedCues.Count, Is.GreaterThan(0),
                "Form cues should be displayed during exercise");
            
            // Add personal notes
            exerciseSettingsPage.OpenPersonalNotes();
            exerciseSettingsPage.AddPersonalNote("Working on depth - aim for hip crease below knee");
            exerciseSettingsPage.AddPersonalNote("Last session felt strong at 225lbs");
            exerciseSettingsPage.SavePersonalNotes();
            Thread.Sleep(1000);
            
            TakeScreenshot("20-personal-notes");
            
            // Test note visibility settings
            exerciseSettingsPage.SetNoteVisibility("During Rest Only");
            Thread.Sleep(500);
            
            workoutPage.EnterSet("5", "225");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            // Verify notes appear during rest
            var restNotes = exerciseSettingsPage.GetRestPeriodNotes();
            Assert.That(restNotes, Is.Not.Empty,
                "Notes should appear during rest period");
            
            TakeScreenshot("21-notes-during-rest");
        }
        
        private void LoginAndStartWorkout()
        {
            Thread.Sleep(3000);
            
            workoutPage.WaitForStartWorkout();
            workoutPage.StartWorkout();
            Thread.Sleep(2000);
            
            workoutPage.WaitForExerciseList();
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private void TakeScreenshot(string name)
        #pragma warning restore CA1822 // Mark members as static
        {
            TestTimings.TakeScreenshot(Driver, name);
        }
    }
}