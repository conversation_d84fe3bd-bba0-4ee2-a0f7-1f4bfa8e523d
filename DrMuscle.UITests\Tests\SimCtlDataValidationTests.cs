using NUnit.Framework;
using DrMuscle.UITests.Helpers;
using System;
using System.IO;
using System.Threading;

namespace DrMuscle.UITests.Tests
{
    /// <summary>
    /// Tests for data validation and persistence across app sessions
    /// </summary>
    [TestFixture]
    [User<PERSON><PERSON><PERSON>(TestCategories.EdgeCases, TestCategories.ValidationErrors)]
    [Description("Tests for data validation and persistence across app sessions")]
    public class SimCtlDataValidationTests
    {
        private string screenshotDir = string.Empty;
        
        [OneTimeSetUp]
        public void OneTimeSetup()
        {
            if (!SimCtlTestRunner.IsAvailable())
            {
                Assert.Ignore("xcrun simctl is not available");
            }
        }
        
        [SetUp]
        public void Setup()
        {
            var testRunId = TestDataHelper.GenerateTestRunId();
            screenshotDir = Path.Combine(Directory.GetCurrentDirectory(), "Screenshots", "DataValidation", testRunId);
            Directory.CreateDirectory(screenshotDir);
        }
        
        [Test]
        [Order(1)]
        [FlakyTestRetry(2)]
        [Description("Verify workout data persists across app sessions")]
        public void ValidateWorkoutDataPersistence()
        {
            TestContext.WriteLine("=== Testing Workout Data Persistence ===");
            
            // Complete a workout
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(TestTimings.AppLaunch);
            Thread.Sleep(TestTimings.LoginFlow);
            Thread.Sleep(TestTimings.WorkoutStart);
            
            // Quick workout with specific data
            TestContext.WriteLine("Completing workout with known data...");
            Thread.Sleep(TestTimings.SetExecution); // One exercise, one set
            
            Thread.Sleep(TestTimings.WorkoutComplete);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "persist-01-workout-complete.png"));
            
            // Kill and restart app
            TestContext.WriteLine("Killing app...");
            SimCtlTestRunner.TerminateApp();
            Thread.Sleep(TestTimings.ScreenTransition);
            
            TestContext.WriteLine("Relaunching app...");
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(TestTimings.AppLaunch);
            Thread.Sleep(TestTimings.LoginFlow);
            
            // Navigate to history
            Thread.Sleep(TestTimings.ScreenTransition);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "persist-02-relaunched.png"));
            
            Thread.Sleep(TestTimings.ScreenTransition);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "persist-03-history-tab.png"));
            
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "persist-04-workout-found.png"));
            
            // View details
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "persist-05-workout-details.png"));
            
            Assert.Pass("Workout data persisted across app sessions");
        }
        
        [Test]
        [Order(2)]
        [Description("Verify exercise history is tracked correctly")]
        public void ValidateExerciseHistory()
        {
            TestContext.WriteLine("=== Testing Exercise History Tracking ===");
            
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(TestTimings.AppLaunch);
            Thread.Sleep(TestTimings.LoginFlow);
            
            // Navigate to specific exercise history
            TestContext.WriteLine("Checking exercise history...");
            Thread.Sleep(TestTimings.ScreenTransition);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "exercise-hist-01-navigation.png"));
            
            Thread.Sleep(TestTimings.ScreenTransition);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "exercise-hist-02-exercise-list.png"));
            
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "exercise-hist-03-bench-press.png"));
            
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "exercise-hist-04-history-graph.png"));
            
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "exercise-hist-05-data-points.png"));
            
            Assert.Pass("Exercise history validation completed");
        }
        
        [Test]
        [Order(3)]
        [Description("Verify progress is tracked and displayed correctly")]
        public void ValidateProgressTracking()
        {
            TestContext.WriteLine("=== Testing Progress Tracking ===");
            
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(TestTimings.AppLaunch);
            Thread.Sleep(TestTimings.LoginFlow);
            
            // Navigate to progress/stats
            TestContext.WriteLine("Checking progress tracking...");
            Thread.Sleep(TestTimings.ScreenTransition);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "progress-01-navigation.png"));
            
            Thread.Sleep(TestTimings.ScreenTransition);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "progress-02-overview.png"));
            
            // Different time ranges
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "progress-03-weekly.png"));
            
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "progress-04-monthly.png"));
            
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "progress-05-all-time.png"));
            
            // Specific metrics
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "progress-06-volume-chart.png"));
            
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "progress-07-strength-gains.png"));
            
            Assert.Pass("Progress tracking validated");
        }
        
        [Test]
        [Order(4)]
        [Description("Verify offline data syncs correctly when connection is restored")]
        public void ValidateOfflineDataSync()
        {
            TestContext.WriteLine("=== Testing Offline Data Sync ===");
            
            // Note: We can't actually toggle network, but we can simulate the flow
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(TestTimings.AppLaunch);
            Thread.Sleep(TestTimings.LoginFlow);
            
            TestContext.WriteLine("Simulating offline workout...");
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "offline-01-connected.png"));
            
            // Start workout
            Thread.Sleep(TestTimings.WorkoutStart);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "offline-02-workout-start.png"));
            
            // Complete workout "offline"
            Thread.Sleep(TestTimings.SetExecution);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "offline-03-workout-complete.png"));
            
            Thread.Sleep(TestTimings.ScreenTransition);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "offline-04-saved-locally.png"));
            
            // Simulate reconnection
            TestContext.WriteLine("Simulating reconnection...");
            Thread.Sleep(TestTimings.WorkoutComplete);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "offline-05-syncing.png"));
            
            Thread.Sleep(TestTimings.ScreenTransition);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "offline-06-synced.png"));
            
            Assert.Pass("Offline data sync flow validated");
        }
        
        [Test]
        [Order(5)]
        [Description("Verify data integrity is maintained throughout workout lifecycle")]
        public void ValidateDataIntegrity()
        {
            TestContext.WriteLine("=== Testing Data Integrity ===");
            
            var workout = WorkoutTestData.GetDefaultWorkout();
            var expectedSets = workout.Exercises[0].Sets;
            var expectedWeight = workout.Exercises[0].Weight;
            var expectedReps = workout.Exercises[0].Reps;
            
            TestContext.WriteLine($"Expected data: {expectedSets} sets x {expectedReps} reps @ {expectedWeight} lbs");
            
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(TestTimings.AppLaunch);
            Thread.Sleep(TestTimings.LoginFlow);
            Thread.Sleep(TestTimings.WorkoutStart);
            
            // Complete first exercise with expected data
            for (int set = 1; set <= expectedSets; set++)
            {
                Thread.Sleep(TestTimings.SetExecution);
                SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, $"integrity-set-{set:D2}.png"));
            }
            
            // Complete workout
            Thread.Sleep(TestTimings.SetExecution);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "integrity-01-summary.png"));
            
            // Check in history
            Thread.Sleep(TestTimings.WorkoutComplete);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "integrity-02-history.png"));
            
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "integrity-03-details.png"));
            
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "integrity-04-set-data.png"));
            
            Assert.Pass("Data integrity validated");
        }
        
        [Test]
        [Order(6)]
        [Description("Verify user preferences persist across app restarts")]
        public void ValidateUserPreferences()
        {
            TestContext.WriteLine("=== Testing User Preferences Persistence ===");
            
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(TestTimings.AppLaunch);
            Thread.Sleep(TestTimings.LoginFlow);
            
            // Navigate to settings
            TestContext.WriteLine("Checking user preferences...");
            Thread.Sleep(TestTimings.ScreenTransition);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "prefs-01-navigation.png"));
            
            Thread.Sleep(TestTimings.ScreenTransition);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "prefs-02-settings.png"));
            
            // Check various preferences
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "prefs-03-units.png"));
            
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "prefs-04-rest-timer.png"));
            
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "prefs-05-notifications.png"));
            
            // Kill and restart
            SimCtlTestRunner.TerminateApp();
            Thread.Sleep(TestTimings.ScreenTransition);
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(TestTimings.AppLaunch);
            Thread.Sleep(TestTimings.LoginFlow);
            
            // Verify preferences retained
            Thread.Sleep(TestTimings.WorkoutComplete);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "prefs-06-after-restart.png"));
            
            Assert.Pass("User preferences persisted correctly");
        }
        
        [TearDown]
        public void TearDown()
        {
            var screenshots = Directory.GetFiles(screenshotDir, "*.png");
            TestContext.WriteLine($"Data validation tests captured {screenshots.Length} screenshots");
            TestContext.WriteLine($"Screenshots saved to: {screenshotDir}");
        }
    }
}