﻿<?xml version="1.0" encoding="utf-8" ?>
<TabbedPage
    x:Class="DrMaxMuscle.MainTabbedPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:android="clr-namespace:Microsoft.Maui.Controls.PlatformConfiguration.AndroidSpecific;assembly=Microsoft.Maui.Controls"
    xmlns:history="clr-namespace:DrMaxMuscle.Screens.History"
    xmlns:ios="clr-namespace:Microsoft.Maui.Controls.PlatformConfiguration.iOSSpecific;assembly=Microsoft.Maui.Controls"
    xmlns:local="clr-namespace:DrMaxMuscle.Screens.User"
    xmlns:local1="clr-namespace:DrMaxMuscle"
    xmlns:navigation="clr-namespace:DrMaxMuscle.Layout"
    x:Name="bottomTabbar"
    android:TabbedPage.IsSwipePagingEnabled="False"
    android:TabbedPage.ToolbarPlacement="Bottom"
    BarTextColor="White"
    CurrentPageChanged="TabbedPage_CurrentPageChanged"
    NavigationPage.HasNavigationBar="False"
    SelectedTabColor="White"
    UnselectedTabColor="#AAAAAA">
    <!--  BarBackgroundColor="#195276"  -->
    <TabbedPage.Behaviors>
        <local1:ActivePageTabbedPageBehavior />
    </TabbedPage.Behaviors>
    <TabbedPage.BarBackground>
        <LinearGradientBrush EndPoint="1,0">
            <GradientStop Offset="0.0" Color="#0C2432" />
            <GradientStop Offset="1.0" Color="#195276" />
        </LinearGradientBrush>
    </TabbedPage.BarBackground>
    <NavigationPage
        
        
        x:Name="TabHome"
        AutomationId="HomeTab"
        Title="Home"
        BarTextColor="White"
        IconImageSource="home_tab">
        <x:Arguments>
            <local:MainAIPage />
        </x:Arguments>
    </NavigationPage>
    <NavigationPage
        x:Name="TabProgress"
        AutomationId="HistoryTab"
        Title="Progress"
        BarTextColor="White"
        IconImageSource="bodyweight_home">
        <x:Arguments>
            <history:HistortWeightPage />
        </x:Arguments>
    </NavigationPage>
    <navigation:NoAnimationNavigationPage
        x:Name="TabChat"
        AutomationId="ChatTab"
        Title="AI Chat"
        BarBackground="{DynamicResource NavigationBarGradientBrush}"
        BarTextColor="White"
        IconImageSource="chat_tab">

        <x:Arguments>
            <local:ChatPage Title="Chat">
                <ContentPage.Behaviors>
                    <local1:LazyContentPageBehavior ContentTemplate="{StaticResource ContentTemplate}" />
                </ContentPage.Behaviors>
                <ContentPage.Resources>
                    <ResourceDictionary>
                        <DataTemplate x:Key="ContentTemplate">
                            <local:ChatView />
                        </DataTemplate>
                    </ResourceDictionary>
                </ContentPage.Resources>
            </local:ChatPage>
        </x:Arguments>

        <navigation:NoAnimationNavigationPage.Resources>
            <ResourceDictionary>
                <LinearGradientBrush x:Key="NavigationBarGradientBrush" StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Offset="0.0" Color="#0C2432" />
                    <GradientStop Offset="1.0" Color="#195276" />
                </LinearGradientBrush>

                <Style TargetType="navigation:NoAnimationNavigationPage">
                    <Setter Property="BarBackground" Value="{DynamicResource NavigationBarGradientBrush}" />
                    <Setter Property="BarTextColor" Value="White" />
                </Style>

                <!--  DataTemplate for ChatView  -->
                <DataTemplate x:Key="ContentTemplate">
                    <local:ChatView />
                </DataTemplate>
            </ResourceDictionary>
        </navigation:NoAnimationNavigationPage.Resources>

       

        <navigation:NoAnimationNavigationPage.Behaviors>
            <local1:LazyNavigationPageBehavior ContentTemplate="{StaticResource ContentTemplate}" />
        </navigation:NoAnimationNavigationPage.Behaviors>
    </navigation:NoAnimationNavigationPage>


    <!--<navigation:NoAnimationNavigationPage
    x:Name="TabChat"
    Title="AI Chat"
    IconImageSource="chat_tab.png"
    BackgroundImageSource="nav.png"
    BarTextColor="White">
        <navigation:NoAnimationNavigationPage.Resources>
            <ResourceDictionary>
                <DataTemplate
                x:Key="ContentTemplate">
                    <local:ChatView />
                </DataTemplate>
            </ResourceDictionary>
        </navigation:NoAnimationNavigationPage.Resources>
        <x:Arguments>
            <local:ChatPage
            Title="Chat">
                <ContentPage.Behaviors>
                    <local1:LazyContentPageBehavior
                    ContentTemplate="{StaticResource ContentTemplate}" />
                </ContentPage.Behaviors>
                <ContentPage.Resources>
                    <ResourceDictionary>
                        <DataTemplate
                        x:Key="ContentTemplate">
                            <local:ChatView />
                        </DataTemplate>
                    </ResourceDictionary>
                </ContentPage.Resources>
            </local:ChatPage>
        </x:Arguments>
        <navigation:NoAnimationNavigationPage.Behaviors>
            <local1:LazyNavigationPageBehavior
            ContentTemplate="{StaticResource ContentTemplate}" />
        </navigation:NoAnimationNavigationPage.Behaviors>
    </navigation:NoAnimationNavigationPage>-->

    <NavigationPage
        x:Name="TabSettings"
        AutomationId="SettingsTab"
        Title="Settings"
        BarTextColor="White"
        IconImageSource="settings_tab">
        <x:Arguments>
            <local:SettingsPage />
        </x:Arguments>
    </NavigationPage>
</TabbedPage>