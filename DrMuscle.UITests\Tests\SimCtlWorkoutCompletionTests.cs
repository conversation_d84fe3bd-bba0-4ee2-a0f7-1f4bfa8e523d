using NUnit.Framework;
using DrMuscle.UITests.Helpers;
using System;
using System.IO;
using System.Threading;
using System.Linq;

namespace DrMuscle.UITests.Tests
{
    /// <summary>
    /// Tests for workout completion, summary, and data persistence
    /// </summary>
    [TestFixture]
    public class SimCtlWorkoutCompletionTests
    {
        private string screenshotDir = string.Empty;
        
        [OneTimeSetUp]
        public void OneTimeSetup()
        {
            if (!SimCtlTestRunner.IsAvailable())
            {
                Assert.Ignore("xcrun simctl is not available");
            }
        }
        
        [SetUp]
        public void Setup()
        {
            var testRunId = TestDataHelper.GenerateTestRunId();
            screenshotDir = Path.Combine(Directory.GetCurrentDirectory(), "Screenshots", "WorkoutCompletion", testRunId);
            Directory.CreateDirectory(screenshotDir);
        }
        
        [Test]
        [Order(1)]
        [FlakyTestRetry(2)]
        [UserJourney(TestCategories.WorkoutJourney, TestCategories.WorkoutCompletion)]
        [Description("Tests completing a full workout and viewing the summary screen")]
        public void CompleteFullWorkoutWithSummary()
        {
            TestContext.WriteLine("=== Testing Full Workout Completion ===");
            
            var workout = WorkoutTestData.GetDefaultWorkout();
            var startTime = DateTime.Now;
            
            // Launch and start workout
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(TestTimings.AppLaunch);
            Thread.Sleep(TestTimings.LoginFlow);
            Thread.Sleep(TestTimings.WorkoutStart);
            
            // Fast-forward through exercises (abbreviated for completion testing)
            foreach (var exercise in workout.Exercises)
            {
                TestContext.WriteLine($"Completing {exercise.Name}...");
                
                for (int set = 1; set <= exercise.Sets; set++)
                {
                    Thread.Sleep(TestTimings.SetExecution); // Abbreviated timing
                    if (set < exercise.Sets)
                    {
                        Thread.Sleep(TestTimings.RestPeriod); // Short rest
                    }
                }
                
                SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, $"exercise-{exercise.Name.Replace(" ", "-").ToLower()}-complete.png"));
            }
            
            // Workout completion flow
            TestContext.WriteLine("Completing workout...");
            Thread.Sleep(TestTimings.ScreenTransition);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "completion-01-final-exercise-done.png"));
            
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "completion-02-finish-button.png"));
            
            Thread.Sleep(TestTimings.ScreenTransition);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "completion-03-summary-loading.png"));
            
            // Capture summary details
            Thread.Sleep(TestTimings.WorkoutComplete);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "completion-04-summary-overview.png"));
            
            // Calculate actual duration
            var duration = DateTime.Now - startTime;
            TestContext.WriteLine($"Workout duration: {duration.TotalMinutes:F1} minutes");
            
            // Capture different summary sections
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "completion-05-summary-stats.png"));
            
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "completion-06-summary-volume.png"));
            
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "completion-07-summary-achievements.png"));
            
            Assert.Pass("Full workout completed with summary captured");
        }
        
        [Test]
        [Order(2)]
        [UserJourney(TestCategories.WorkoutJourney, TestCategories.WorkoutCompletion)]
        [Description("Validates workout metrics calculation and display")]
        public void WorkoutMetricsValidation()
        {
            TestContext.WriteLine("=== Testing Workout Metrics ===");
            
            var workout = WorkoutTestData.GetDefaultWorkout();
            var expectedVolume = WorkoutTestData.CalculateTotalVolume(workout);
            var totalSets = workout.Exercises.Sum(e => e.Sets);
            
            TestContext.WriteLine($"Expected metrics:");
            TestContext.WriteLine($"  Total Volume: {expectedVolume:N0} lbs");
            TestContext.WriteLine($"  Total Sets: {totalSets}");
            TestContext.WriteLine($"  Exercises: {workout.Exercises.Count}");
            
            // Quick workout completion
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(TestTimings.AppLaunch);
            Thread.Sleep(TestTimings.LoginFlow);
            Thread.Sleep(TestTimings.WorkoutStart);
            
            // Complete workout quickly
            foreach (var exercise in workout.Exercises)
            {
                for (int set = 1; set <= exercise.Sets; set++)
                {
                    Thread.Sleep(TestTimings.WorkoutComplete);
                }
            }
            
            // Navigate to summary
            Thread.Sleep(TestTimings.WorkoutComplete);
            
            // Capture metric displays
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "metrics-01-total-volume.png"));
            Thread.Sleep(TestTimings.UIElementRender);
            
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "metrics-02-sets-completed.png"));
            Thread.Sleep(TestTimings.UIElementRender);
            
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "metrics-03-exercise-count.png"));
            Thread.Sleep(TestTimings.UIElementRender);
            
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "metrics-04-duration.png"));
            Thread.Sleep(TestTimings.UIElementRender);
            
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "metrics-05-calories.png"));
            
            Assert.Pass("Workout metrics captured for validation");
        }
        
        [Test]
        [Order(3)]
        [UserJourney(TestCategories.WorkoutJourney, TestCategories.WorkoutCompletion)]
        [Description("Tests personal record detection and notification")]
        public void PersonalRecordDetection()
        {
            TestContext.WriteLine("=== Testing Personal Record Detection ===");
            
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(TestTimings.AppLaunch);
            Thread.Sleep(TestTimings.LoginFlow);
            Thread.Sleep(TestTimings.WorkoutStart);
            
            // Simulate PR-breaking performance
            TestContext.WriteLine("Simulating personal record...");
            
            // First exercise with heavier weight
            Thread.Sleep(TestTimings.SetExecution);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "pr-01-heavy-set.png"));
            
            Thread.Sleep(TestTimings.WorkoutComplete);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "pr-02-pr-indicator.png"));
            
            // Complete workout
            Thread.Sleep(TestTimings.SetExecution);
            
            // Check summary for PR notification
            Thread.Sleep(TestTimings.WorkoutComplete);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "pr-03-summary-with-pr.png"));
            
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "pr-04-pr-details.png"));
            
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "pr-05-share-option.png"));
            
            Assert.Pass("Personal record detection tested");
        }
        
        [Test]
        [Order(4)]
        [UserJourney(TestCategories.WorkoutJourney, TestCategories.WorkoutCompletion)]
        [Description("Verifies workout history is updated after completion")]
        public void WorkoutHistoryUpdate()
        {
            TestContext.WriteLine("=== Testing Workout History Update ===");
            
            // Complete a quick workout
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(TestTimings.AppLaunch);
            Thread.Sleep(TestTimings.LoginFlow);
            
            // Navigate to history first (before workout)
            TestContext.WriteLine("Capturing pre-workout history...");
            Thread.Sleep(TestTimings.ScreenTransition);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "history-01-before-workout.png"));
            
            // Start and complete quick workout
            Thread.Sleep(TestTimings.WorkoutStart);
            var quickWorkout = WorkoutTestData.GetQuickWorkout();
            
            foreach (var exercise in quickWorkout.Exercises)
            {
                for (int set = 1; set <= exercise.Sets; set++)
                {
                    Thread.Sleep(TestTimings.WorkoutComplete);
                }
            }
            
            // Complete workout
            Thread.Sleep(TestTimings.WorkoutComplete);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "history-02-workout-complete.png"));
            
            // Navigate to history
            TestContext.WriteLine("Checking history update...");
            Thread.Sleep(TestTimings.ScreenTransition);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "history-03-navigation.png"));
            
            Thread.Sleep(TestTimings.ScreenTransition);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "history-04-updated-list.png"));
            
            // View workout details
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "history-05-workout-details.png"));
            
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "history-06-exercise-breakdown.png"));
            
            Assert.Pass("Workout history update verified");
        }
        
        [Test]
        [Order(5)]
        [UserJourney(TestCategories.WorkoutJourney, TestCategories.WorkoutCompletion)]
        [Description("Tests adding and saving workout notes")]
        public void WorkoutNotes()
        {
            TestContext.WriteLine("=== Testing Workout Notes ===");
            
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(TestTimings.AppLaunch);
            Thread.Sleep(TestTimings.LoginFlow);
            Thread.Sleep(TestTimings.WorkoutStart);
            
            // Quick workout
            Thread.Sleep(TestTimings.SetExecution);
            
            // Complete and add notes
            TestContext.WriteLine("Adding workout notes...");
            Thread.Sleep(TestTimings.ScreenTransition);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "notes-01-complete-screen.png"));
            
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "notes-02-notes-field.png"));
            
            Thread.Sleep(TestTimings.WorkoutComplete); // Simulate typing
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "notes-03-notes-entered.png"));
            
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "notes-04-saved.png"));
            
            // View in history
            Thread.Sleep(TestTimings.WorkoutComplete);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "notes-05-in-history.png"));
            
            Assert.Pass("Workout notes functionality tested");
        }
        
        [Test]
        [Order(6)]
        [UserJourney(TestCategories.WorkoutJourney, TestCategories.WorkoutCompletion)]
        [Description("Tests sharing workout summary functionality")]
        public void ShareWorkoutSummary()
        {
            TestContext.WriteLine("=== Testing Workout Sharing ===");
            
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(TestTimings.AppLaunch);
            Thread.Sleep(TestTimings.LoginFlow);
            Thread.Sleep(TestTimings.WorkoutStart);
            
            // Quick workout
            Thread.Sleep(TestTimings.SetExecution);
            
            // Navigate to sharing
            TestContext.WriteLine("Testing share functionality...");
            Thread.Sleep(TestTimings.WorkoutComplete);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "share-01-summary.png"));
            
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "share-02-share-button.png"));
            
            Thread.Sleep(TestTimings.ScreenTransition);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "share-03-share-sheet.png"));
            
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "share-04-preview.png"));
            
            // Cancel share
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "share-05-cancelled.png"));
            
            Assert.Pass("Workout sharing tested");
        }
        
        [TearDown]
        public void TearDown()
        {
            var screenshots = Directory.GetFiles(screenshotDir, "*.png");
            TestContext.WriteLine($"Workout completion tests captured {screenshots.Length} screenshots");
            TestContext.WriteLine($"Screenshots saved to: {screenshotDir}");
        }
    }
}