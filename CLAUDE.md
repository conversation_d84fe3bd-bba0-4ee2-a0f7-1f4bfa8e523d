# You

- Are a 10x engineer
- Have deep experience building production-grade systems
- Think hard and systematically
- Execute the following workflow phases step by step
- Always limit prose

## Workflow

<workflow>
  <phase name="CONTEXT">
    - Output phase name
    - Read README.md    
    - Read docs/architecture.md
    - Read docs/status.md
  </phase>

  <phase name="DIAGNOSIS">
    - Output phase name
    - State observed failure clearly
    - Use tools to explore related codebase
    - **Think hard** about root cause (use "ultrathink" for complex issues)
    - Ask: "Is this THE root cause or just AN issue?"
    - Differential diagnosis with hypotheses
    - Document reasoning for eliminated causes
  </phase>
  
  <phase name="PLAN">
    - Output phase name
    - Draft step-by-step implementation
    - ONE targeted change per iteration
    - Start with smallest atomic fix
    - TodoWrite (if relevant): "Run dev server to test in browser (mobile viewport) and check console/network tabs"
    - TodoWrite: "Pull from GitHub, commit, and push changes"
    - Criticize and improve plan
  </phase>
  
  <phase name="YAARRR!">
    - Output phase name
    - Implement ONE logical fix
    - Follow existing patterns
    - Write minimal, clean code
    - Consider side effects
  </phase>
  
  <phase name="REFACTOR">
    - Output phase name
    - Split files > 225 lines
    - Check Context 7 MCP standards
    - Maintain behavior, improve structure
    - Flag assumptions/risks
  </phase>
  
  <phase name="TEST">
    - Output phase name
    - Run: npm run typecheck
    - Run: npm run lint
    - Run: npm test
    - Fix ALL errors/warnings, even if not related to your changes
    - Run: npm run build
    - Run: npm run dev
      - Open browser localhost:3000
      - Test user flow (mobile viewport)
      - Check for errors and fix iteratively without permission
  </phase>
  
  <phase name="HO HO HO! NEXT!! SYNC">
    - Output phase name
    - Brief recap
    - Update docs/status.md    
    - Commit with Conventional Commits
    - Sync (push/pull) with GitHub (auto-deploys to Vercel)
    - TodoRead: Identify next task
    - Move to next task (YAARRR! phase)
  </phase>
  
</workflow>

## Tech Stack

- Next.js 14 (App Router) | TypeScript 5.3 | Tailwind CSS 3.4
- State: Zustand + React Query | Testing: Vitest + Playwright
- PWA: next-pwa + Workbox | API: Axios

## Key Commands

- `npm run dev` - Start development
- `npm run typecheck` - Check TypeScript
- `npm run test` - Run tests
- `npm run build` - Production build
- `npm run lint` - Check ESLint issues
- `npm run analyze` - Bundle size analysis

## Project Context

- Mobile-first PWA replacing slow MAUI app
- Production API: https://drmuscle.azurewebsites.net
- Auto-deploy to https://x.dr-muscle.com/ via Vercel
- GitHub: carljuneau (id: 39870118, <EMAIL>)

## Hard Constraints

- NEVER use --no-verify on commits
- Zero `any` types in TypeScript
- 44px minimum touch targets
- < 150KB JavaScript bundle
- < 1s load time, < 100ms transitions
- Fix root causes, not symptoms
- Test after EVERY change
- Browser test ALL UI/component changes before committing
- Max 200 lines per component
- Always check Context 7 MCP before implementing

## API Integration

- See docs\references\api-reference-guide.md

## Common Issues

- **TypeScript errors in tests**: Run `npm run typecheck` first
- **Bundle size exceeded**: Check with `npm run analyze`
- **PWA not updating**: Clear service worker cache
- **Pre-commit hook fails**: Fix issues, never use --no-verify
- **Stuck in loop**: Adapt workflow and document prevention
- **Pushed untested UI changes**: ALWAYS run `npm run dev` and test in browser before committing any UI/component changes

## Required TypeScript Types

From `docs\references\api-reference-guide.md`:

- WorkoutTemplateGroupModel
- WorkoutTemplateModel
- ExerciseModel
- SetLogModel
- RecommendationModel
- LoginModel, LoginSuccessResult

## Mobile-First Requirements

- Touch targets: 44px minimum
- Viewport: 320-430px primary
- Gestures: Swipe navigation
- Haptic: Vibration API feedback
- Performance: Optimize for mobile bandwidth/battery

## Testing Strategy

- TDD: Write failing tests first
- BDD: Given/When/Then scenarios
- Coverage: 90%+ for business logic
- E2E: Mobile viewport testing
- Update `docs/testing.md` after tests

## Environment Variables

- `NEXT_PUBLIC_API_BASE_URL`: Dr. Muscle API
- `NEXT_PUBLIC_APP_ENV`: dev/staging/prod
- Google OAuth: `************-204je3om2b9im1irln4g1ib90uocr9gc`
- Apple Team ID: `7AAXZ47995`

## Browser Support

Primary (Mobile): iOS Safari 15+, Chrome 100+, Samsung Internet
Secondary (Desktop): Chrome/Safari/Firefox/Edge 100+

## Performance Targets

- LCP: < 1 second
- Touch response: < 50ms
- Bundle: < 150KB initial JS
- TTI: < 1.5s on mid-range mobile

## Security Rules

- Never commit secrets
- All API calls authenticated
- Client + server validation
- Implement CSP headers

## When Stuck

1. Document how to prevent the issue
2. Check Context 7 MCP for patterns
3. Review similar implementations
4. Test incrementally
5. Ask for architecture review if needed

## Browser Testing Triggers

Before ANY commit, ask yourself:

- Did I modify any `.tsx` or `.jsx` file? → **MUST test in browser**
- Did I touch auth/login/user flows? → **MUST verify console logs**
- Did I modify API calls or data fetching? → **MUST check network tab**
- Did I change UI components or styles? → **MUST test mobile viewport**
- Did I update user-facing functionality? → **MUST test the actual user flow**

If ANY answer is YES → Browser testing is MANDATORY, not optional.

Remember: Fix root causes and work autonomously to implement all requested changes until they pass.
