#!/bin/bash

# Generate Enhanced Test Summary for GitHub Actions
# Usage: ./generate-enhanced-test-summary.sh <trx-file> <screenshot-dir> <run-id>

TRX_FILE="$1"
SCREENSHOT_DIR="$2"
RUN_ID="$3"

if [ -z "$TRX_FILE" ] || [ -z "$SCREENSHOT_DIR" ] || [ -z "$RUN_ID" ]; then
    echo "Usage: $0 <trx-file> <screenshot-dir> <run-id>"
    exit 1
fi

if [ ! -f "$TRX_FILE" ]; then
    echo "Error: TRX file not found: $TRX_FILE"
    exit 1
fi

# Parse test results from TRX
parse_tests() {
    local category="$1"
    local pattern="$2"
    
    # Extract test results matching pattern
    xmllint --xpath "//ns:UnitTestResult[contains(translate(@testName, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '$pattern')]" \
        --nsclean "$TRX_FILE" 2>/dev/null || echo ""
}

# Count tests by status
count_tests() {
    local xml="$1"
    local status="$2"
    echo "$xml" | grep -c "outcome=\"$status\"" || echo "0"
}

# Generate summary header
cat << EOF
# 📱 DrMuscle UI Test Results
**Run ID:** $RUN_ID  
**Date:** $(date '+%Y-%m-%d %H:%M:%S')  

## 📊 Overall Summary
EOF

# Parse overall stats from TRX
TOTAL_TESTS=$(grep -o 'total="[0-9]*"' "$TRX_FILE" | head -1 | grep -o '[0-9]*' || echo "0")
PASSED_TESTS=$(grep -o 'passed="[0-9]*"' "$TRX_FILE" | head -1 | grep -o '[0-9]*' || echo "0")
FAILED_TESTS=$(grep -o 'failed="[0-9]*"' "$TRX_FILE" | head -1 | grep -o '[0-9]*' || echo "0")
# Count skipped and notExecuted tests
SKIPPED_TESTS=$(grep -o 'skipped="[0-9]*"' "$TRX_FILE" | head -1 | grep -o '[0-9]*' || echo "0")
NOT_EXECUTED=$(grep -o 'notExecuted="[0-9]*"' "$TRX_FILE" | head -1 | grep -o '[0-9]*' || echo "0")
# Total skipped includes both skipped and notExecuted
SKIPPED_TESTS=$((SKIPPED_TESTS + NOT_EXECUTED))

if [ "$TOTAL_TESTS" -gt 0 ]; then
    PASS_RATE=$(( PASSED_TESTS * 100 / TOTAL_TESTS ))
else
    PASS_RATE=0
fi

cat << EOF
**Total Tests:** $TOTAL_TESTS  
**✅ Passed:** $PASSED_TESTS ($PASS_RATE%)  
**❌ Failed:** $FAILED_TESTS  
**⏭️ Skipped:** $SKIPPED_TESTS  

## 🗺️ User Journey Test Coverage

EOF

# Function to process a journey category
process_journey() {
    local emoji="$1"
    local category="$2"
    local description="$3"
    local patterns="$4"
    
    echo "### $emoji $category"
    echo "*$description*"
    echo ""
    
    # Count tests for this category
    local category_tests=0
    local category_passed=0
    
    for pattern in $patterns; do
        local pattern_lower=$(echo "$pattern" | tr '[:upper:]' '[:lower:]')
        local tests=$(grep -i "testName=\"[^\"]*$pattern_lower" "$TRX_FILE" || true)
        if [ -n "$tests" ]; then
            local count=$(echo "$tests" | wc -l)
            local passed=$(echo "$tests" | grep -c 'outcome="Passed"' || echo "0")
            category_tests=$((category_tests + count))
            category_passed=$((category_passed + passed))
        fi
    done
    
    if [ "$category_tests" -gt 0 ]; then
        local pass_rate=$((category_passed * 100 / category_tests))
        echo "**Tests:** $category_passed/$category_tests passed (${pass_rate}%)  "
        
        # Count screenshots for this category
        local screenshot_count=0
        if [ -d "$SCREENSHOT_DIR" ]; then
            # Map category to folder name
            local folder_name=""
            case "$category" in
                "Pre-Installation") folder_name="01-pre-installation" ;;
                "First-Time User") folder_name="02-first-time-user" ;;
                "Returning User") folder_name="03-returning-user" ;;
                "Workout Journey") folder_name="04-workout-journey" ;;
                "Edge Cases") folder_name="05-edge-cases" ;;
            esac
            
            if [ -n "$folder_name" ] && [ -d "$SCREENSHOT_DIR/$folder_name" ]; then
                screenshot_count=$(find "$SCREENSHOT_DIR/$folder_name" -name "*.png" 2>/dev/null | wc -l || echo "0")
                echo "**📸 Screenshots:** $screenshot_count images captured  "
            fi
        fi
        
        echo ""
        
        # Show test details
        echo "<details>"
        echo "<summary>View Test Details</summary>"
        echo ""
        echo "| Test | Status | Duration |"
        echo "|------|--------|----------|"
        
        # List individual tests
        for pattern in $patterns; do
            local pattern_lower=$(echo "$pattern" | tr '[:upper:]' '[:lower:]')
            grep -i "testName=\"[^\"]*$pattern_lower" "$TRX_FILE" | while read -r line; do
                local test_name=$(echo "$line" | grep -o 'testName="[^"]*"' | cut -d'"' -f2)
                local outcome=$(echo "$line" | grep -o 'outcome="[^"]*"' | cut -d'"' -f2)
                local duration=$(echo "$line" | grep -o 'duration="[^"]*"' | cut -d'"' -f2 | cut -d'.' -f1)
                
                local status_emoji="❓"
                case "$outcome" in
                    "Passed") status_emoji="✅" ;;
                    "Failed") status_emoji="❌" ;;
                    "Skipped") status_emoji="⏭️" ;;
                esac
                
                # Shorten test name if too long
                if [ ${#test_name} -gt 40 ]; then
                    test_name="${test_name:0:37}..."
                fi
                
                echo "| $test_name | $status_emoji | ${duration}s |"
            done
        done
        
        echo ""
        echo "</details>"
        echo ""
    else
        echo "**Tests:** No tests found  "
        echo ""
    fi
}

# Process each journey category
process_journey "🔧" "Pre-Installation" "Build, setup, and environment verification" "infrastructure smoke basicinfrastructure"
process_journey "👋" "First-Time User" "New user's first experience with the app" "authentication simctlauthentication"
process_journey "🔄" "Returning User" "Existing user login and navigation" "loginlogout"
process_journey "💪" "Workout Journey" "Complete workout experience from start to finish" "workoutflow setrecording workoutcompletion"
process_journey "⚠️" "Edge Cases" "Error handling and edge case scenarios" "network edgecase datavalidation"

# Failed tests summary
if [ "$FAILED_TESTS" -gt 0 ]; then
    echo "## ❌ Failed Tests"
    echo ""
    
    grep 'outcome="Failed"' "$TRX_FILE" | while read -r line; do
        test_name=$(echo "$line" | grep -o 'testName="[^"]*"' | cut -d'"' -f2)
        echo "- $test_name"
    done
    echo ""
fi

# Screenshot gallery
echo "## 📸 Screenshot Summary"
echo ""
echo "Screenshots captured by journey stage:"
echo ""

if [ -d "$SCREENSHOT_DIR" ]; then
    total_screenshots=0
    for folder in "$SCREENSHOT_DIR"/*; do
        if [ -d "$folder" ]; then
            folder_name=$(basename "$folder")
            count=$(find "$folder" -name "*.png" 2>/dev/null | wc -l || echo "0")
            if [ "$count" -gt 0 ]; then
                # Map folder to category name
                category=""
                case "$folder_name" in
                    "01-pre-installation") category="Pre-Installation" ;;
                    "02-first-time-user") category="First-Time User" ;;
                    "03-returning-user") category="Returning User" ;;
                    "04-workout-journey") category="Workout Journey" ;;
                    "05-edge-cases") category="Edge Cases" ;;
                    *) category="$folder_name" ;;
                esac
                
                echo "- **$category**: $count images"
                total_screenshots=$((total_screenshots + count))
            fi
        fi
    done
    echo ""
    echo "**Total Screenshots:** $total_screenshots"
fi