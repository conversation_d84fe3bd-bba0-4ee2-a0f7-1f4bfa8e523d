using OpenQA.Selenium;
using OpenQA.Selenium.Appium;
using OpenQA.Selenium.Support.UI;
using System;
using System.Linq;
using System.Threading;

namespace DrMuscle.UITests.Pages
{
    /// <summary>
    /// Page object for Advanced Set Types functionality
    /// </summary>
    public class AdvancedSetsPage
    {
        private readonly AppiumDriver? _driver;
        private readonly WebDriverWait _wait;
        
        public AdvancedSetsPage(AppiumDriver driver)
        {
            _driver = driver ?? throw new ArgumentNullException(nameof(driver));
            _wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
        }
        
        // Advanced options menu
        private AppiumElement? AdvancedOptionsButton => FindElement(MobileBy.AccessibilityId("AdvancedOptions"));
        private AppiumElement? AdvancedOptionsModal => FindElement(MobileBy.AccessibilityId("AdvancedOptionsModal"));
        private AppiumElement? SaveAdvancedButton => FindElement(MobileBy.AccessibilityId("SaveAdvancedSettings"));
        
        // Drop sets elements
        private AppiumElement? DropSetsSwitch => FindElement(MobileBy.AccessibilityId("EnableDropSets"));
        private AppiumElement? DropCountInput => FindElement(MobileBy.AccessibilityId("DropCount"));
        private AppiumElement? DropPercentageInput => FindElement(MobileBy.AccessibilityId("DropPercentage"));
        private AppiumElement? DropSetPrompt => FindElement(MobileBy.AccessibilityId("DropSetPrompt"));
        private AppiumElement? SuggestedDropWeight => FindElement(MobileBy.AccessibilityId("SuggestedDropWeight"));
        
        // Rest-pause elements
        private AppiumElement? RestPauseSwitch => FindElement(MobileBy.AccessibilityId("EnableRestPause"));
        private AppiumElement? RestPauseDurationInput => FindElement(MobileBy.AccessibilityId("RestPauseDuration"));
        private AppiumElement? RestPauseRoundsInput => FindElement(MobileBy.AccessibilityId("RestPauseRounds"));
        private AppiumElement? RestPauseRoundLabel => FindElement(MobileBy.AccessibilityId("RestPauseRound"));
        
        // Pyramid elements
        private AppiumElement? SetSchemeSelector => FindElement(MobileBy.AccessibilityId("SetSchemeSelector"));
        private AppiumElement? PyramidStepsInput => FindElement(MobileBy.AccessibilityId("PyramidSteps"));
        private AppiumElement? PyramidIncrementInput => FindElement(MobileBy.AccessibilityId("PyramidIncrement"));
        private AppiumElement? PyramidWeightLabel => FindElement(MobileBy.AccessibilityId("PyramidWeight"));
        
        // AMRAP elements
        private AppiumElement? AMRAPSwitch => FindElement(MobileBy.AccessibilityId("EnableAMRAP"));
        
        // Timed sets elements
        private AppiumElement? TimedSetIndicator => FindElement(MobileBy.AccessibilityId("TimedSetIndicator"));
        private AppiumElement? StartTimerButton => FindElement(MobileBy.AccessibilityId("StartTimedSet"));
        private AppiumElement? PauseTimerButton => FindElement(MobileBy.AccessibilityId("PauseTimedSet"));
        private AppiumElement? ResumeTimerButton => FindElement(MobileBy.AccessibilityId("ResumeTimedSet"));
        private AppiumElement? CompleteTimerButton => FindElement(MobileBy.AccessibilityId("CompleteTimedSet"));
        private AppiumElement? ElapsedTimeLabel => FindElement(MobileBy.AccessibilityId("ElapsedTime"));
        
        // Mechanical drops elements
        private AppiumElement? AddVariationButton => FindElement(MobileBy.AccessibilityId("AddMechanicalVariation"));
        private AppiumElement? CurrentVariationLabel => FindElement(MobileBy.AccessibilityId("CurrentVariation"));
        private AppiumElement? VariationSearchInput => FindElement(MobileBy.AccessibilityId("VariationSearch"));
        
        // Summary elements
        private AppiumElement? SetSummaryLabel => FindElement(MobileBy.AccessibilityId("SetSummary"));
        
        // Actions
        public void OpenAdvancedOptions()
        {
            AdvancedOptionsButton?.Click();
            _wait.Until(d => AdvancedOptionsModal != null && AdvancedOptionsModal.Displayed);
        }
        
        public void SaveAdvancedSettings()
        {
            SaveAdvancedButton?.Click();
            Thread.Sleep(500);
        }
        
        // Drop sets
        public void EnableDropSets(bool enable)
        {
            SetSwitchState(DropSetsSwitch, enable);
        }
        
        public void SetDropCount(int count)
        {
            DropCountInput?.Clear();
            DropCountInput?.SendKeys(count.ToString());
        }
        
        public void SetDropPercentage(int percentage)
        {
            DropPercentageInput?.Clear();
            DropPercentageInput?.SendKeys(percentage.ToString());
        }
        
        // Rest-pause
        public void EnableRestPause(bool enable)
        {
            SetSwitchState(RestPauseSwitch, enable);
        }
        
        public void SetRestPauseDuration(int seconds)
        {
            RestPauseDurationInput?.Clear();
            RestPauseDurationInput?.SendKeys(seconds.ToString());
        }
        
        public void SetRestPauseRounds(int rounds)
        {
            RestPauseRoundsInput?.Clear();
            RestPauseRoundsInput?.SendKeys(rounds.ToString());
        }
        
        // Pyramid
        public void SelectSetScheme(string scheme)
        {
            SetSchemeSelector?.Click();
            Thread.Sleep(500);
            
            var schemeOption = FindElement(MobileBy.XPath($"//XCUIElementTypeButton[@name='{scheme}']"));
            schemeOption?.Click();
        }
        
        public void SetPyramidSteps(int steps)
        {
            PyramidStepsInput?.Clear();
            PyramidStepsInput?.SendKeys(steps.ToString());
        }
        
        public void SetPyramidIncrement(int increment)
        {
            PyramidIncrementInput?.Clear();
            PyramidIncrementInput?.SendKeys(increment.ToString());
        }
        
        // AMRAP
        public void EnableAMRAP(bool enable)
        {
            SetSwitchState(AMRAPSwitch, enable);
        }
        
        // Timed sets
        public void StartTimedSet()
        {
            StartTimerButton?.Click();
        }
        
        public void PauseTimedSet()
        {
            PauseTimerButton?.Click();
        }
        
        public void ResumeTimedSet()
        {
            ResumeTimerButton?.Click();
        }
        
        public void CompleteTimedSet()
        {
            CompleteTimerButton?.Click();
        }
        
        // Mechanical drops
        public void AddMechanicalVariation(string exerciseName)
        {
            AddVariationButton?.Click();
            Thread.Sleep(500);
            
            VariationSearchInput?.Clear();
            VariationSearchInput?.SendKeys(exerciseName);
            Thread.Sleep(500);
            
            var exerciseOption = FindElement(MobileBy.XPath($"//XCUIElementTypeCell[contains(@name, '{exerciseName}')]"));
            exerciseOption?.Click();
        }
        
        // Verifications
        public bool IsDropSetPromptVisible()
        {
            return DropSetPrompt != null && DropSetPrompt.Displayed;
        }
        
        public bool IsTimedExercise()
        {
            return TimedSetIndicator != null && TimedSetIndicator.Displayed;
        }
        
        // Data retrieval
        public string GetDropSetPrompt()
        {
            return DropSetPrompt?.Text ?? "";
        }
        
        public string GetSuggestedDropWeight()
        {
            return SuggestedDropWeight?.Text ?? "";
        }
        
        public string GetRestPauseRound()
        {
            return RestPauseRoundLabel?.Text ?? "";
        }
        
        public string GetPyramidWeight()
        {
            return PyramidWeightLabel?.Text ?? "";
        }
        
        public string GetElapsedTime()
        {
            return ElapsedTimeLabel?.Text ?? "";
        }
        
        public string GetCurrentVariation()
        {
            return CurrentVariationLabel?.Text ?? "";
        }
        
        public string GetSetSummary()
        {
            return SetSummaryLabel?.Text ?? "";
        }
        
        // Helper methods
        private AppiumElement? FindElement(By by)
        {
            try
            {
                return _driver?.FindElement(by) as AppiumElement;
            }
            catch (NoSuchElementException)
            {
                return null;
            }
        }
        
        private static void SetSwitchState(AppiumElement? switchElement, bool enabled)
        {
            if (switchElement != null)
            {
                var currentState = switchElement.GetAttribute("value") == "1";
                if (currentState != enabled)
                {
                    switchElement.Click();
                }
            }
        }
    }
}