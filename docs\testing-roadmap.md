# MAUI Automated Testing & Quality Roadmap

> **Purpose**  Establish a phased plan to integrate automated UI tests and quality gates into the GitHub Actions macOS pipeline.

---

## 1  Foundations
- [ ] Select cross-platform UI test runner (\*XHarness MAUI UITest\* initially)
- [ ] Add `DrMaxMuscle.Tests` UI test project (net8.0-ios‐simulator)
- [ ] Configure Mac runner to boot iOS simulator & run `dotnet test`
- [ ] Minimal happy-path: **Sign-up → Logout → Login**

## 2  Core User Flows
- [ ] Workout (flow: basic)
- [ ] Workout (flow: advanced – all options)
- [ ] Body-weight check-in & progress page
- [ ] Settings page smoke-test

## 3  Quality Gates
- [ ] Static code analysis (`dotnet format --verify-no-changes`, *Roslyn analyzers*)
- [ ] Code coverage threshold ≥ 60 % (`coverlet` + `reportgenerator`)
- [ ] Linting & formatting checks (EditorConfig + StyleCop)
- [ ] Dependency vulnerability scan (`dotnet list package --vulnerable`)

## 4  Workflow Integration
1. **setup** (reuse existing Mac job)
2. **build** (`dotnet publish`)
3. **test** (`dotnet test DrMaxMuscle.Tests.csproj`) – fails workflow on red
4. **quality** (format, analyzers, coverage)
5. **package/deploy** (existing)

## 5  Milestones & Ownership
| Milestone | Owner | Target Date |
|-----------|-------|-------------|
| Foundations | QA Guild | T+7 days |
| Core flows  | QA Guild | T+30 days |
| Quality gates | Dev Ex Team | T+45 days |

---

### Tips & Ideas
- Use **`--output-directory`** of XHarness to attach artifacts
- Run Android tests in parallel on Ubicloud Linux (future)
- Consider **`playwright`** for cross-platform visual testing later

---

*Created automatically – refine as we iterate.* 