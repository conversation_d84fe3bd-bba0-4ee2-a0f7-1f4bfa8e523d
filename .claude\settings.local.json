{"permissions": {"allow": ["Bash(rm:*)", "WebFetch(domain:docs.anthropic.com)", "Bash(grep:*)", "Bash(ls:*)", "Bash(xcodebuild:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git revert:*)", "Bash(git checkout:*)", "Bash(git push:*)", "Bash(find:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(realpath:*)", "Bash(cp:*)", "mcp__context7__resolve-library-id", "Bash(awk:*)", "<PERSON><PERSON>(sed:*)", "Bash(rg:*)", "Bash(ruby:*)", "<PERSON><PERSON>(python3:*)", "Bash(git rev-parse:*)", "Bash(git merge-base:*)", "Bash(gh pr create:*)", "<PERSON><PERSON>(gh pr edit:*)", "Bash(git fetch:*)", "Bash(gh repo sync:*)", "Bash(bash:*)", "Bash(git config:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(dotnet build)", "Bash(dotnet build:*)", "Bash(dotnet format:*)", "WebFetch(domain:github.com)", "Bash(dotnet restore --verbosity normal)", "Bash(gh auth:*)", "<PERSON><PERSON>(gh run list:*)", "Bash(gh run view:*)", "Bash(for:*)", "Bash(do mv \"$f\" \"$f.bak\")", "Bash(done)", "Bash(dotnet restore)", "Bash(dotnet nuget list:*)", "Bash(dotnet nuget add source:*)", "Bash(dotnet test:*)", "WebFetch(domain:learn.microsoft.com)", "WebFetch(domain:devblogs.microsoft.com)", "Bash(gh workflow view:*)", "<PERSON><PERSON>(mv:*)", "Bash(gh run:*)", "Bash(echo \"Run status: $?\")", "Bash(gh workflow run:*)", "Bash(gh workflow:*)"], "deny": []}}