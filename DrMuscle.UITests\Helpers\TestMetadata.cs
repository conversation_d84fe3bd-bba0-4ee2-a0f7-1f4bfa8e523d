using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text.RegularExpressions;
using NUnit.Framework;
using NUnit.Framework.Interfaces;

namespace DrMuscle.UITests.Helpers
{
    /// <summary>
    /// Metadata about a test for reporting purposes
    /// </summary>
    public class TestMetadata
    {
        public string Name { get; set; } = "";
        public string FullName { get; set; } = "";
        public string ClassName { get; set; } = "";
        public string? Category { get; set; }
        public string? SubCategory { get; set; }
        public string Description { get; set; } = "";
        public TestStatus Status { get; set; }
        public string? ErrorMessage { get; set; }
        public double Duration { get; set; }
        public List<string> Screenshots { get; set; } = new List<string>();
        
        public string GetJourneyStage()
        {
            if (!string.IsNullOrEmpty(Category))
                return Category;
                
            // Infer from class name if no category
            if (ClassName.Contains("Auth") || ClassName.Contains("Login"))
                return TestCategories.FirstTimeUser;
            if (ClassName.Contains("Workout") || ClassName.Contains("Exercise"))
                return TestCategories.WorkoutJourney;
            if (ClassName.Contains("Network") || ClassName.Contains("Error"))
                return TestCategories.EdgeCases;
                
            return TestCategories.PreInstallation;
        }
    }
    
    /// <summary>
    /// Collects and organizes test metadata for reporting
    /// </summary>
    public partial class TestMetadataCollector
    {
        private static readonly Dictionary<string, TestMetadata> TestData = new Dictionary<string, TestMetadata>();
        
        /// <summary>
        /// Records test metadata from the current test context
        /// </summary>
        public static void RecordTest(TestContext.TestAdapter test, TestContext.ResultAdapter result)
        {
            var metadata = new TestMetadata
            {
                Name = test.Name,
                FullName = test.FullName,
                ClassName = test.ClassName ?? "Unknown",
                Status = result.Outcome.Status,
                ErrorMessage = result.Message,
                Duration = 0 // Duration not available from ResultAdapter
            };
            
            // Extract category from properties (NUnit stores attributes as properties)
            if (test.Properties.ContainsKey("Category"))
            {
                var categories = test.Properties["Category"];
                if (categories != null)
                {
                    var categoryList = categories.ToList();
                    if (categoryList.Count > 0)
                    {
                        metadata.Category = categoryList[0]?.ToString();
                    }
                }
            }
            
            // Extract description from properties
            if (test.Properties.ContainsKey("Description"))
            {
                var descriptions = test.Properties["Description"];
                if (descriptions != null)
                {
                    var descList = descriptions.ToList();
                    if (descList.Count > 0)
                    {
                        metadata.Description = descList[0]?.ToString() ?? "";
                    }
                }
            }
            
            if (string.IsNullOrEmpty(metadata.Description))
            {
                // Generate description from test name
                metadata.Description = GenerateDescription(test.Name);
            }
            
            TestData[test.FullName] = metadata;
        }
        
        /// <summary>
        /// Adds a screenshot to the current test
        /// </summary>
        public static void AddScreenshot(string testFullName, string screenshotPath)
        {
            if (TestData.TryGetValue(testFullName, out var metadata))
            {
                metadata.Screenshots.Add(screenshotPath);
            }
        }
        
        /// <summary>
        /// Gets all collected test metadata organized by journey stage
        /// </summary>
        public static Dictionary<string, List<TestMetadata>> GetTestsByJourney()
        {
            var grouped = TestData.Values
                .GroupBy(t => t.GetJourneyStage())
                .ToDictionary(g => g.Key, g => g.ToList());
                
            // Ensure all categories are present
            foreach (var category in new[] { 
                TestCategories.PreInstallation,
                TestCategories.FirstTimeUser,
                TestCategories.ReturningUser,
                TestCategories.WorkoutJourney,
                TestCategories.EdgeCases 
            })
            {
                if (!grouped.ContainsKey(category))
                {
                    grouped[category] = new List<TestMetadata>();
                }
            }
            
            return grouped;
        }
        
        // Compiled regex for better performance
        [GeneratedRegex("([A-Z])")]
        private static partial Regex PascalCaseRegex();
        
        /// <summary>
        /// Generates a human-readable description from a test name
        /// </summary>
        private static string GenerateDescription(string testName)
        {
            // Convert PascalCase to sentence
            var words = PascalCaseRegex().Replace(testName, " $1").Trim();
            
            // Lowercase everything except first letter
            if (words.Length > 1)
            {
                words = char.ToUpper(words[0]) + words.Substring(1).ToLower();
            }
            
            // Fix common patterns
            words = words.Replace(" i d", " ID")
                         .Replace(" u i", " UI")
                         .Replace(" a p i", " API")
                         .Replace(" happy path", " happy path")
                         .Replace(" with ", " with ");
                         
            return words;
        }
        
        /// <summary>
        /// Clears all collected metadata
        /// </summary>
        public static void Clear()
        {
            TestData.Clear();
        }
    }
}