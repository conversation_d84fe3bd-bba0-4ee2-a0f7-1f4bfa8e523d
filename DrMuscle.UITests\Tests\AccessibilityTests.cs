using NUnit.Framework;
using DrMuscle.UITests.Helpers;
using DrMuscle.UITests.Pages;
using System;
using System.Threading;
using OpenQA.Selenium.Appium;
using OpenQA.Selenium;

namespace DrMuscle.UITests.Tests
{
    /// <summary>
    /// Comprehensive tests for Accessibility
    /// Tests VoiceOver/TalkBack, large text, one-handed operation, and accessibility labels
    /// </summary>
    [TestFixture]
    [Category(TestCategories.Accessibility)]
    public class AccessibilityTests : AppiumSetup
    {
        private WorkoutPage workoutPage = null!;
        private SettingsPage settingsPage = null!;
        private TimerPage timerPage = null!;
        private WorkoutFlowPage workoutFlowPage = null!;
        
        [SetUp]
        public void TestSetup()
        {
            workoutPage = new WorkoutPage(Driver!);
            settingsPage = new SettingsPage(Driver!);
            timerPage = new TimerPage(Driver!);
            workoutFlowPage = new WorkoutFlowPage(Driver!);
            
            // Prepare for accessibility testing
            PrepareAccessibilityTesting();
        }
        
        [Test]
        [Order(1)]
        [Description("Tests VoiceOver/TalkBack navigation through workout")]
        public void TEST_VoiceOver_Full_Workout()
        {
            TestContext.WriteLine("=== Testing VoiceOver/TalkBack Full Workout ===");
            
            // Enable screen reader
            EnableScreenReader();
            Thread.Sleep(2000);
            
            TakeScreenshot("01-voiceover-enabled");
            
            // Navigate to start workout using accessibility
            TestContext.WriteLine("Testing accessibility navigation to start workout...");
            
            var startButton = Driver?.FindElement(MobileBy.AccessibilityId("StartWorkoutButton"));
            Assert.That(startButton, Is.Not.Null,
                "Start workout button should have accessibility ID");
            
            // Verify accessibility label
            var accessibilityLabel = startButton?.GetAttribute("label") ?? 
                                   startButton?.GetAttribute("contentDescription");
            TestContext.WriteLine($"Start button accessibility label: {accessibilityLabel}");
            
            Assert.That(accessibilityLabel, Is.Not.Empty,
                "Start button should have accessibility label");
            
            // Activate using accessibility
            startButton?.Click();
            Thread.Sleep(2000);
            
            // Verify exercise list is accessible
            TestContext.WriteLine("Testing exercise list accessibility...");
            
            var exerciseList = Driver?.FindElement(MobileBy.AccessibilityId("ExerciseList"));
            Assert.That(exerciseList, Is.Not.Null,
                "Exercise list should be accessible");
            
            // Get all exercises using accessibility
            var exercises = Driver?.FindElements(MobileBy.ClassName("XCUIElementTypeCell"));
            TestContext.WriteLine($"Found {exercises?.Count} accessible exercise cells");
            
            Assert.That(exercises?.Count, Is.GreaterThan(0),
                "Exercises should be accessible as cells");
            
            TakeScreenshot("02-accessible-exercise-list");
            
            // Select first exercise using accessibility
            var firstExercise = exercises?[0];
            var exerciseName = firstExercise?.GetAttribute("label") ?? 
                              firstExercise?.GetAttribute("contentDescription");
            TestContext.WriteLine($"First exercise: {exerciseName}");
            
            firstExercise?.Click();
            Thread.Sleep(2000);
            
            // Test weight/reps entry accessibility
            TestContext.WriteLine("Testing accessible weight/reps entry...");
            
            var weightInput = Driver?.FindElement(MobileBy.AccessibilityId("WeightInput"));
            var repsInput = Driver?.FindElement(MobileBy.AccessibilityId("RepsInput"));
            
            Assert.That(weightInput, Is.Not.Null,
                "Weight input should be accessible");
            Assert.That(repsInput, Is.Not.Null,
                "Reps input should be accessible");
            
            // Check input labels
            var weightLabel = weightInput?.GetAttribute("label");
            var repsLabel = repsInput?.GetAttribute("label");
            
            TestContext.WriteLine($"Weight input label: {weightLabel}");
            TestContext.WriteLine($"Reps input label: {repsLabel}");
            
            Assert.That(weightLabel, Does.Contain("weight") | Does.Contain("Weight"),
                "Weight input should have descriptive label");
            Assert.That(repsLabel, Does.Contain("reps") | Does.Contain("Reps"),
                "Reps input should have descriptive label");
            
            TakeScreenshot("03-accessible-inputs");
            
            // Enter set using accessibility
            weightInput?.Clear();
            weightInput?.SendKeys("135");
            repsInput?.Clear();
            repsInput?.SendKeys("10");
            
            // Save set
            var saveButton = Driver?.FindElement(MobileBy.AccessibilityId("SaveSetButton"));
            Assert.That(saveButton, Is.Not.Null,
                "Save button should be accessible");
            
            saveButton?.Click();
            Thread.Sleep(1000);
            
            // Test timer accessibility
            TestContext.WriteLine("Testing timer accessibility...");
            
            if (timerPage.IsTimerVisible())
            {
                var timerDisplay = Driver?.FindElement(MobileBy.AccessibilityId("TimerDisplay"));
                var timerLabel = timerDisplay?.GetAttribute("label");
                TestContext.WriteLine($"Timer accessibility: {timerLabel}");
                
                Assert.That(timerLabel, Is.Not.Empty,
                    "Timer should announce remaining time");
                
                TakeScreenshot("04-accessible-timer");
                
                // Skip timer
                var skipButton = Driver?.FindElement(MobileBy.AccessibilityId("SkipTimerButton"));
                skipButton?.Click();
            }
            
            // Complete more sets
            for (int i = 0; i < 2; i++)
            {
                workoutPage.EnterSet("8", "135");
                workoutPage.SaveSet();
                if (timerPage.IsTimerVisible())
                {
                    timerPage.SkipTimer();
                }
            }
            
            // Finish exercise with accessibility
            var finishButton = Driver?.FindElement(MobileBy.AccessibilityId("FinishExerciseButton"));
            var finishLabel = finishButton?.GetAttribute("label");
            TestContext.WriteLine($"Finish button label: {finishLabel}");
            
            finishButton?.Click();
            Thread.Sleep(1000);
            
            // Navigate and complete workout
            var finishWorkoutButton = Driver?.FindElement(MobileBy.AccessibilityId("FinishWorkoutButton"));
            finishWorkoutButton?.Click();
            Thread.Sleep(2000);
            
            // Verify summary is accessible
            var summary = Driver?.FindElement(MobileBy.AccessibilityId("WorkoutSummary"));
            var summaryText = summary?.GetAttribute("label") ?? summary?.Text;
            TestContext.WriteLine($"Accessible summary: {summaryText}");
            
            Assert.That(summaryText, Is.Not.Empty,
                "Workout summary should be accessible");
            
            TakeScreenshot("05-accessible-summary");
            
            // Disable screen reader
            DisableScreenReader();
        }
        
        [Test]
        [Order(2)]
        [Description("Tests large text mode UI adaptation")]
        public void TEST_Large_Text_Mode()
        {
            TestContext.WriteLine("=== Testing Large Text Mode ===");
            
            // Enable large text
            EnableLargeText();
            Thread.Sleep(2000);
            
            TakeScreenshot("06-large-text-enabled");
            
            // Start workout
            workoutPage.StartWorkout();
            Thread.Sleep(2000);
            
            // Check text is not truncated in exercise list
            var exercises = workoutPage.GetExerciseList();
            TestContext.WriteLine($"Exercises with large text: {string.Join(", ", exercises)}");
            
            // Take screenshot to verify no text truncation
            TakeScreenshot("07-large-text-exercise-list");
            
            // Select exercise
            workoutPage.SelectExerciseByName(exercises[0]);
            Thread.Sleep(2000);
            
            // Verify exercise detail text
            var exerciseName = workoutPage.GetCurrentExerciseName();
            var targetReps = workoutPage.GetTargetReps();
            var recommendedWeight = workoutPage.GetRecommendedWeight();
            
            TestContext.WriteLine($"Exercise: {exerciseName}");
            TestContext.WriteLine($"Target: {targetReps}");
            TestContext.WriteLine($"Weight: {recommendedWeight}");
            
            Assert.That(exerciseName, Is.Not.Empty,
                "Exercise name should be visible with large text");
            
            TakeScreenshot("08-large-text-exercise-detail");
            
            // Test input fields with large text
            workoutPage.EnterSet("10", "100");
            Thread.Sleep(500);
            
            TakeScreenshot("09-large-text-inputs");
            
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            // Check timer display with large text
            if (timerPage.IsTimerVisible())
            {
                var timerText = timerPage.GetRemainingTime();
                TestContext.WriteLine($"Timer with large text: {timerText}");
                
                TakeScreenshot("10-large-text-timer");
                
                timerPage.SkipTimer();
            }
            
            // Navigate to settings
            workoutPage.NavigateToSettings();
            Thread.Sleep(2000);
            
            // Verify settings are readable with large text
            TakeScreenshot("11-large-text-settings");
            
            // Return to workout
            Driver?.Navigate().Back();
            Thread.Sleep(1000);
            
            // Complete workout
            workoutPage.FinishExercise();
            Thread.Sleep(1000);
            
            workoutPage.FinishWorkout();
            Thread.Sleep(2000);
            
            // Check summary with large text
            var summary = workoutFlowPage?.GetWorkoutSummary() ?? "";
            TestContext.WriteLine($"Summary with large text: {summary}");
            
            TakeScreenshot("12-large-text-summary");
            
            // Disable large text
            DisableLargeText();
        }
        
        [Test]
        [Order(3)]
        [Description("Tests one-handed operation accessibility")]
        public void TEST_One_Handed_Operation()
        {
            TestContext.WriteLine("=== Testing One-Handed Operation ===");
            
            // Get screen dimensions
            var screenSize = Driver?.Manage().Window.Size;
            var screenHeight = screenSize?.Height ?? 800;
            var bottomThird = screenHeight * 2 / 3;
            
            TestContext.WriteLine($"Screen height: {screenHeight}, Testing reach up to: {bottomThird}");
            
            // Start workout
            workoutPage.StartWorkout();
            Thread.Sleep(2000);
            
            // Verify critical controls are in lower portion of screen
            TestContext.WriteLine("Checking control positions for one-handed use...");
            
            // Get exercise list items
            var exercises = Driver?.FindElements(MobileBy.ClassName("XCUIElementTypeCell"));
            if (exercises != null && exercises.Count > 0)
            {
                foreach (var exercise in exercises)
                {
                    var location = exercise.Location;
                    TestContext.WriteLine($"Exercise at Y: {location.Y}");
                    
                    // At least first few exercises should be reachable
                    if (exercises.IndexOf(exercise) < 3)
                    {
                        Assert.That(location.Y, Is.LessThan(bottomThird),
                            "First exercises should be reachable one-handed");
                    }
                }
            }
            
            TakeScreenshot("13-one-handed-exercise-list");
            
            // Select first exercise
            workoutPage.SelectExerciseByName(workoutPage.GetExerciseList()[0]);
            Thread.Sleep(2000);
            
            // Check input control positions
            var weightInput = Driver?.FindElement(MobileBy.AccessibilityId("WeightInput"));
            var repsInput = Driver?.FindElement(MobileBy.AccessibilityId("RepsInput"));
            var saveButton = Driver?.FindElement(MobileBy.AccessibilityId("SaveSetButton"));
            
            if (weightInput != null)
            {
                var weightY = weightInput.Location.Y;
                TestContext.WriteLine($"Weight input Y: {weightY}");
                Assert.That(weightY, Is.LessThan(bottomThird),
                    "Weight input should be reachable one-handed");
            }
            
            if (saveButton != null)
            {
                var saveY = saveButton.Location.Y;
                TestContext.WriteLine($"Save button Y: {saveY}");
                Assert.That(saveY, Is.LessThan(screenHeight - 100),
                    "Save button should be at bottom for easy reach");
            }
            
            TakeScreenshot("14-one-handed-inputs");
            
            // Test gesture alternatives
            TestContext.WriteLine("Testing swipe gestures for navigation...");
            
            // Enter set
            workoutPage.EnterSet("10", "100");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            // Test swipe to skip timer (if implemented)
            if (timerPage.IsTimerVisible())
            {
                TestContext.WriteLine("Testing timer skip accessibility...");
                
                var skipButton = Driver?.FindElement(MobileBy.AccessibilityId("SkipTimerButton"));
                if (skipButton != null)
                {
                    var skipY = skipButton.Location.Y;
                    TestContext.WriteLine($"Skip timer Y: {skipY}");
                    
                    Assert.That(skipY, Is.LessThan(screenHeight - 100),
                        "Skip timer should be easily reachable");
                }
                
                TakeScreenshot("15-one-handed-timer");
                timerPage.SkipTimer();
            }
            
            // Complete exercise
            workoutPage.EnterSet("8", "100");
            workoutPage.SaveSet();
            workoutPage.FinishExercise();
            Thread.Sleep(1000);
            
            // Check finish workout button position
            var finishWorkout = Driver?.FindElement(MobileBy.AccessibilityId("FinishWorkoutButton"));
            if (finishWorkout != null)
            {
                var finishY = finishWorkout.Location.Y;
                TestContext.WriteLine($"Finish workout Y: {finishY}");
                
                Assert.That(finishY, Is.LessThan(screenHeight - 50),
                    "Finish workout should be at bottom");
            }
            
            TakeScreenshot("16-one-handed-finish");
            
            workoutPage.FinishWorkout();
            Thread.Sleep(2000);
            
            TakeScreenshot("17-one-handed-complete");
        }
        
        [Test]
        [Order(4)]
        [Description("Tests color contrast and visibility")]
        public void TEST_Color_Contrast_Visibility()
        {
            TestContext.WriteLine("=== Testing Color Contrast and Visibility ===");
            
            // Start workout
            workoutPage.StartWorkout();
            Thread.Sleep(2000);
            
            // Note: Actual contrast testing would require color analysis
            // Here we verify elements are visible and take screenshots for manual review
            
            TestContext.WriteLine("Checking element visibility...");
            
            // Check primary action buttons are prominent
            var startButton = Driver?.FindElement(MobileBy.AccessibilityId("StartWorkoutButton"));
            Assert.That(startButton?.Displayed, Is.True,
                "Primary actions should be clearly visible");
            
            TakeScreenshot("18-contrast-home");
            
            // Select exercise
            workoutPage.SelectExerciseByName("Bench Press");
            Thread.Sleep(2000);
            
            // Verify important information stands out
            var recommendedWeight = workoutPage.GetRecommendedWeight();
            var targetReps = workoutPage.GetTargetReps();
            
            Assert.That(recommendedWeight, Is.Not.Empty,
                "Recommended weight should be visible");
            Assert.That(targetReps, Is.Not.Empty,
                "Target reps should be visible");
            
            TakeScreenshot("19-contrast-exercise");
            
            // Enter and save set
            workoutPage.EnterSet("10", "135");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            // Check timer visibility
            if (timerPage.IsTimerVisible())
            {
                var timerDisplay = timerPage.GetRemainingTime();
                Assert.That(timerDisplay, Is.Not.Empty,
                    "Timer should be clearly visible");
                
                TakeScreenshot("20-contrast-timer");
                timerPage.SkipTimer();
            }
            
            // Test error state visibility
            TestContext.WriteLine("Testing error state contrast...");
            
            workoutPage.EnterWeight("-50");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            TakeScreenshot("21-contrast-error");
            
            // Clear error
            workoutPage.EnterWeight("135");
            
            // Complete workout
            workoutPage.EnterSet("8", "135");
            workoutPage.SaveSet();
            workoutPage.FinishExercise();
            Thread.Sleep(1000);
            
            workoutPage.FinishWorkout();
            Thread.Sleep(2000);
            
            TakeScreenshot("22-contrast-summary");
        }
        
        [Test]
        [Order(5)]
        [Description("Tests haptic feedback and audio cues")]
        public void TEST_Haptic_Audio_Feedback()
        {
            TestContext.WriteLine("=== Testing Haptic Feedback and Audio Cues ===");
            
            // Note: Automated testing of haptics/audio is limited
            // This test verifies the features are enabled and logs for manual verification
            
            // Check settings for haptic/audio options
            workoutPage.NavigateToSettings();
            Thread.Sleep(2000);
            
            TestContext.WriteLine("Checking haptic feedback settings...");
            
            // Look for haptic/sound settings
            var hapticSetting = Driver?.FindElements(MobileBy.XPath("//*[contains(@label, 'Haptic') or contains(@label, 'Vibration')]"));
            var soundSetting = Driver?.FindElements(MobileBy.XPath("//*[contains(@label, 'Sound') or contains(@label, 'Audio')]"));
            
            if (hapticSetting?.Count > 0)
            {
                TestContext.WriteLine("Haptic feedback setting found");
                TakeScreenshot("23-haptic-settings");
            }
            
            if (soundSetting?.Count > 0)
            {
                TestContext.WriteLine("Sound setting found");
            }
            
            // Return to workout
            Driver?.Navigate().Back();
            Thread.Sleep(1000);
            
            workoutPage.StartWorkout();
            Thread.Sleep(2000);
            
            workoutPage.SelectExerciseByName("Squat");
            Thread.Sleep(2000);
            
            // Test feedback on set completion
            TestContext.WriteLine("Testing feedback on set save...");
            workoutPage.EnterSet("5", "225");
            
            // Log for manual verification
            TestContext.WriteLine("[Manual Check] Verify haptic feedback on save button tap");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            // Test timer completion feedback
            if (timerPage.IsTimerVisible())
            {
                TestContext.WriteLine("Waiting for timer completion...");
                
                // Wait for timer to near completion
                Thread.Sleep(3000);
                
                TestContext.WriteLine("[Manual Check] Verify audio/haptic alert when timer completes");
                
                // Let timer complete naturally for feedback test
                var startTime = DateTime.Now;
                while (timerPage.IsTimerVisible() && (DateTime.Now - startTime).TotalSeconds < 30)
                {
                    Thread.Sleep(1000);
                }
            }
            
            TakeScreenshot("24-feedback-test-complete");
            
            // Complete workout
            workoutPage.FinishExercise();
            Thread.Sleep(1000);
            
            TestContext.WriteLine("[Manual Check] Verify feedback on workout completion");
            workoutPage.FinishWorkout();
            Thread.Sleep(2000);
        }
        
        private void PrepareAccessibilityTesting()
        {
            Thread.Sleep(3000);
            
            // Ensure we're at home screen
            workoutPage.WaitForStartWorkout();
            
            // Reset any accessibility settings
            DisableScreenReader();
            DisableLargeText();
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private void EnableScreenReader()
        #pragma warning restore CA1822 // Mark members as static
        {
            // Platform-specific screen reader activation
            if (Driver?.PlatformName == "iOS")
            {
                TestContext.WriteLine("[Simulated] VoiceOver enabled");
                // In real testing: Settings > Accessibility > VoiceOver
            }
            else
            {
                TestContext.WriteLine("[Simulated] TalkBack enabled");
                // In real testing: Settings > Accessibility > TalkBack
            }
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private void DisableScreenReader()
        #pragma warning restore CA1822 // Mark members as static
        {
            if (Driver?.PlatformName == "iOS")
            {
                TestContext.WriteLine("[Simulated] VoiceOver disabled");
            }
            else
            {
                TestContext.WriteLine("[Simulated] TalkBack disabled");
            }
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private void EnableLargeText()
        #pragma warning restore CA1822 // Mark members as static
        {
            // In real testing, would adjust system text size
            TestContext.WriteLine("[Simulated] Large text enabled");
            
            // Some apps have in-app text size settings
            var settings = new SettingsPage(Driver!);
            // settings.SetTextSize("Large"); // If implemented
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private void DisableLargeText()
        #pragma warning restore CA1822 // Mark members as static
        {
            TestContext.WriteLine("[Simulated] Large text disabled");
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private void TakeScreenshot(string name)
        #pragma warning restore CA1822 // Mark members as static
        {
            TestTimings.TakeScreenshot(Driver, name);
        }
    }
}