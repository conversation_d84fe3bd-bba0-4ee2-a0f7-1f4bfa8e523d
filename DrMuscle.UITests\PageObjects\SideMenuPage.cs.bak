using DrMuscle.UITests.Extensions;

namespace DrMuscle.UITests.PageObjects
{
    public class SideMenuPage
    {
        private readonly IApp _app;

        // Automation IDs for page elements
        private const string _settingsTabId = "SettingsTab";
        private const string _logoutButtonId = "LogoutButton";
        private const string _settingsButtonId = "SettingsButton";
        private const string _profileButtonId = "ProfileButton";

        public SideMenuPage(IApp app)
        {
            _app = app;
        }

        public void OpenMenu()
        {
            // On iOS, the menu is accessed via Settings tab
            _app.WaitAndTap(_settingsTabId);
            // Wait for menu/settings page to load
            Thread.Sleep(1000);
        }

        public void TapLogout()
        {
            _app.WaitAndTap(_logoutButtonId);
        }

        public void TapSettings()
        {
            _app.WaitAndTap(_settingsButtonId);
        }

        public void TapProfile()
        {
            _app.WaitAndTap(_profileButtonId);
        }
    }
}
