# DrMuscle UI Tests with <PERSON>pp<PERSON>

This project contains UI tests for the DrMuscle MAUI application using Appium WebDriver.

## Prerequisites

### Local Development
1. Node.js installed
2. Appium installed globally: `npm install -g appium`
3. XCUITest driver installed: `appium driver install xcuitest`
4. iOS Simulator with the app installed

### Running Tests Locally

1. Start Appium server:
   ```bash
   appium
   ```

2. Build the iOS app:
   ```bash
   cd ../DrMaxMuscle
   dotnet build -f net8.0-ios -c Release
   ```

3. Run the tests:
   ```bash
   dotnet test
   ```

## Test Structure

- `AppiumSetup.cs` - Base class that handles Appium driver initialization
- `Pages/` - Page Object Model classes for UI elements
- `Tests/` - Test classes organized by feature

## Adding New Tests

1. Add `AutomationId` to MAUI elements you want to test
2. Create a Page Object class in `Pages/` folder
3. Write tests using the Page Object pattern

Example:
```csharp
[Test]
public void UserCanLogin()
{
    var welcomePage = new WelcomePage(Driver);
    welcomePage.TapLogin();
    
    var loginPage = new LoginPage(Driver);
    loginPage.EnterCredentials("<EMAIL>", "password");
    loginPage.Submit();
    
    // Assert successful login
}
```

## CI/CD Integration

Tests run automatically on GitHub Actions with:
- Self-hosted macOS runners
- Appium server started automatically
- Screenshots captured on failure
- Test results uploaded as artifacts

### Environment Variables

- `IOS_APP_BUNDLE` - Path to the iOS app bundle (auto-detected if not set)
- `IOS_PLATFORM_VERSION` - iOS SDK version (default: 18.4)
- `IOS_DEVICE_NAME` - Simulator device name (default: iPhone 15 Pro)
- `APPIUM_SERVER_URL` - Appium server URL (default: http://127.0.0.1:4723/)

## Troubleshooting

- If tests can't find elements, ensure `AutomationId` is set in MAUI
- Check `appium.log` for server issues
- Use `Driver.PageSource` to inspect available elements
- Screenshots are saved on test failure for debugging