using NUnit.Framework;
using System;
using System.IO;
using System.Threading;
using DrMuscle.UITests.Helpers;

namespace DrMuscle.UITests.Tests
{
    /// <summary>
    /// Basic tests using xcrun simctl when Appium is not available
    /// </summary>
    [TestFixture]
    public class SimCtlBasicTests
    {
        private string? _appPath;
        
        [OneTimeSetUp]
        public void Setup()
        {
            if (!SimCtlTestRunner.IsAvailable())
            {
                Assert.Ignore("xcrun simctl is not available");
            }
            
            // Get app bundle path from environment or search for it
            _appPath = Environment.GetEnvironmentVariable("IOS_APP_BUNDLE");
            if (string.IsNullOrEmpty(_appPath))
            {
                // Try common locations
                var searchPaths = new[]
                {
                    "../DrMaxMuscle/bin/Release/net8.0-ios/iossimulator-x64/DrMaxMuscle.app",
                    "../DrMaxMuscle/bin/Debug/net8.0-ios/iossimulator-x64/DrMaxMuscle.app"
                };
                
                foreach (var path in searchPaths)
                {
                    var fullPath = Path.GetFullPath(path);
                    if (Directory.Exists(fullPath))
                    {
                        _appPath = fullPath;
                        break;
                    }
                }
            }
            
            if (string.IsNullOrEmpty(_appPath) || !Directory.Exists(_appPath))
            {
                Assert.Ignore($"App bundle not found at: {_appPath}");
            }
            
            Console.WriteLine($"Using app bundle: {_appPath}");
        }
        
        [Test, Order(1)]
        public void VerifySimulatorEnvironment()
        {
            // Check if we have a booted device
            var deviceId = SimCtlTestRunner.GetBootedDeviceId();
            Assert.That(deviceId, Is.Not.Null.And.Not.Empty, "Should have a booted iOS simulator");
            
            Console.WriteLine($"Found booted device: {deviceId}");
        }
        
        [Test, Order(2)]
        public void InstallAppOnSimulator()
        {
            // Install the app
            SimCtlTestRunner.InstallApp(_appPath!);
            Thread.Sleep(2000);
            
            // Verify installation
            var isInstalled = SimCtlTestRunner.IsAppInstalled();
            Assert.That(isInstalled, Is.True, "App should be installed on simulator");
            
            Console.WriteLine("App installed successfully");
        }
        
        [Test, Order(3)]
        [Retry(3)] // App launch can be flaky on CI
        public void LaunchAppAndTakeScreenshot()
        {
            // Launch the app
            SimCtlTestRunner.LaunchApp();
            
            // Take a screenshot
            var screenshotDir = Path.Combine(TestContext.CurrentContext.WorkDirectory, "Screenshots");
            Directory.CreateDirectory(screenshotDir);
            
            var screenshotPath = Path.Combine(screenshotDir, "app-launch.png");
            SimCtlTestRunner.TakeScreenshot(screenshotPath);
            
            Assert.That(File.Exists(screenshotPath), Is.True, "Screenshot should be created");
            TestContext.AddTestAttachment(screenshotPath, "App Launch Screenshot");
            
            Console.WriteLine($"Screenshot saved to: {screenshotPath}");
        }
        
        [Test, Order(4)]
        public void VerifyAppIsRunning()
        {
            // Give app time to fully launch
            Thread.Sleep(5000);
            
            // Take another screenshot to see current state
            var screenshotPath = Path.Combine(
                TestContext.CurrentContext.WorkDirectory, 
                "Screenshots", 
                "app-running.png"
            );
            
            SimCtlTestRunner.TakeScreenshot(screenshotPath);
            TestContext.AddTestAttachment(screenshotPath, "App Running Screenshot");
            
            // Basic verification that we can interact with simulator
            Assert.Pass("App launched and screenshots captured successfully");
        }
        
        [Test, Order(5)]
        public void TestMultipleScreenshots()
        {
            // Take multiple screenshots to capture app state
            var screenshotDir = Path.Combine(TestContext.CurrentContext.WorkDirectory, "Screenshots");
            
            for (int i = 1; i <= 3; i++)
            {
                Thread.Sleep(2000);
                var screenshotPath = Path.Combine(screenshotDir, $"app-state-{i}.png");
                SimCtlTestRunner.TakeScreenshot(screenshotPath);
                
                if (File.Exists(screenshotPath))
                {
                    TestContext.AddTestAttachment(screenshotPath, $"App State {i}");
                    Console.WriteLine($"Screenshot {i} captured");
                }
            }
            
            Assert.Pass("Multiple screenshots captured");
        }
    }
}