using OpenQA.Selenium;
using OpenQA.Selenium.Appium;
using OpenQA.Selenium.Support.UI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

namespace DrMuscle.UITests.Pages
{
    /// <summary>
    /// Page object for Weight Entry Variations functionality
    /// </summary>
    public class WeightEntryPage
    {
        private readonly AppiumDriver? _driver;
        private readonly WebDriverWait _wait;
        
        public WeightEntryPage(AppiumDriver driver)
        {
            _driver = driver ?? throw new ArgumentNullException(nameof(driver));
            _wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
        }
        
        // Weight display elements
        private AppiumElement? WeightDisplay => FindElement(MobileBy.AccessibilityId("WeightDisplay"));
        private AppiumElement? WeightInput => FindElement(MobileBy.AccessibilityId("WeightInput"));
        private AppiumElement? UnitToggle => FindElement(MobileBy.AccessibilityId("WeightUnitToggle"));
        private AppiumElement? WeightOptionsButton => FindElement(MobileBy.AccessibilityId("WeightOptions"));
        
        // Bodyweight elements
        private AppiumElement? BodyweightIndicator => FindElement(MobileBy.AccessibilityId("BodyweightIndicator"));
        private AppiumElement? WeightedModeButton => FindElement(MobileBy.AccessibilityId("WeightedMode"));
        private AppiumElement? AssistedModeButton => FindElement(MobileBy.AccessibilityId("AssistedMode"));
        private AppiumElement? AddedWeightInput => FindElement(MobileBy.AccessibilityId("AddedWeight"));
        private AppiumElement? AssistanceWeightInput => FindElement(MobileBy.AccessibilityId("AssistanceWeight"));
        private AppiumElement? ProgressionInfo => FindElement(MobileBy.AccessibilityId("ProgressionInfo"));
        private AppiumElement? UserBodyweightLabel => FindElement(MobileBy.AccessibilityId("UserBodyweight"));
        private AppiumElement? EffectiveWeightLabel => FindElement(MobileBy.AccessibilityId("EffectiveWeight"));
        
        // Band elements
        private AppiumElement? BandModeSelector => FindElement(MobileBy.AccessibilityId("ResistanceBandMode"));
        private AppiumElement? BandResistanceSelector => FindElement(MobileBy.AccessibilityId("BandResistance"));
        private AppiumElement? BandPlusWeightSwitch => FindElement(MobileBy.AccessibilityId("BandPlusWeight"));
        private AppiumElement? AdditionalWeightInput => FindElement(MobileBy.AccessibilityId("AdditionalWeight"));
        private AppiumElement? BandProgressionLabel => FindElement(MobileBy.AccessibilityId("BandProgression"));
        
        // Cable elements
        private AppiumElement? CableModeSelector => FindElement(MobileBy.AccessibilityId("CableMachineMode"));
        private AppiumElement? CableSystemSelector => FindElement(MobileBy.AccessibilityId("CableSystem"));
        private AppiumElement? PinNumberSelector => FindElement(MobileBy.AccessibilityId("PinNumber"));
        private AppiumElement? CableWeightInput => FindElement(MobileBy.AccessibilityId("CableWeight"));
        private AppiumElement? ConversionInfoLabel => FindElement(MobileBy.AccessibilityId("ConversionInfo"));
        
        // Validation elements
        private AppiumElement? ValidationMessage => FindElement(MobileBy.AccessibilityId("WeightValidation"));
        
        // Actions
        public void OpenWeightOptions()
        {
            WeightOptionsButton?.Click();
            Thread.Sleep(500);
        }
        
        public void SelectWeightType(string type)
        {
            var typeButton = FindElement(MobileBy.XPath($"//XCUIElementTypeButton[@name='{type}']"));
            typeButton?.Click();
        }
        
        // Bodyweight methods
        public void SwitchToWeightedMode()
        {
            WeightedModeButton?.Click();
        }
        
        public void SwitchToAssistedMode()
        {
            AssistedModeButton?.Click();
        }
        
        public void EnterAddedWeight(string weight)
        {
            AddedWeightInput?.Clear();
            AddedWeightInput?.SendKeys(weight);
        }
        
        public void EnterAssistanceWeight(string weight)
        {
            AssistanceWeightInput?.Clear();
            AssistanceWeightInput?.SendKeys(weight);
        }
        
        // Band methods
        public void SelectBandResistance(string resistance)
        {
            BandResistanceSelector?.Click();
            Thread.Sleep(300);
            
            var bandOption = FindElement(MobileBy.XPath($"//XCUIElementTypeButton[@name='{resistance}']"));
            bandOption?.Click();
        }
        
        public void EnableWeightPlusBand(bool enable)
        {
            if (BandPlusWeightSwitch != null)
            {
                var currentState = BandPlusWeightSwitch.GetAttribute("value") == "1";
                if (currentState != enable)
                {
                    BandPlusWeightSwitch.Click();
                }
            }
        }
        
        public void EnterAdditionalWeight(string weight)
        {
            AdditionalWeightInput?.Clear();
            AdditionalWeightInput?.SendKeys(weight);
        }
        
        // Cable methods
        public void SelectCableSystem(string system)
        {
            CableSystemSelector?.Click();
            Thread.Sleep(300);
            
            var systemOption = FindElement(MobileBy.XPath($"//XCUIElementTypeButton[@name='{system}']"));
            systemOption?.Click();
        }
        
        public void SelectPinNumber(string pin)
        {
            PinNumberSelector?.Click();
            Thread.Sleep(300);
            
            var pinOption = FindElement(MobileBy.XPath($"//XCUIElementTypeButton[@name='{pin}']"));
            pinOption?.Click();
        }
        
        public void EnterCableWeight(string weight)
        {
            CableWeightInput?.Clear();
            CableWeightInput?.SendKeys(weight);
        }
        
        // General weight entry
        public void EnterExactWeight(string weight, string unit)
        {
            SwitchUnits(unit);
            WeightInput?.Clear();
            WeightInput?.SendKeys(weight);
        }
        
        public void SwitchUnits(string unit)
        {
            var currentUnit = GetCurrentUnit();
            if (currentUnit != unit)
            {
                UnitToggle?.Click();
            }
        }
        
        // Verifications
        public bool IsAssistanceModeActive()
        {
            return AssistedModeButton != null && 
                   (AssistedModeButton.GetAttribute("selected") == "true" || 
                    AssistanceWeightInput?.Displayed == true);
        }
        
        public bool HasWeightConversion()
        {
            return ConversionInfoLabel != null && ConversionInfoLabel.Displayed;
        }
        
        // Data retrieval
        public string GetDisplayedWeight()
        {
            return WeightDisplay?.Text ?? WeightInput?.Text ?? "";
        }
        
        public string GetProgressionInfo()
        {
            return ProgressionInfo?.Text ?? "";
        }
        
        public List<string> GetAvailableBands()
        {
            var bands = new List<string>();
            
            BandResistanceSelector?.Click();
            Thread.Sleep(300);
            
            var bandOptions = _driver?.FindElements(MobileBy.XPath("//XCUIElementTypeButton[contains(@name, 'Band')]"));
            if (bandOptions != null)
            {
                bands = bandOptions.Select(b => b.Text).ToList();
            }
            
            // Close selector
            BandResistanceSelector?.Click();
            
            return bands;
        }
        
        public string GetBandProgressionInfo()
        {
            return BandProgressionLabel?.Text ?? "";
        }
        
        public List<string> GetPinNumbers()
        {
            var pins = new List<string>();
            
            PinNumberSelector?.Click();
            Thread.Sleep(300);
            
            var pinOptions = _driver?.FindElements(MobileBy.ClassName("XCUIElementTypeButton"));
            if (pinOptions != null)
            {
                pins = pinOptions.Where(p => int.TryParse(p.Text, out _))
                                .Select(p => p.Text)
                                .ToList();
            }
            
            // Close selector
            PinNumberSelector?.Click();
            
            return pins;
        }
        
        public string GetConversionInfo()
        {
            return ConversionInfoLabel?.Text ?? "";
        }
        
        public string GetUserBodyweight()
        {
            return UserBodyweightLabel?.Text ?? "";
        }
        
        public string GetEffectiveWeight()
        {
            return EffectiveWeightLabel?.Text ?? "";
        }
        
        public string GetValidationMessage()
        {
            return ValidationMessage?.Text ?? "";
        }
        
        public string GetCurrentUnit()
        {
            var unitText = UnitToggle?.Text ?? "";
            return unitText.Contains("kg") ? "kg" : "lbs";
        }
        
        // Helper methods
        private AppiumElement? FindElement(By by)
        {
            try
            {
                return _driver?.FindElement(by) as AppiumElement;
            }
            catch (NoSuchElementException)
            {
                return null;
            }
        }
    }
}