using OpenQA.Selenium;
using OpenQA.Selenium.Appium;

namespace DrMuscle.UITests.Pages
{
    public class WelcomePage
    {
        private readonly AppiumDriver _driver;
        
        // Define element locators as properties
        private AppiumElement WelcomeTitle => _driver.FindElement(MobileBy.AccessibilityId("WelcomeTitle"));
        private AppiumElement SignUpButton => _driver.FindElement(MobileBy.AccessibilityId("SignUpButton"));
        private AppiumElement LoginButton => _driver.FindElement(MobileBy.AccessibilityId("LoginButton"));
        
        public WelcomePage(AppiumDriver driver)
        {
            _driver = driver;
        }
        
        public bool IsDisplayed()
        {
            try
            {
                return WelcomeTitle.Displayed;
            }
            catch (NoSuchElementException)
            {
                return false;
            }
        }
        
        public void TapSignUp()
        {
            SignUpButton.Click();
        }
        
        public void TapLogin()
        {
            LoginButton.Click();
        }
        
        public string GetWelcomeText()
        {
            return WelcomeTitle.Text;
        }
    }
}