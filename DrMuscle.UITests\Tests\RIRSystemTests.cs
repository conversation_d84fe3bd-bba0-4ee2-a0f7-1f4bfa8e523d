using NUnit.Framework;
using DrMuscle.UITests.Helpers;
using DrMuscle.UITests.Pages;
using System;
using System.Threading;
using OpenQA.Selenium.Appium;

namespace DrMuscle.UITests.Tests
{
    /// <summary>
    /// Comprehensive tests for the RIR (Reps in Reserve) system
    /// Tests RIR prompts, recommendations, and training mode differences
    /// </summary>
    [TestFixture]
    [Category(TestCategories.CoreFeatures)]
    public class RIRSystemTests : AppiumSetup
    {
        private WorkoutPage workoutPage = null!;
        private RIRPage rirPage = null!;
        private MainPage mainPage = null!;
        
        [SetUp]
        public void TestSetup()
        {
            workoutPage = new WorkoutPage(Driver!);
            rirPage = new RIRPage(Driver!);
            mainPage = new MainPage(Driver!);
            
            // Login and start workout
            LoginAndStartWorkout();
        }
        
        [Test]
        [Order(1)]
        [Description("Tests RIR prompt appears after first work set")]
        public void TEST_RIR_First_Work_Set()
        {
            TestContext.WriteLine("=== Testing RIR Prompt After First Work Set ===");
            
            // Navigate to an exercise
            workoutPage.SelectExerciseByName("Bench Press");
            Thread.Sleep(2000);
            
            // Complete warm-up sets (should NOT trigger RIR)
            TestContext.WriteLine("Completing warm-up sets...");
            
            // Warmup set 1
            workoutPage.EnterSet("10", "45");
            workoutPage.SaveSet();
            Thread.Sleep(2000);
            
            Assert.That(rirPage.IsRIRPromptVisible(), Is.False,
                "RIR prompt should NOT appear after warm-up set");
            
            TakeScreenshot("01-warmup-no-rir");
            
            // Warmup set 2
            workoutPage.EnterSet("5", "95");
            workoutPage.SaveSet();
            Thread.Sleep(2000);
            
            Assert.That(rirPage.IsRIRPromptVisible(), Is.False,
                "RIR prompt should NOT appear after second warm-up set");
            
            // First work set - should trigger RIR
            TestContext.WriteLine("Completing first work set...");
            workoutPage.EnterSet("8", "135");
            workoutPage.SaveSet();
            Thread.Sleep(2000);
            
            // Verify RIR prompt appears
            Assert.That(rirPage.IsRIRPromptVisible(), Is.True,
                "RIR prompt SHOULD appear after first work set");
            
            TakeScreenshot("02-rir-prompt-shown");
            
            // Verify RIR options
            var rirOptions = rirPage.GetRIROptions();
            TestContext.WriteLine($"RIR options: {string.Join(", ", rirOptions)}");
            
            Assert.That(rirOptions.Count, Is.GreaterThanOrEqualTo(5),
                "Should have at least 5 RIR options");
            
            // Test each RIR option visibility
            Assert.That(rirOptions, Does.Contain("0"),
                "Should include RIR 0 (failure)");
            Assert.That(rirOptions, Does.Contain("1"),
                "Should include RIR 1");
            Assert.That(rirOptions, Does.Contain("2"),
                "Should include RIR 2");
            Assert.That(rirOptions, Does.Contain("3"),
                "Should include RIR 3");
            Assert.That(rirOptions, Does.Contain("4"),
                "Should include RIR 4");
            Assert.That(rirOptions, Does.Contain("5+"),
                "Should include RIR 5+ option");
            
            // Select RIR 2
            rirPage.SelectRIR("2");
            Thread.Sleep(1000);
            
            // Verify prompt dismissed and next set recommendation updated
            Assert.That(rirPage.IsRIRPromptVisible(), Is.False,
                "RIR prompt should be dismissed after selection");
            
            TakeScreenshot("03-rir-selected");
        }
        
        [Test]
        [Order(2)]
        [Description("Tests how RIR affects weight recommendations")]
        public void TEST_RIR_Affects_Weight_Recommendation()
        {
            TestContext.WriteLine("=== Testing RIR Effect on Weight Recommendations ===");
            
            // Start with a baseline exercise
            workoutPage.SelectExerciseByName("Squat");
            Thread.Sleep(2000);
            
            // Record initial recommendation
            var initialWeight = workoutPage.GetRecommendedWeight();
            TestContext.WriteLine($"Initial recommended weight: {initialWeight}");
            
            // Complete first work set with high RIR (easy)
            TestContext.WriteLine("Testing high RIR (4-5) - should increase weight");
            workoutPage.EnterSet("10", initialWeight);
            workoutPage.SaveSet();
            Thread.Sleep(2000);
            
            // Select high RIR (indicating it was easy)
            rirPage.SelectRIR("5+");
            Thread.Sleep(2000);
            
            // Check recommendation for next set
            var nextSetWeight = workoutPage.GetRecommendedWeight();
            TestContext.WriteLine($"Next set recommendation after RIR 5+: {nextSetWeight}");
            
            TakeScreenshot("04-high-rir-recommendation");
            
            // Parse weights and compare
            double initial = ParseWeight(initialWeight);
            double next = ParseWeight(nextSetWeight);
            
            Assert.That(next, Is.GreaterThan(initial),
                "Weight should increase after high RIR (easy set)");
            
            // Complete this set with low RIR (hard)
            TestContext.WriteLine("Testing low RIR (0-1) - should maintain or decrease weight");
            workoutPage.SaveSet();
            Thread.Sleep(2000);
            
            // Note: Second work set shouldn't prompt for RIR
            Assert.That(rirPage.IsRIRPromptVisible(), Is.False,
                "RIR prompt should NOT appear after second work set");
            
            // Finish this workout and start next session
            workoutPage.FinishExercise();
            workoutPage.FinishWorkout();
            Thread.Sleep(3000);
            
            // Start new workout to see updated recommendations
            TestContext.WriteLine("Starting next workout to check weight adjustments...");
            workoutPage.StartWorkout();
            Thread.Sleep(2000);
            
            workoutPage.SelectExerciseByName("Squat");
            Thread.Sleep(2000);
            
            var nextWorkoutWeight = workoutPage.GetRecommendedWeight();
            TestContext.WriteLine($"Next workout recommendation: {nextWorkoutWeight}");
            
            TakeScreenshot("05-next-workout-recommendation");
            
            // Now test with very low RIR
            workoutPage.EnterSet("10", nextWorkoutWeight);
            workoutPage.SaveSet();
            Thread.Sleep(2000);
            
            rirPage.SelectRIR("0");
            Thread.Sleep(2000);
            
            TakeScreenshot("06-zero-rir-selected");
            
            // Verify recommendation doesn't increase (or might decrease)
            var afterFailureWeight = workoutPage.GetRecommendedWeight();
            TestContext.WriteLine($"Recommendation after RIR 0: {afterFailureWeight}");
            
            double failure = ParseWeight(afterFailureWeight);
            Assert.That(failure, Is.LessThanOrEqualTo(ParseWeight(nextWorkoutWeight)),
                "Weight should not increase after RIR 0 (failure)");
        }
        
        [Test]
        [Order(3)]
        [Description("Tests RIR behavior in muscle vs strength training modes")]
        public void TEST_RIR_Muscle_vs_Strength_Mode()
        {
            TestContext.WriteLine("=== Testing RIR in Different Training Modes ===");
            
            // First test in muscle building mode
            mainPage.NavigateToSettings();
            Thread.Sleep(2000);
            
            var settingsPage = new SettingsPage(Driver!);
            settingsPage.SelectTrainingMode("Muscle Building");
            Thread.Sleep(1000);
            
            TakeScreenshot("07-muscle-mode-selected");
            
            // Go back to workout
            mainPage.NavigateToHome();
            Thread.Sleep(2000);
            
            // Test exercise in muscle mode
            TestContext.WriteLine("Testing RIR targets in Muscle Building mode");
            workoutPage.SelectExerciseByName("Bicep Curl");
            Thread.Sleep(2000);
            
            // Check recommended RIR range
            var muscleRIRTarget = workoutPage.GetRIRTargetRange();
            TestContext.WriteLine($"Muscle mode RIR target: {muscleRIRTarget}");
            
            Assert.That(muscleRIRTarget, Does.Contain("1-3") | Does.Contain("1 to 3"),
                "Muscle building mode should target RIR 1-3");
            
            // Complete set and check RIR guidance
            workoutPage.EnterSet("12", "30");
            workoutPage.SaveSet();
            Thread.Sleep(2000);
            
            var rirGuidance = rirPage.GetRIRGuidanceText();
            TestContext.WriteLine($"RIR guidance text: {rirGuidance}");
            
            Assert.That(rirGuidance.ToLower(), Does.Contain("muscle") | Does.Contain("hypertrophy"),
                "RIR guidance should mention muscle building");
            
            TakeScreenshot("08-muscle-mode-rir-guidance");
            
            rirPage.SelectRIR("2");
            Thread.Sleep(1000);
            
            // Switch to strength mode
            workoutPage.FinishExercise();
            mainPage.NavigateToSettings();
            Thread.Sleep(2000);
            
            settingsPage.SelectTrainingMode("Strength");
            Thread.Sleep(1000);
            
            TakeScreenshot("09-strength-mode-selected");
            
            // Test in strength mode
            mainPage.NavigateToHome();
            Thread.Sleep(2000);
            
            TestContext.WriteLine("Testing RIR targets in Strength mode");
            workoutPage.SelectExerciseByName("Deadlift");
            Thread.Sleep(2000);
            
            var strengthRIRTarget = workoutPage.GetRIRTargetRange();
            TestContext.WriteLine($"Strength mode RIR target: {strengthRIRTarget}");
            
            Assert.That(strengthRIRTarget, Does.Contain("3-5") | Does.Contain("3 to 5"),
                "Strength mode should target RIR 3-5");
            
            // Complete set
            workoutPage.EnterSet("3", "315");
            workoutPage.SaveSet();
            Thread.Sleep(2000);
            
            rirGuidance = rirPage.GetRIRGuidanceText();
            TestContext.WriteLine($"Strength mode RIR guidance: {rirGuidance}");
            
            Assert.That(rirGuidance.ToLower(), Does.Contain("strength") | Does.Contain("power"),
                "RIR guidance should mention strength training");
            
            TakeScreenshot("10-strength-mode-rir-guidance");
        }
        
        [Test]
        [Order(4)]
        [Description("Tests RIR trends over multiple workouts")]
        public void TEST_RIR_Trends_Over_Time()
        {
            TestContext.WriteLine("=== Testing RIR Trends and Progression ===");
            
            // Simulate multiple workout sessions
            for (int session = 1; session <= 3; session++)
            {
                TestContext.WriteLine($"\n--- Workout Session {session} ---");
                
                if (session > 1)
                {
                    // Start new workout
                    workoutPage.StartWorkout();
                    Thread.Sleep(2000);
                }
                
                workoutPage.SelectExerciseByName("Bench Press");
                Thread.Sleep(2000);
                
                // Get recommended weight
                var recommendedWeight = workoutPage.GetRecommendedWeight();
                TestContext.WriteLine($"Session {session} recommended weight: {recommendedWeight}");
                
                // Complete warm-up
                workoutPage.EnterSet("5", "45");
                workoutPage.SaveSet();
                Thread.Sleep(1000);
                
                // Complete first work set
                workoutPage.EnterSet("8", recommendedWeight);
                workoutPage.SaveSet();
                Thread.Sleep(2000);
                
                // Vary RIR to simulate progression
                string selectedRIR = session switch
                {
                    1 => "3", // Moderate difficulty
                    2 => "2", // Getting harder
                    3 => "1", // Near failure
                    _ => "2"
                };
                
                TestContext.WriteLine($"Selecting RIR: {selectedRIR}");
                rirPage.SelectRIR(selectedRIR);
                Thread.Sleep(1000);
                
                TakeScreenshot($"11-session{session}-rir{selectedRIR}");
                
                // Complete remaining sets
                for (int set = 2; set <= 3; set++)
                {
                    workoutPage.EnterSet("8", recommendedWeight);
                    workoutPage.SaveSet();
                    Thread.Sleep(1000);
                }
                
                workoutPage.FinishExercise();
                
                if (session < 3)
                {
                    workoutPage.FinishWorkout();
                    Thread.Sleep(3000);
                }
            }
            
            // Check if deload is suggested after consistent low RIR
            var deloadSuggestion = workoutPage.GetDeloadSuggestion();
            if (!string.IsNullOrEmpty(deloadSuggestion))
            {
                TestContext.WriteLine($"Deload suggestion: {deloadSuggestion}");
                TakeScreenshot("12-deload-suggestion");
            }
        }
        
        [Test]
        [Order(5)]
        [Description("Tests RIR input methods and UI")]
        public void TEST_RIR_UI_Elements()
        {
            TestContext.WriteLine("=== Testing RIR UI Elements and Interactions ===");
            
            workoutPage.SelectExerciseByName("Overhead Press");
            Thread.Sleep(2000);
            
            // Complete first work set
            workoutPage.EnterSet("10", "95");
            workoutPage.SaveSet();
            Thread.Sleep(2000);
            
            // Test RIR visual indicators
            Assert.That(rirPage.IsRIRPromptVisible(), Is.True);
            
            // Check for visual scale or description
            var rirDescriptions = rirPage.GetRIRDescriptions();
            foreach (var (rir, description) in rirDescriptions)
            {
                TestContext.WriteLine($"RIR {rir}: {description}");
            }
            
            TakeScreenshot("13-rir-descriptions");
            
            // Test RIR 0 (failure) special indicator
            rirPage.HighlightRIR("0");
            Thread.Sleep(500);
            
            var failureWarning = rirPage.GetFailureWarning();
            Assert.That(failureWarning, Is.Not.Empty,
                "RIR 0 should show warning about training to failure");
            
            TakeScreenshot("14-rir-failure-warning");
            
            // Test RIR selection animation/feedback
            rirPage.SelectRIR("3");
            Thread.Sleep(500);
            
            // Verify selection feedback
            var confirmationMessage = rirPage.GetConfirmationMessage();
            Assert.That(confirmationMessage, Does.Contain("RIR 3") | Does.Contain("3 reps"),
                "Should show confirmation of selected RIR");
            
            TakeScreenshot("15-rir-selection-confirmed");
        }
        
        private void LoginAndStartWorkout()
        {
            // Login flow
            Thread.Sleep(3000);
            
            // Navigate to workout
            workoutPage.WaitForStartWorkout();
            workoutPage.StartWorkout();
            Thread.Sleep(2000);
            
            workoutPage.WaitForExerciseList();
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private double ParseWeight(string weightText)
        #pragma warning restore CA1822 // Mark members as static
        {
            // Remove units and parse
            var cleanWeight = weightText.Replace("lbs", "").Replace("kg", "").Trim();
            return double.TryParse(cleanWeight, out double weight) ? weight : 0;
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private void TakeScreenshot(string name)
        {
            TestTimings.TakeScreenshot(Driver, name);
        }
        #pragma warning restore CA1822 // Mark members as static
    }
}