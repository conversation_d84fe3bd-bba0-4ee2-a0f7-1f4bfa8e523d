using NUnit.Framework;
using DrMuscle.UITests.Helpers;
using System;
using System.IO;
using System.Collections.Concurrent;

namespace DrMuscle.UITests.Tests
{
    /// <summary>
    /// Demonstrates the retry functionality for flaky tests
    /// </summary>
    [TestFixture]
    public class RetryDemoTests
    {
        // Use a static concurrent dictionary to maintain attempt counters across retries
        private static readonly ConcurrentDictionary<string, int> AttemptCounters = new ConcurrentDictionary<string, int>();
        private readonly Random _random = new Random();
        
        [SetUp]
        public void Setup()
        {
            // Initialize counter for the current test if not present
            var testName = TestContext.CurrentContext.Test.Name;
            AttemptCounters.TryAdd(testName, 0);
        }
        
        [Test]
        [FlakyTestRetry(3)]
        public void DemoTest_FailsFirstTimeSucceedsOnRetry()
        {
            var testName = TestContext.CurrentContext.Test.Name;
            var attemptCounter = AttemptCounters.AddOrUpdate(testName, 1, (_, count) => count + 1);
            TestContext.WriteLine($"Attempt #{attemptCounter}");
            
            // This test will fail on first attempt, succeed on second
            if (attemptCounter < 2)
            {
                Assert.Fail($"Simulated failure on attempt {attemptCounter}");
            }
            
            Assert.Pass("Test succeeded!");
        }
        
        [Test]
        [FlakyTestRetry(5)]
        public void DemoTest_RandomlyFails()
        {
            var testName = TestContext.CurrentContext.Test.Name;
            var attemptCounter = AttemptCounters.AddOrUpdate(testName, 1, (_, count) => count + 1);
            TestContext.WriteLine($"Random test attempt #{attemptCounter}");
            
            // 50% chance of failure
            if (_random.Next(2) == 0)
            {
                Assert.Fail("Random failure occurred");
            }
            
            Assert.Pass("Random test succeeded!");
        }
        
        [Test]
        [FlakyTestRetry(2)]
        [Ignore("Demo test showing retry behavior - not needed for actual testing")]
        public void DemoTest_CreatesTempFiles()
        {
            var testName = TestContext.CurrentContext.Test.Name;
            var attemptCounter = AttemptCounters.AddOrUpdate(testName, 1, (_, count) => count + 1);
            var tempDir = Path.GetTempPath();
            if (string.IsNullOrEmpty(tempDir))
            {
                tempDir = Directory.GetCurrentDirectory();
            }
            
            var testFile = Path.Combine(tempDir, $"test_{attemptCounter}.txt");
            
            TestContext.WriteLine($"Creating file: {testFile}");
            File.WriteAllText(testFile, $"Test run {attemptCounter}");
            
            // Simulate occasional file system issues - fail only on first attempt
            if (attemptCounter == 1)
            {
                // Clean up before failing
                if (File.Exists(testFile))
                {
                    File.Delete(testFile);
                }
                Assert.Fail("Simulated file system error");
            }
            
            // On second attempt, file should exist
            Assert.That(File.Exists(testFile), Is.True);
            
            // Cleanup
            if (File.Exists(testFile))
            {
                File.Delete(testFile);
            }
        }
        
        [Test]
        [FlakyTestRetry(1)] // Only one retry
        [Ignore("This test is designed to always fail for demonstration purposes")]
        public void DemoTest_AlwaysFails()
        {
            var testName = TestContext.CurrentContext.Test.Name;
            var attemptCounter = AttemptCounters.AddOrUpdate(testName, 1, (_, count) => count + 1);
            TestContext.WriteLine($"This test always fails - attempt {attemptCounter}");
            Assert.Fail("This test is designed to always fail");
        }
        
        [Test]
        [FlakyTestRetry(3)]
        public void DemoTest_ThrowsException()
        {
            var testName = TestContext.CurrentContext.Test.Name;
            var attemptCounter = AttemptCounters.AddOrUpdate(testName, 1, (_, count) => count + 1);
            TestContext.WriteLine($"Exception test - attempt {attemptCounter}");
            
            if (attemptCounter < 3)
            {
                throw new InvalidOperationException($"Simulated exception on attempt {attemptCounter}");
            }
            
            Assert.Pass("No exception thrown this time!");
        }
        
        [Test]
        [Retry(2)]
        public void AppLaunch_WithRetry_ShouldHandleTransientFailures()
        {
            // This test might fail if the simulator is slow to respond
            TestContext.WriteLine("Launching app with retry support...");
            
            try
            {
                SimCtlTestRunner.LaunchApp();
                System.Threading.Thread.Sleep(3000);
                
                var screenshotPath = Path.Combine(
                    TestContext.CurrentContext.WorkDirectory, 
                    "Screenshots",
                    $"retry-demo-{DateTime.Now:yyyyMMdd-HHmmss}.png"
                );
                
                var screenshotDir = Path.GetDirectoryName(screenshotPath);
                if (!string.IsNullOrEmpty(screenshotDir))
                {
                    Directory.CreateDirectory(screenshotDir);
                }
                SimCtlTestRunner.TakeScreenshot(screenshotPath);
                
                Assert.That(File.Exists(screenshotPath), Is.True, 
                    "Screenshot should be captured after app launch");
            }
            catch (Exception ex)
            {
                TestContext.WriteLine($"Error during test: {ex.Message}");
                throw;
            }
        }
        
        [Test]
        [Retry(3)]
        public void SimulatorInteraction_WithRetry_ShouldBeResilient()
        {
            // Test that demonstrates retry helping with simulator timing issues
            var deviceId = SimCtlTestRunner.GetBootedDeviceId();
            Assert.That(deviceId, Is.Not.Null.And.Not.Empty, 
                "Should find a booted simulator device");
            
            // Sometimes the simulator might not be fully ready
            var appInstalled = SimCtlTestRunner.IsAppInstalled();
            
            if (!appInstalled)
            {
                TestContext.WriteLine("App not installed, attempting to install...");
                var appPath = Environment.GetEnvironmentVariable("IOS_APP_BUNDLE");
                if (!string.IsNullOrEmpty(appPath) && Directory.Exists(appPath))
                {
                    SimCtlTestRunner.InstallApp(appPath);
                    // Give it time to complete
                    System.Threading.Thread.Sleep(2000);
                }
            }
            
            Assert.That(SimCtlTestRunner.IsAppInstalled(), Is.True, 
                "App should be installed on simulator");
        }
        
        [Test]
        public void TestWithoutRetry_ForComparison()
        {
            // This test doesn't use retry, for comparison
            TestContext.WriteLine("Running test without retry support");
            
            Assert.That(SimCtlTestRunner.IsAvailable(), Is.True,
                "SimCtl should be available");
        }
        
        [TearDown]
        public void TearDown()
        {
            // Clean up counter after successful test completion
            var testName = TestContext.CurrentContext.Test.Name;
            if (TestContext.CurrentContext.Result.Outcome.Status == NUnit.Framework.Interfaces.TestStatus.Passed)
            {
                AttemptCounters.TryRemove(testName, out _);
            }
        }
    }
}