using OpenQA.Selenium;
using OpenQA.Selenium.Appium;
using OpenQA.Selenium.Support.UI;
using System;
using System.Collections.Generic;

namespace DrMuscle.UITests.Pages
{
    /// <summary>
    /// Page object for the onboarding walkthrough screens
    /// </summary>
    public class OnboardingPage
    {
        private readonly AppiumDriver? _driver;
        private readonly WebDriverWait _wait;
        
        public OnboardingPage(AppiumDriver driver)
        {
            _driver = driver ?? throw new ArgumentNullException(nameof(driver));
            _wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
        }
        
        // Main elements
        private AppiumElement? Carousel => FindElement(MobileBy.AccessibilityId("OnboardingCarousel"));
        private AppiumElement? Indicator => FindElement(MobileBy.AccessibilityId("OnboardingIndicator"));
        private AppiumElement? GetStartedButton => FindElement(MobileBy.AccessibilityId("GetStartedButton"));
        
        // Page specific elements
        private AppiumElement? Page1 => FindElement(MobileBy.AccessibilityId("OnboardingPage1"));
        private AppiumElement? Page2 => FindElement(MobileBy.AccessibilityId("OnboardingPage2"));
        
        // Chat-based onboarding elements
        private AppiumElement? ChatList => FindElement(MobileBy.AccessibilityId("OnboardingChatList"));
        private AppiumElement? OptionsStack => FindElement(MobileBy.AccessibilityId("OnboardingOptionsStack"));
        private AppiumElement? TermsLink => FindElement(MobileBy.AccessibilityId("OnboardingTermsLink"));
        private AppiumElement? PrivacyLink => FindElement(MobileBy.AccessibilityId("OnboardingPrivacyLink"));
        
        // Actions
        public void SwipeToNextPage()
        {
            if (_driver == null) return;
            
            var size = _driver.Manage().Window.Size;
            var startX = size.Width * 0.8;
            var endX = size.Width * 0.2;
            var y = size.Height / 2;
            
            PerformSwipe(startX, y, endX, y);
        }
        
        public void SwipeToPreviousPage()
        {
            if (_driver == null) return;
            
            var size = _driver.Manage().Window.Size;
            var startX = size.Width * 0.2;
            var endX = size.Width * 0.8;
            var y = size.Height / 2;
            
            PerformSwipe(startX, y, endX, y);
        }
        
        public void TapGetStarted()
        {
            GetStartedButton?.Click();
        }
        
        public void TapTermsOfUse()
        {
            TermsLink?.Click();
        }
        
        public void TapPrivacyPolicy()
        {
            PrivacyLink?.Click();
        }
        
        public void SelectChatOption(string optionText)
        {
            // Find and tap option in the options stack
            var option = FindElement(By.XPath($"//XCUIElementTypeStaticText[@name='{optionText}']"));
            option?.Click();
        }
        
        // Verifications
        public bool IsDisplayed()
        {
            return Carousel != null || ChatList != null;
        }
        
        public bool IsGetStartedButtonVisible()
        {
            return GetStartedButton != null && GetStartedButton.Displayed;
        }
        
        public bool IsOnPage1()
        {
            return Page1 != null && Page1.Displayed;
        }
        
        public bool IsOnPage2()
        {
            return Page2 != null && Page2.Displayed;
        }
        
        public int GetCurrentPageIndex()
        {
            try
            {
                // Try to get position from carousel
                var position = Carousel?.GetAttribute("position");
                if (!string.IsNullOrEmpty(position) && int.TryParse(position, out int index))
                {
                    return index;
                }
                
                // Alternative: check which page is visible
                if (IsOnPage1()) return 0;
                if (IsOnPage2()) return 1;
                
                return -1;
            }
            catch
            {
                return -1;
            }
        }
        
        public void WaitForPageToLoad()
        {
            _wait.Until(d => IsDisplayed());
        }
        
        public void WaitForGetStartedButton()
        {
            _wait.Until(d => IsGetStartedButtonVisible());
        }
        
        // Helper methods
        private AppiumElement? FindElement(By by)
        {
            try
            {
                return _driver?.FindElement(by) as AppiumElement;
            }
            catch (NoSuchElementException)
            {
                return null;
            }
        }
        
        private void PerformSwipe(double startX, double startY, double endX, double endY)
        {
            if (_driver == null) return;
            
            try
            {
                // Use mobile: swipe command for iOS
                _driver.ExecuteScript("mobile: swipe", new Dictionary<string, object>
                {
                    { "startX", startX },
                    { "startY", startY },
                    { "endX", endX },
                    { "endY", endY },
                    { "duration", 0.5 }
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Swipe failed: {ex.Message}");
                // Alternative: Try to use scroll if swipe is not supported
                try
                {
                    _driver.ExecuteScript("mobile: scroll", new Dictionary<string, object>
                    {
                        { "direction", startX > endX ? "left" : "right" }
                    });
                }
                catch
                {
                    Console.WriteLine("Neither swipe nor scroll is supported");
                }
            }
        }
    }
}