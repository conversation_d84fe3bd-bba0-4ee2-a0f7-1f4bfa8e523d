# Dr. Muscle Apple Watch App - Project Status

## Summary & Current State

*   **Goal:** Build and distribute an **independent companion watchOS app**. This means the watch app can be installed and run without its companion iOS app, but is still submitted to the App Store as a single purchase.
*   **Status:** The build workflow and project settings have been corrected to align with Apple's requirements for this app type. The app is now ready for a clean build, archive, and TestFlight upload attempt.
*   **Current Date:** July 3, 2025

### Current Status: v1.1 → v1.2 Merge Build Issues Resolved

*   **Task ID:** MERGE-BUILD-01
*   **Status:** Completed Successfully ✅
*   **Completion Time:** June 26, 2025
*   **Description:** All compilation errors from v1.1 → v1.2 merge have been resolved

#### Resolution Summary:
1. **DrMuscleAPIClient Compilation**: Fixed duplicate ID preventing compilation
2. **Xcode "Damaged" Project**: Resolved secondary ID conflict with Debug configuration
3. **Build Status**: ✅ Project now builds successfully in GitHub Actions

#### Next Steps:
- Complete remaining merge phases per merge.md
- Proceed with feature development on stable v1.2 branch

### Current Task: Update LoginView Text

*   **Task ID:** UI-UPDATE-01
*   **Status:** Completed ✅
*   **Start Time:** December 13, 2024
*   **Completion Time:** December 13, 2024
*   **Description:** Update LoginView text from "IT'S TIME TO LIFT" to "AI Coach" and "Real Gains"

#### Implementation Summary:
1. **TDD Phase:** ✅
   - Created tests in LoginViewTests.swift:
     - testLoginViewDisplaysAICoachTitle - Tests for "AI Coach" text
     - testLoginViewDisplaysRealGainsSubtitle - Tests for "Real Gains" text
     - testLoginViewTitleStyling - Tests that styling is maintained
   - Updated testing.md with new failing tests

2. **Implementation Phase:** ✅
   - Updated LoginView.swift to display "AI Coach" and "Real Gains"
   - Maintained Nike-style heavy font styling
   - All tests are now passing

### Current Task: Debug Authentication "Sign-Up not completed" Error

*   **Task ID:** AUTH-DEBUG-01
*   **Status:** In Progress 🔄
*   **Start Time:** December 13, 2024
*   **Description:** Debug and fix "Sign-Up not completed" error that persists after authentication implementation

#### Issues Identified:
1. **Error Source**: The error comes from the backend API, not the watch app
2. **Poor Error Messaging**: Generic "Sign-Up not completed" doesn't help users understand what to do
3. **Missing Parameters**: iOS app sends bodyweight/massunit which might be required
4. **Root Cause Found**: User has existing Dr. Muscle account with same email as Apple ID - backend doesn't support account linking
5. **Instant Error**: Error appears instantly without network delay, suggesting possible mock implementation or cached state

#### IMPORTANT: Debugging Constraints
- **NO XCODE CONSOLE ACCESS**: User only has actual watch device and GitHub Actions runner
- **Cannot see console logs**: All debugging must be done through:
  - DiagnosticLoginView on the actual watch screen
  - TestFlight builds with visible debug information
  - GitHub Actions test results
- **Do NOT suggest**: "Check Xcode console", "Look at console logs", "Monitor debug output"

#### Implementation Completed:
1. **Enhanced Error Handling:** ✅
   - Added user-friendly error message mapping in AuthenticationManager
   - "Sign-Up not completed" → "Your account setup is incomplete. Please try signing in again or contact support."
   - Added specific handling for common errors (401, 500, network issues)

2. **Improved Logging:** ✅
   - Added full error response body logging in DrMuscleAPIClient
   - Added OAuth error format parsing
   - Better debugging information for backend responses

3. **Diagnostic Login View:** ✅
   - Created DiagnosticLoginView with real-time debug information
   - Shows authentication flow progress with timestamps
   - Captures and displays backend errors with explanations
   - Temporarily enabled in ContentView for debugging

4. **Parameter Updates:** ✅
   - Added default bodyWeight: "60" (matching iOS app)
   - Added default massUnit: "kg" (matching iOS app)
   - These parameters might be required by backend

#### Debugging Guide Created:
- See `/docs/authentication-debugging-guide.md` for instructions
- Explains how to use diagnostic mode
- Lists common error patterns and solutions

#### Recent Updates:
1. **Parameter Fix Applied:** ✅
   - Changed bodyWeight/massUnit from "60"/"kg" to null (matching mobile app)
   - Mobile app passes null for these parameters during login
   - Enhanced error logging to capture raw backend responses

### MAUI App Test Automation Updates

*   **Task ID:** MAUI-TEST-01
*   **Status:** Completed ✅
*   **Completion Time:** July 3, 2025
*   **Description:** Created integrated build and test workflow for MAUI app

### MAUI Test Workflow Fixes

*   **Task ID:** MAUI-TEST-FIX-01
*   **Status:** Completed ✅
*   **Completion Time:** July 4, 2025
*   **Description:** Fixed GitHub Actions test workflow failures

#### Issues Fixed:
1. **Test Reporter Permissions Error**: ✅
   - Error: "HttpError: Resource not accessible by integration"
   - Root Cause: dorny/test-reporter@v1 lacks permissions to create checks on push events
   - Solution: Split reporting - use test reporter for PRs, artifact upload for pushes

2. **Appium Server Startup Issues**: ✅
   - Added port 4723 availability check before starting
   - Implemented retry logic (3 attempts) for server startup
   - Enhanced health check to verify server is truly ready
   - Added Appium log capture on failures for debugging

3. **Configuration Clarification**: ✅
   - Confirmed correct usage of `AddAdditionalOption` method in Appium.WebDriver 5.0.0
   - No changes needed to AppiumSetup.cs

#### Implementation Details:
- Modified `.github/workflows/maui-test-workflow.yml`
- Test results now properly reported for both push and PR events
- Appium server management is more robust with automatic retry

#### Implementation Summary:
1. **New Workflow Created:** ✅
   - Created `maui-build-and-test-workflow.yml` combining build and test functionality
   - Maintains existing build capabilities while adding integrated testing
   - Workflow available on main branch for feature branch execution

2. **Key Features:** ✅
   - **Immediate Feedback**: Download links output as soon as builds complete
   - **Parallel Testing**: UI tests run in parallel with download availability
   - **Configurable Inputs**: 
     - `platform`: iOS & Android, iOS, or Android
     - `run-tests`: Boolean to enable/disable tests
     - `test-filter`: Optional test filter string
   - **Resource Optimization**: Tests attempt to reuse iOS app from build job

3. **Workflow Structure:** ✅
   ```
   setup → check-runner → wake-runner → build-ios/build-android (parallel)
                                                |
                                         download-links (immediate)
                                                |
                                         ios-ui-tests (parallel)
                                                |
                                         test-summary → performance-metrics
   ```

4. **Benefits:** ✅
   - Developers get build artifacts immediately
   - Tests run in background without blocking access
   - Single workflow for complete CI/CD pipeline
   - Enhanced metrics include test execution data

2. **Root Cause Identified:** ✅ (January 27, 2025)
   - User has existing Dr. Muscle account with same email as Apple ID
   - Backend doesn't support linking Apple Sign In to existing email accounts
   - Created workaround documentation using "Hide My Email" feature
   - Backend specification created for account linking implementation

3. **Enhanced Debugging Added:** ✅ (January 27, 2025)
   - Added timing diagnostics to track network request durations
   - Added timestamps to authentication flow for instant error detection
   - Enhanced parameter logging to capture exact Apple credential data
   - All changes visible in DiagnosticLoginView (no console access)

#### Current Status:
- **Tests Pass**: All 4 authentication tests passing in CI/CD
- **Device Fails**: Actual watch still shows "Sign-Up not completed" error
- **Instant Error**: Error appears without network delay, suggesting:
  - Possible mock implementation still active
  - Cached authentication state
  - Network connectivity issue on watch
- **Workaround Available**: Use Apple's "Hide My Email" to create new account

#### Next Steps:
1. **Deploy Timing Diagnostics Build:**
   - Latest build includes timing information visible in DiagnosticLoginView
   - Will show if network requests are actually being made
   - Will display request/response times to identify instant errors
   
2. **Try Hide My Email Workaround:**
   - When signing in, choose "Hide My Email" option
   - Creates new account with relay email address
   - Documented in `/DrMuscleWatch/docs/apple-signin-workaround-guide.md`

3. **Backend Implementation:**
   - Share `/DrMuscleWatch/docs/backend-account-linking-spec.md` with backend team
   - Implement auto-linking for existing email accounts
   - Estimated timeline: 2-4 weeks

### Previous Task: DrMuscleAPIClient Compilation Error Fixed

*   **Task ID:** API-CLIENT-02
*   **Status:** Completed
*   **Description:** Fixed DrMuscleAPIClient.swift compilation by resolving ID conflict

#### Solution Summary:
- Removed duplicate iOS app entry
- Assigned unique ID `1A2B3C4D5E6F7890ABCDEF90` to DrMuscleAPIClient.swift
- This ID was then reused, causing the "damaged" project error above

### Previous Task: Merge v1.1 → v1.2 Authentication Issues Resolution

*   **Task ID:** MERGE-01
*   **Status:** Completed
*   **Start Time:** June 26, 2025
*   **Description:** Resolved compilation errors reported during v1.1 → v1.2 merge related to DrMuscleAPIClient visibility

#### Resolution Summary:
- **DrMuscleAPIClient Scope Error**: Fixed by resolving Xcode project file ID conflict (see XCODE-01)
- **Email/Password Authentication**: No such code exists in v1.2 - correctly implements Sign in with Apple only
- **Warnings Fixed**: 
  - Fixed unused token warning in AuthenticationManager by checking token validity without storing unused variable
  - StorageService batchDelete warning already addressed in v1.2 code
- **Conclusion**: v1.2 branch now has correct configuration with all compilation errors resolved

### Previous Task: Authentication Debugging with Automated Testing

*   **Task ID:** AUTH-DEBUG-02
*   **Status:** Completed ✅
*   **Start Time:** January 27, 2025
*   **Completion Time:** January 27, 2025
*   **Description:** Setting up automated testing to debug persistent authentication errors

#### Resolution Summary:
1. **Diagnostic View Enhancement**: ✅
   - Created API log capture using NotificationCenter
   - Enhanced DrMuscleAPIClient with request/response logging
   - Attempted to use EnhancedDiagnosticView (reverted due to build error)

2. **Automated Testing Implementation**: ✅
   - Created `AuthenticationIntegrationTests.swift` for OAuth endpoint testing
   - Created `APIMockTests.swift` for JSON format validation
   - Updated GitHub Actions workflow with test execution step
   - Added comprehensive error reporting in CI/CD summary

3. **Test Configuration Fixed**: ✅
   - Added test target to project.pbxproj
   - Configured PBXNativeTarget "DrMuscleWatchAppTests"
   - Added test file references and build phases
   - Tests now run successfully in CI/CD

#### Test Results (January 27, 2025):
- **Total Tests Run**: 4
- **Passed**: 4 ✅
- **Failed**: 0
- **Test Suite**:
  - Parameter Order & Encoding Tests: ✅ PASSED
  - API Client Implementation Test: ✅ PASSED
  - Live API Endpoint Test: ✅ PASSED
  - Documentation Compliance Check: ✅ PASSED

#### Key Findings:
- Authentication implementation matches apple-auth-integration.md requirements
- OAuth /token endpoint format is correct
- Parameters are in correct order with legacy `provider=google` requirement
- Build and TestFlight upload completed successfully

### Previous Task: Testing and Validation

*   **Task ID:** TEST-01
*   **Status:** Blocked ⚠️
*   **Start Time:** June 26, 2025
*   **Description:** Testing authentication implementation and running comprehensive test suite

#### Testing Status:
1. **Manual Authentication Testing**:
   - [x] Sign in button displays correctly (yellow gradient, Apple logo, "Sign in" text) ✅
   - [ ] Tapping button triggers Sign in with Apple flow
   - [ ] Authentication succeeds for existing users (with provider=google fix)
   - [ ] Authentication creates account for new users (with provider=google fix)
   - [ ] Error handling works for cancelled/failed authentication

2. **Automated Test Suite**:
   - [x] Create basic authentication tests ✅
   - [ ] Configure test target in Xcode project (BLOCKED)
   - [ ] Run test-quick.sh for rapid validation
   - [ ] Run run-all-tests.sh for comprehensive results
   - [ ] All authentication-related tests pass
   - [ ] No regression in existing functionality

#### Authentication Implementation Summary:
- Fixed "Sign-Up Not Completed" error by matching iOS app implementation
- iOS app uses `GoogleLogin()` method with `provider=google` for both Google and Apple Sign In
- Backend distinguishes by checking if `userid` is populated (Apple) or `accesstoken` has value (Google)
- This is a legacy implementation where Google OAuth flow was reused for Apple Sign In
- See ADR-0008 for complete documentation of this counterintuitive but functional approach

#### Previous Task: Sign in with Apple Authentication Issues

*   **Task ID:** AUTH-02
*   **Status:** Completed ✅
*   **Start Time:** June 26, 2025
*   **Completion Time:** June 26, 2025
*   **Description:** Fixed Sign in with Apple authentication by matching iOS app implementation

#### Issues Identified and Fixes Applied:

1. **Button Design Issue**: ✅ Fixed
   - Problem: Button was black instead of yellow gradient per Dr. Muscle design spec
   - Solution: Updated LoginView.swift to use yellow gradient (#DFE769 to #E9FF97) with Apple logo

2. **API URL Mismatch**: ✅ Fixed
   - Problem: WatchOS app was using incorrect API URL (dr-muscle-api.azurewebsites.net)
   - Solution: Updated DrMuscleAPIClient.swift to use correct URL (drmuscle.azurewebsites.net)

3. **Missing Entitlements**: ✅ Fixed
   - Problem: Sign in with Apple capability not configured
   - Solution: Created entitlements files for both watchOS and iOS targets

4. **Authentication Flow Error**: ✅ Fixed
   - Problem: "Sign-up Not completed" error after tapping Sign in with Apple
   - Root Cause: Watch app was using wrong API endpoint and data format
   - Investigation Results (June 26, 2025):
     - iOS app analysis revealed authentication uses `/token` endpoint, NOT `/api/Account/RegisterWithApple`
     - iOS app sends form-urlencoded data, NOT JSON
     - Required parameters: grant_type, accesstoken, provider, email, name, bodyweight, massunit, userid
     - The `userid` (Apple's user ID) is critical for authentication when email is not available
   - Resolution:
     - Updated DrMuscleAPIClient to use `/token` endpoint with form-urlencoded POST
     - Added all required parameters matching iOS app implementation
     - Added user existence checks before authentication
     - Created proper models for OAuth response (LoginSuccessResult)
     - See ADR-0008 for detailed documentation of the fix

5. **Button Label Update**: ✅ Fixed (June 26, 2025)
   - Changed from "Sign in with Apple" to "Sign in"
   - Increased font size from 16pt to 18pt for better readability

### Previous Task: Sign in with Apple Authentication Implementation

*   **Task ID:** AUTH-01 (Redux)
*   **Status:** Resumed - Fixing Implementation Issues
*   **Start Time:** December 13, 2024, 10:00 AM EST
*   **Description:** Implementing proper Sign in with Apple authentication to replace the temporary "Quick Sign In" mock implementation that was added during initial deployment.

#### Implementation Plan:
1. **TDD Phase (Current):**
   - Created `AuthenticationManagerTests.swift` with tests for:
     - Initial authentication state
     - Handling successful Sign in with Apple
     - Handling authentication errors
     - Token storage and retrieval
     - ASAuthorizationControllerDelegate methods
   - Created `LoginViewTests.swift` with tests for:
     - Sign in with Apple button display
     - Button styling and interaction
     - Error message display
     - Loading state handling
   - Created `LoginViewModelTests.swift` with tests for:
     - Sign in flow initiation
     - Success/error handling
     - Backend integration
     - Authorization request creation

2. **Next Steps:**
   - Implement ASAuthorizationControllerDelegate in AuthenticationManager
   - Update LoginView to use real Sign in with Apple button
   - Update LoginViewModel to handle authentication flow
   - Integrate with backend API endpoint `/api/Account/RegisterWithApple`
   - Remove mock authentication code
   - Ensure all tests pass

### Required Configuration for Independent Companion App

Based on Apple's official documentation and extensive debugging, the following configuration is required for a successful App Store submission:

#### 1. Xcode Project Settings

The project must be configured to define the watch app's independence and ensure it's correctly embedded within the iOS app archive.

*   **Watch App Target (`DrMuscleWatchApp`):**
    *   In the `General > Deployment Info` section, the **"Supports Running Without iOS App Installation"** option must be **checked (enabled)**.
    *   This sets the `WKRunsIndependentlyOfCompanionApp` key to `YES` in the watch app's `Info.plist`, signaling to the system that it can run standalone.
    *   In `Build Settings`, `SKIP_INSTALL` must be set to `YES`. This prevents the watch app from being installed as a top-level product in the archive, ensuring it is instead embedded within the iOS app.

*   **iOS App Target (`DrMuscleWatchApp-iOS`):**
    *   In `Build Settings`, `SKIP_INSTALL` must be set to `NO`. This ensures the iOS app is correctly placed in the `Products/Applications` directory of the final archive, making it the primary application for App Store processing.

#### 2. Build & Submission Workflow (e.g., GitHub Actions)

The CI/CD pipeline must archive the iOS container, not the watch app directly.

*   **Archive Command:** The `xcodebuild archive` command must target the **iOS scheme** (e.g., `DrMuscleWatchApp-iOS`) and the **iOS platform** (`-destination "generic/platform=iOS"`).
*   **Upload Command:** The upload tool (`xcrun altool` or `notarytool`) must specify the platform as **`ios`**, as it is uploading an iOS app archive.

## Next Steps

1.  **Execute the Workflow:** Run the corrected build and upload workflow.
2.  **Verify in App Store Connect:** After a successful upload, confirm that App Store Connect recognizes both the iOS and watchOS versions of the app and that the watch app is listed as independent.
3.  **Test on Device:** Install the app via TestFlight on an Apple Watch (without the iOS app installed) to confirm it runs independently.

---

## Final Working Configuration (June 24, 2025)

After extensive debugging, the following configuration successfully builds and prepares an independent companion watchOS app for TestFlight:

### Xcode Project Configuration

*   **Watch App Target (`DrMuscleWatchApp`):**
    *   `INFOPLIST_KEY_WKRunsIndependentlyOfCompanionApp = YES` (enables independence)
    *   `INFOPLIST_KEY_WKWatchOnly` is NOT set (removed - this is critical)
    *   `INFOPLIST_KEY_LSApplicationLaunchProhibited = YES`
    *   `SKIP_INSTALL = YES`
    *   Bundle ID: `com.drmaxmuscle.max.watchkitapp`

*   **iOS App Target (`DrMuscleWatchApp-iOS`):**
    *   `SKIP_INSTALL = NO`
    *   Bundle ID: `com.drmaxmuscle.max`
    *   Embeds the watchOS app in the `/Watch` directory

### GitHub Actions Workflow

*   **Scheme:** `DrMuscleWatchApp-iOS` (the iOS scheme, not the watch scheme)
*   **Archive Destination:** `generic/platform=iOS` (not watchOS)
*   **Upload Type:** `-t ios` for TestFlight upload
*   **Export:** Creates an IPA file containing the iOS app with embedded watchOS app

This configuration creates an independent companion watchOS app that:
- Can be installed and run without the iOS app
- Is distributed through the App Store as part of an iOS app bundle
- Appears as a single app in App Store Connect with both iOS and watchOS versions

---

## Historical Log & Previous Tasks

<details>
<summary>Click to expand the detailed log of previous tasks and debugging steps.</summary>

### TestFlight Upload Issues (Resolved)

The primary issue was a series of misconfigurations that led to errors like "Unable to determine app platform for 'Undefined' software type. (1194)". The root cause was a misunderstanding of the build and packaging requirements for independent watchOS apps.

**Key Learnings:**
*   An independent watchOS app must still be wrapped in an iOS app for App Store submission.
*   The `SKIP_INSTALL` setting is critical for controlling which target becomes the top-level product in the archive.
*   The `Supports Running Without iOS App Installation` (`WKRunsIndependentlyOfCompanionApp`) project setting defines the app's behavior on-device and in the App Store, but does not change the requirement to submit via an iOS archive.

#### Chronological Fixes (June 2025)
*   **2025-06-25 00:20:00 GMT**: Added encryption declaration to skip App Store submission questions:
    - Added `INFOPLIST_KEY_ITSAppUsesNonExemptEncryption = NO` to all build configurations (iOS Debug/Release, watchOS Debug/Release)
    - This automatically declares that the app doesn't use encryption, eliminating the need to answer encryption questions for each submission
    - Follows Apple's recommendation to add this Info.plist key for apps that only use Apple's built-in encryption (Keychain, HTTPS/TLS, Sign in with Apple)
    - Future TestFlight uploads will skip the encryption compliance questionnaire
*   **2025-06-25 00:15:00 GMT**: Fixed iOS AppIcon asset dimensions causing build failure:
    - Replaced incorrectly sized icon files with properly dimensioned ones from main Dr. Muscle app
    - Critical fixes: icon-120.png (was 100x100, now 120x120), icon-152.png (now correctly 152x152)
    - Fixed icon-1024.png, icon-76.png, icon-80.png, icon-29.png, icon-40.png, icon-58.png, icon-87.png
    - Removed extra unused icon files (icon100.png, icon172.png, etc.) to prevent confusion
    - This resolves the "The stickers icon set or app icon set named 'AppIcon' did not have any applicable content" error
    - All required iOS icon sizes now have valid PNG files with correct or close-enough dimensions
*   **2025-06-25 00:00:00 GMT**: Fixed iOS Assets.xcassets path reference causing build failure:
    - Changed PBXFileReference path from "iOS/Assets.xcassets" to "Assets.xcassets"
    - Resolves "iOS/iOS/Assets.xcassets" double path issue that was causing build to fail
    - The path is relative to the iOS group, so "Assets.xcassets" is the correct relative path
    - This fixes the error: "None of the input catalogs contained a matching stickers icon set or app icon set named 'AppIcon'"
*   **2025-06-24 23:45:00 GMT**: Fixed iOS target missing Assets.xcassets causing TestFlight validation errors:
    - Added iOS Assets.xcassets file reference to iOS target's Resources section
    - Created proper PBXBuildFile and PBXFileReference entries in project.pbxproj
    - This resolves the "Missing required app icon for iPhone/iPod Touch of exactly 120x120 pixels" error
    - This resolves the "Missing required app icon for iPad of exactly 152x152 pixels" error
    - The CFBundleIconName was already configured, but the iOS target couldn't find the AppIcon asset set
    - All required iOS icon sizes (120x120, 152x152) are present and properly configured in Contents.json
*   **2025-06-24 23:30:00 GMT**: Fixed AppIcon asset catalog naming inconsistencies:
    - Renamed icon files to match Contents.json references (added hyphens to icon48.png → icon-48.png, etc.)
    - This resolves the "The stickers icon set or app icon set named 'AppIcon' did not have any applicable content" build error
    - All required watchOS icon sizes are now properly referenced and available for compilation
*   **2025-06-24 23:00:00 GMT**: Fixed DEVELOPMENT_ASSET_PATHS build error:
    - Corrected watchOS target's DEVELOPMENT_ASSET_PATHS from "iOS/Preview Content" back to "DrMuscleWatchApp/Preview Content"
    - Created missing iOS/Preview Content directory to prevent future path issues
    - This resolves the build error: "One of the paths in DEVELOPMENT_ASSET_PATHS does not exist"
*   **2025-06-24 22:45:00 GMT**: Created complete icon asset sets and added CFBundleIconFiles:
    - Created all required iOS icon sizes (20x20 to 1024x1024) in iOS/Assets.xcassets/AppIcon.appiconset
    - Created all required watchOS icon sizes (24x24 to 1024x1024) in DrMuscleWatchApp/Assets.xcassets/AppIcon.appiconset
    - Added `INFOPLIST_KEY_CFBundleIconFiles` to watchOS target with app launcher icon references
    - Updated Contents.json files with proper platform-specific icon configurations
    - This should resolve all missing icon validation errors for both platforms
*   **2025-06-24 22:15:00 GMT**: Fixed TestFlight validation errors by adding missing Info.plist keys:
    - Added `INFOPLIST_KEY_CFBundleIconName = AppIcon` to both iOS and watchOS targets
    - Added `INFOPLIST_KEY_WKCompanionAppBundleIdentifier = com.drmaxmuscle.max` to watchOS target
    - Copied Assets.xcassets to iOS target to provide required app icons
    - This resolves validation errors for missing CFBundleIconName and WKCompanionAppBundleIdentifier
*   **2025-06-24 21:30:00 GMT**: Fixed export error by converting from watch-only app to independent companion app:
    - Removed `INFOPLIST_KEY_WKWatchOnly = YES` from both Debug and Release configurations in project.pbxproj
    - Kept `INFOPLIST_KEY_WKRunsIndependentlyOfCompanionApp = YES` for independent functionality
    - Updated GitHub Actions workflow to use iOS scheme `DrMuscleWatchApp-iOS` instead of watch scheme
    - This resolves the "exportArchive exportOptionsPlist error for key 'method' expected one {} but found app-store" error
*   **2025-06-24 09:00:00 GMT**: Implemented critical `SKIP_INSTALL` build setting corrections in `project.pbxproj` for both iOS stub (`NO`) and Watch App (`YES`) targets, ensuring correct embedding of the watch app within the iOS stub archive.
*   **2025-06-24 09:05:00 GMT**: Removed redundant `SKIP_INSTALL=NO` override from the `xcodebuild archive` command in the GitHub Actions workflow, allowing Xcode's per-target settings to take effect.
*   **2025-06-23 00:28:00**: IMPLEMENTED FIX: Added INFOPLIST_KEY_WKRunsIndependentlyOfCompanionApp=YES to convert dependent watchOS app to independent per Apple docs, should resolve "SoftwareTypeEnum = Undefined" issue
*   **2025-06-23 00:25:00**: UPDATED APPROACH: Based on Apple docs, need to convert dependent watchOS app to independent by enabling "Supports Running Without iOS App Installation" option in Xcode project settings instead of creating watch-only app
*   **2025-06-23 00:22:00**: IDENTIFIED ROOT CAUSE: App Store Connect app record for com.drmaxmuscle.max has "SoftwareTypeEnum = Undefined" - need to fix/recreate app record as iOS platform for standalone watchOS app
*   **2025-06-22 23:50:00**: Fixed standalone watchOS app archiving by switching to iOS stub scheme, iOS platform destination, corrected SKIP_INSTALL settings, and iOS upload type per Apple requirements
*   And many other previous attempts related to bundle IDs, Info.plist keys, and signing.

### Previous Feature Development Tasks

*   **Task 22:** Implement basic UI feedback for common errors (API, Sync, Login)
*   **Task 21:** Enhance exercise transition with performance feedback and haptic confirmation
*   **Task 20:** Implement HKWorkoutSession management (start, stop)
*   **Task 19:** Implement HealthKit authorization request flow
*   **Task 18:** Implement background sync service to push locally saved data to API when online
*   **Task 17:** Ensure workout can continue if connection lost after starting (rely on local storage)
*   **Task 16:** Implement Workout Complete screen and logic to mark workout as finished locally.
*   **Task 15:** Implement "Next Exercise" action (start 1-min timer, handle skip, transition on complete/skip/expiry).
*   **Task 14:** Implement "Add Set" action (revert to Set Screen with "Save Set" button).
*   **Task 13:** Implement calculation and display of performance % change on Save button (requires API historical data).
*   **Task 12:** Implement "Add Set" / "Next Exercise" choice screen after last planned set.
*   **Task 11:** Implement intra-set rest timer logic (start automatically after save/RIR, display countdown on button).
*   **Task 10:** Implement logic to display RIR picker only after the first work set (requires API flags) and save RIR value locally.
*   **Task 9:** Implement RIR Picker UI (using descriptive options).
*   **Task 8:** Implement saving completed set data (reps, weight) to local storage.
*   **Task 5:** Implement basic Set Screen UI (Display Exercise Name, Target Reps, Target Weight from API data/local storage).
*   **Task 4:** Implement Pre-Workout Screen: Display selected workout name and list of exercises (fetched from API).
*   **Task 3:** Fetch and display list of available workouts from API after login.
*   **Task 1 & 2:** Implement "Sign in with Apple" flow and Core Data stack for local storage.

### Project Organization
*   The project has been reorganized into two main directories:
    *   **v0/**: Contains the old implementation (Xamarin-based) for reference
    *   **v1/**: Contains the new implementation (Swift/SwiftUI-based)

</details>