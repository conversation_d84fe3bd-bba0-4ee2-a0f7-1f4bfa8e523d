using NUnit.Framework;
using DrMuscle.UITests.Helpers;
using DrMuscle.UITests.Pages;
using System;
using System.Threading;
using System.Diagnostics;
using OpenQA.Selenium.Appium;

namespace DrMuscle.UITests.Tests
{
    /// <summary>
    /// Comprehensive tests for Workout Flow Variations
    /// Tests pause/resume, skip exercise, add exercise mid-workout, and finish early
    /// </summary>
    [TestFixture]
    [Category(TestCategories.WorkoutJourney)]
    public class WorkoutFlowVariationsTests : AppiumSetup
    {
        private WorkoutPage workoutPage = null!;
        private WorkoutFlowPage workoutFlowPage = null!;
        
        [SetUp]
        public void TestSetup()
        {
            workoutPage = new WorkoutPage(Driver!);
            workoutFlowPage = new WorkoutFlowPage(Driver!);
            
            // Login and start workout
            LoginAndStartWorkout();
        }
        
        [Test]
        [Order(1)]
        [Description("Tests pause and resume workout functionality")]
        public void TEST_Pause_Resume_Workout()
        {
            TestContext.WriteLine("=== Testing Pause/Resume Workout ===");
            
            // Start workout and complete some sets
            workoutPage.SelectExerciseByName("Bench Press");
            Thread.Sleep(2000);
            
            // Complete first set
            workoutPage.EnterSet("10", "135");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            TakeScreenshot("01-first-set-complete");
            
            // Complete second set
            var timerPage = new TimerPage(Driver!);
            timerPage.SkipTimer();
            
            workoutPage.EnterSet("8", "135");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            // Record current state
            var currentExercise = workoutPage.GetCurrentExerciseName();
            var completedSets = workoutPage.GetCompletedSetCount();
            TestContext.WriteLine($"Before pause - Exercise: {currentExercise}, Sets: {completedSets}");
            
            TakeScreenshot("02-before-pause");
            
            // Exit app (simulate crash or user exit)
            TestContext.WriteLine("Simulating app exit...");
            workoutFlowPage.ExitWorkout();
            Thread.Sleep(1000);
            
            // For real crash simulation on device
            if (Driver?.PlatformName == "iOS")
            {
                Driver.TerminateApp("com.drmuscle.app");
                Thread.Sleep(2000);
                Driver.ActivateApp("com.drmuscle.app");
            }
            else
            {
                // Android
                Driver?.BackgroundApp(TimeSpan.FromSeconds(-1)); // Force close
                Thread.Sleep(2000);
                Driver?.ActivateApp("com.drmuscle.app");
            }
            
            Thread.Sleep(3000);
            
            // Verify prompts to resume
            Assert.That(workoutFlowPage.IsResumePromptVisible(), Is.True,
                "Should prompt to resume interrupted workout");
            
            var resumeMessage = workoutFlowPage.GetResumePromptMessage();
            TestContext.WriteLine($"Resume prompt: {resumeMessage}");
            
            Assert.That(resumeMessage, Does.Contain("Bench Press") | Does.Contain("Continue"),
                "Resume prompt should mention the exercise");
            
            TakeScreenshot("03-resume-prompt");
            
            // Resume workout
            workoutFlowPage.ResumeWorkout();
            Thread.Sleep(2000);
            
            // Verify continues from last set
            var resumedExercise = workoutPage.GetCurrentExerciseName();
            var resumedSets = workoutPage.GetCompletedSetCount();
            
            Assert.That(resumedExercise, Is.EqualTo(currentExercise),
                "Should resume with same exercise");
            Assert.That(resumedSets, Is.EqualTo(completedSets),
                "Should maintain completed sets count");
            
            TakeScreenshot("04-workout-resumed");
            
            // Complete another set to verify functionality
            workoutPage.EnterSet("6", "135");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            // Test declining to resume
            TestContext.WriteLine("Testing decline to resume...");
            workoutFlowPage.ExitWorkout();
            Thread.Sleep(2000);
            
            // Reopen app
            Driver?.ActivateApp("com.drmuscle.app");
            Thread.Sleep(3000);
            
            if (workoutFlowPage.IsResumePromptVisible())
            {
                workoutFlowPage.StartNewWorkout();
                Thread.Sleep(1000);
                
                // Verify starts fresh workout
                Assert.That(workoutPage.GetCompletedSetCount(), Is.EqualTo("0"),
                    "Should start fresh workout when declining resume");
                
                TakeScreenshot("05-fresh-workout");
            }
        }
        
        [Test]
        [Order(2)]
        [Description("Tests skipping exercises during workout")]
        public void TEST_Skip_Exercise()
        {
            TestContext.WriteLine("=== Testing Skip Exercise ===");
            
            // Start workout with multiple exercises
            var exerciseList = workoutPage.GetExerciseList();
            TestContext.WriteLine($"Workout exercises: {string.Join(", ", exerciseList)}");
            
            Assert.That(exerciseList.Count, Is.GreaterThan(2),
                "Need multiple exercises to test skipping");
            
            // Complete first exercise
            workoutPage.SelectExerciseByName(exerciseList[0]);
            Thread.Sleep(2000);
            
            workoutPage.EnterSet("10", "100");
            workoutPage.SaveSet();
            workoutPage.FinishExercise();
            Thread.Sleep(1000);
            
            TakeScreenshot("06-first-exercise-complete");
            
            // Start second exercise
            var secondExercise = exerciseList[1];
            TestContext.WriteLine($"Starting exercise to skip: {secondExercise}");
            
            workoutPage.SelectExerciseByName(secondExercise);
            Thread.Sleep(2000);
            
            // Skip this exercise
            workoutFlowPage.SkipCurrentExercise();
            Thread.Sleep(1000);
            
            // Verify asks for reason
            Assert.That(workoutFlowPage.IsSkipReasonVisible(), Is.True,
                "Should prompt for skip reason");
            
            TakeScreenshot("07-skip-reason-prompt");
            
            // Select reason
            var skipReasons = workoutFlowPage.GetSkipReasons();
            TestContext.WriteLine($"Skip reasons: {string.Join(", ", skipReasons)}");
            
            workoutFlowPage.SelectSkipReason("Equipment Not Available");
            workoutFlowPage.ConfirmSkip();
            Thread.Sleep(1000);
            
            // Add optional note
            if (workoutFlowPage.CanAddSkipNote())
            {
                workoutFlowPage.AddSkipNote("All benches taken");
                Thread.Sleep(500);
            }
            
            TakeScreenshot("08-exercise-skipped");
            
            // Verify moves to next exercise
            var currentExercise = workoutPage.GetCurrentExerciseName();
            Assert.That(currentExercise, Is.Not.EqualTo(secondExercise),
                "Should move to next exercise after skipping");
            
            if (exerciseList.Count > 2)
            {
                Assert.That(currentExercise, Does.Contain(exerciseList[2]),
                    "Should move to third exercise");
            }
            
            // Continue with remaining exercises
            workoutPage.EnterSet("12", "50");
            workoutPage.SaveSet();
            workoutPage.FinishExercise();
            Thread.Sleep(1000);
            
            // Check workout summary shows skipped exercise
            workoutPage.FinishWorkout();
            Thread.Sleep(2000);
            
            var workoutSummary = workoutFlowPage.GetWorkoutSummary();
            TestContext.WriteLine($"Workout summary: {workoutSummary}");
            
            Assert.That(workoutSummary, Does.Contain("Skipped") | Does.Contain(secondExercise),
                "Summary should indicate skipped exercise");
            
            TakeScreenshot("09-summary-with-skip");
            
            // Verify affects volume tracking
            var totalVolume = workoutFlowPage.GetTotalVolume();
            TestContext.WriteLine($"Total volume (with skip): {totalVolume}");
        }
        
        [Test]
        [Order(3)]
        [Description("Tests adding exercise mid-workout")]
        public void TEST_Add_Exercise_Mid_Workout()
        {
            TestContext.WriteLine("=== Testing Add Exercise Mid-Workout ===");
            
            // Complete first exercise
            workoutPage.SelectExerciseByName("Squat");
            Thread.Sleep(2000);
            
            for (int i = 1; i <= 3; i++)
            {
                workoutPage.EnterSet("5", "225");
                workoutPage.SaveSet();
                if (i < 3)
                {
                    var timer = new TimerPage(Driver!);
                    timer.SkipTimer();
                }
            }
            
            workoutPage.FinishExercise();
            Thread.Sleep(1000);
            
            TakeScreenshot("10-first-exercise-done");
            
            // Feel good, want to add extra exercise
            TestContext.WriteLine("Adding extra exercise...");
            workoutFlowPage.AddExercise();
            Thread.Sleep(1000);
            
            // Search for exercise to add
            workoutFlowPage.SearchExercise("Leg Press");
            Thread.Sleep(1000);
            
            var searchResults = workoutFlowPage.GetSearchResults();
            TestContext.WriteLine($"Search results: {string.Join(", ", searchResults.Take(3))}");
            
            workoutFlowPage.SelectExerciseToAdd("Leg Press");
            Thread.Sleep(1000);
            
            // Configure sets/reps for added exercise
            if (workoutFlowPage.CanConfigureAddedExercise())
            {
                workoutFlowPage.SetAddedExerciseSets("4");
                workoutFlowPage.SetAddedExerciseReps("12-15");
                workoutFlowPage.ConfirmAddExercise();
                Thread.Sleep(1000);
            }
            
            TakeScreenshot("11-exercise-added");
            
            // Verify exercise added to current workout
            var updatedExerciseList = workoutPage.GetExerciseList();
            Assert.That(updatedExerciseList.Any(ex => ex.Contains("Leg Press")), Is.True,
                "Added exercise should appear in workout");
            
            // Complete the added exercise
            workoutPage.SelectExerciseByName("Leg Press");
            Thread.Sleep(2000);
            
            workoutPage.EnterSet("15", "400");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            workoutPage.EnterSet("12", "400");
            workoutPage.SaveSet();
            workoutPage.FinishExercise();
            Thread.Sleep(1000);
            
            // Complete remaining planned exercises
            var remainingExercises = workoutPage.GetExerciseList()
                .Where(ex => !ex.Contains('✓') && !ex.Contains("Complete"))
                .ToList();
            
            TestContext.WriteLine($"Remaining exercises: {string.Join(", ", remainingExercises)}");
            
            // Finish workout and verify added exercise included
            workoutPage.FinishWorkout();
            Thread.Sleep(2000);
            
            var finalSummary = workoutFlowPage.GetWorkoutSummary();
            TestContext.WriteLine($"Final summary: {finalSummary}");
            
            Assert.That(finalSummary, Does.Contain("Leg Press"),
                "Added exercise should be included in workout summary");
            
            TakeScreenshot("12-summary-with-added");
        }
        
        [Test]
        [Order(4)]
        [Description("Tests finishing workout early")]
        public void TEST_Finish_Workout_Early()
        {
            TestContext.WriteLine("=== Testing Finish Workout Early ===");
            
            // Start workout with multiple exercises
            var totalExercises = workoutPage.GetExerciseList().Count;
            TestContext.WriteLine($"Total planned exercises: {totalExercises}");
            
            // Complete 2-3 exercises
            int exercisesToComplete = Math.Min(2, totalExercises - 1);
            
            for (int ex = 0; ex < exercisesToComplete; ex++)
            {
                var exercises = workoutPage.GetExerciseList();
                workoutPage.SelectExerciseByName(exercises[ex]);
                Thread.Sleep(2000);
                
                // Do minimal sets
                workoutPage.EnterSet("5", "100");
                workoutPage.SaveSet();
                workoutPage.FinishExercise();
                Thread.Sleep(1000);
            }
            
            TakeScreenshot("13-partial-workout");
            
            // Tap finish workout early
            workoutFlowPage.FinishWorkoutEarly();
            Thread.Sleep(1000);
            
            // Verify confirmation prompt
            Assert.That(workoutFlowPage.IsFinishEarlyConfirmationVisible(), Is.True,
                "Should show confirmation before finishing early");
            
            var confirmMessage = workoutFlowPage.GetFinishEarlyMessage();
            TestContext.WriteLine($"Confirmation message: {confirmMessage}");
            
            Assert.That(confirmMessage, Does.Contain("incomplete") | Does.Contain("sure"),
                "Should warn about incomplete workout");
            
            TakeScreenshot("14-finish-early-confirm");
            
            // Select reason for finishing early
            var finishReasons = workoutFlowPage.GetFinishEarlyReasons();
            TestContext.WriteLine($"Finish reasons: {string.Join(", ", finishReasons)}");
            
            workoutFlowPage.SelectFinishReason("Time Constraint");
            
            // Add optional note
            workoutFlowPage.AddFinishNote("Had to leave for appointment");
            Thread.Sleep(500);
            
            workoutFlowPage.ConfirmFinishEarly();
            Thread.Sleep(2000);
            
            TakeScreenshot("15-workout-finished-early");
            
            // Check partial workout saved
            var partialSummary = workoutFlowPage.GetWorkoutSummary();
            TestContext.WriteLine($"Partial workout summary: {partialSummary}");
            
            Assert.That(partialSummary, Does.Contain($"{exercisesToComplete}") | Does.Contain("Partial"),
                "Should indicate partial workout completion");
            
            // Verify reason noted
            Assert.That(partialSummary, Does.Contain("Time") | Does.Contain("Early"),
                "Should note reason for early finish");
            
            // Check affects statistics appropriately
            var completionRate = workoutFlowPage.GetCompletionRate();
            TestContext.WriteLine($"Workout completion rate: {completionRate}");
            
            if (!string.IsNullOrEmpty(completionRate))
            {
                #pragma warning disable CA1307
                if (completionRate.Contains('✓'))
                #pragma warning restore CA1307
                {
                    TestContext.WriteLine("Workout marked as complete despite early finish");
                }
                else
                {
                    var rate = ParsePercentage(completionRate);
                    Assert.That(rate, Is.LessThan(100),
                        "Completion rate should be less than 100%");
                }
            }
            
            TakeScreenshot("16-partial-stats");
        }
        
        [Test]
        [Order(5)]
        [Description("Tests workout interruption recovery scenarios")]
        public void TEST_Workout_Interruption_Recovery()
        {
            TestContext.WriteLine("=== Testing Workout Interruption Recovery ===");
            
            // Start workout
            workoutPage.SelectExerciseByName("Deadlift");
            Thread.Sleep(2000);
            
            // Complete one set
            workoutPage.EnterSet("5", "315");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            // Simulate various interruptions
            var scenarios = new (string name, Action action)[]
            {
                ("Phone Call", () => SimulatePhoneCall()),
                ("Low Battery", () => SimulateLowBattery()),
                ("Network Loss", () => SimulateNetworkLoss())
            };
            
            foreach (var (scenario, action) in scenarios)
            {
                TestContext.WriteLine($"\nTesting {scenario} interruption...");
                
                action();
                Thread.Sleep(3000);
                
                // Verify workout state preserved
                if (workoutFlowPage.IsWorkoutActive())
                {
                    var preservedExercise = workoutPage.GetCurrentExerciseName();
                    TestContext.WriteLine($"Workout preserved after {scenario}: {preservedExercise}");
                    
                    TakeScreenshot($"17-{scenario.ToLower().Replace(" ", "-")}-recovery");
                }
                else if (workoutFlowPage.IsResumePromptVisible())
                {
                    TestContext.WriteLine($"Resume prompt shown after {scenario}");
                    workoutFlowPage.ResumeWorkout();
                    Thread.Sleep(1000);
                }
            }
        }
        
        private void LoginAndStartWorkout()
        {
            Thread.Sleep(3000);
            
            workoutPage.WaitForStartWorkout();
            workoutPage.StartWorkout();
            Thread.Sleep(2000);
            
            workoutPage.WaitForExerciseList();
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private void SimulatePhoneCall()
        #pragma warning restore CA1822 // Mark members as static
        {
            // iOS: Simulate incoming call
            if (Driver?.PlatformName == "iOS")
            {
                Driver.BackgroundApp(TimeSpan.FromSeconds(5));
            }
            else
            {
                // Android: Send app to background
                Driver?.BackgroundApp(TimeSpan.FromSeconds(5));
            }
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private void SimulateLowBattery()
        #pragma warning restore CA1822 // Mark members as static
        {
            // This would typically trigger system low battery mode
            // For testing, we just background the app briefly
            Driver?.BackgroundApp(TimeSpan.FromSeconds(3));
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private void SimulateNetworkLoss()
        #pragma warning restore CA1822 // Mark members as static
        {
            // Toggle airplane mode or disable network
            // For testing, we can't actually do this, so we simulate
            TestContext.WriteLine("Simulating network loss...");
            Thread.Sleep(2000);
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private double ParsePercentage(string percentageText)
        #pragma warning restore CA1822 // Mark members as static
        {
            var cleanText = percentageText.Replace("%", "").Trim();
            return double.TryParse(cleanText, out double percentage) ? percentage : 0;
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private void TakeScreenshot(string name)
        #pragma warning restore CA1822 // Mark members as static
        {
            TestTimings.TakeScreenshot(Driver, name);
        }
    }
}