using NUnit.Framework;
using System;
using System.IO;
using Xamarin.UITest;

namespace DrMuscle.UITests.Tests
{
    [TestFixture]
    public class BasicConnectionTest
    {
        [Test]
        public void CanConnectToTestFramework()
        {
            // This test verifies basic framework connectivity without launching the app
            Assert.That(ConfigureApp.iOS, Is.Not.Null, "iOS configurator should be available");
            
            // Verify we're in CI environment
            var isCI = Environment.GetEnvironmentVariable("CI") == "true";
            Console.WriteLine($"Running in CI: {isCI}");
            
            if (isCI)
            {
                var appPath = Environment.GetEnvironmentVariable("IOS_APP_BUNDLE_PATH");
                Console.WriteLine($"IOS_APP_BUNDLE_PATH: {appPath}");
                
                if (!string.IsNullOrEmpty(appPath))
                {
                    Assert.That(Directory.Exists(appPath), Is.True, $"App bundle should exist at: {appPath}");
                    
                    var infoPlistPath = Path.Combine(appPath, "Info.plist");
                    Assert.That(File.Exists(infoPlistPath), Is.True, $"Info.plist should exist at: {infoPlistPath}");
                }
            }
            
            Assert.Pass("Basic connectivity test passed");
        }
    }
}