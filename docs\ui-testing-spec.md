# MAUI UI Testing Specification

## Overview
Automated UI testing specification for DrMaxMuscle MAUI app focusing on authentication and core user flows using Mac runner with iOS Simulator.

---

## Phase 1: Happy Path Testing

### Test Environment
- **Platform**: iOS Simulator on macOS GitHub Actions runner
- **Backend**: Production API (`https://drmuscle.azurewebsites.net/`)
- **Test Framework**: XHarness MAUI UITest (net8.0-ios-simulator)
- **Test Data**: Disposable email accounts (`@yopmail.com`)

### Test Account Strategy
- **Email Pattern**: `test-{timestamp}@yopmail.com` (e.g., `<EMAIL>`)
- **Password**: `123456` (static for all tests)
- **Account Lifecycle**: Create fresh accounts per test run, accumulation acceptable
- **Profile Data**: Realistic test data for complete onboarding

---

## Test Case 1: Complete User Journey (Happy Path)

### 1.1 Account Creation Flow
**Objective**: Successfully create new account and complete onboarding

**Steps**:
1. **Launch App** → Verify landing on initial screen
2. **Navigate to Registration**:
   - Tap "Create new account" label
   - Verify `RegistrationPage` loads with logo and form fields
3. **Enter Account Details**:
   - Email: Generate `test-{timestamp}@yopmail.com`
   - Password: `123456`
   - Tap email creation frame/button
4. **Complete Onboarding Flow** (`MainOnboardingPage`):
   - **First Name**: `TestUser{randomNum}`
   - **Gender**: Select "Man" 
   - **Body Weight**: `180` lbs
   - **Mass Unit**: `lbs`
   - **Experience**: Select appropriate level
   - **Workout Location**: Home/Gym preference
   - **Equipment**: Standard equipment selection
   - **Additional Preferences**: Complete all required onboarding steps
5. **Verify Account Creation**:
   - Check successful navigation to main app
   - Verify user data persistence (`LocalDBManager` contains token, email, firstname)

### 1.2 Logout Flow
**Objective**: Successfully log out and clear session

**Steps**:
1. **Access Logout**:
   - Navigate to side menu/hamburger menu
   - Locate logout gesture/button
   - Tap logout option
2. **Confirm Logout**:
   - If confirmation dialog appears, confirm logout
3. **Verify Logout**:
   - Check navigation back to `WelcomePage`
   - Verify session cleared (`LocalDBManager.Reset()` called)
   - Verify no user data in local storage

### 1.3 Login Flow (Existing User)
**Objective**: Successfully log back in with same credentials

**Steps**:
1. **Enter Login Credentials**:
   - Email: Use same test email from creation
   - Password: `123456`
   - Tap login frame/button
2. **Verify Login Success**:
   - Check successful authentication (`LoginSuccessResult` with access_token)
   - Verify navigation to main app
   - Verify user data restored

### 1.4 Success Criteria Validation
**Objective**: Confirm user can access core app functionality

**Steps**:
1. **Navigate to Home/Workout Screen**
2. **Locate "Start workout" Button**:
   - Find button at bottom of home page
   - Verify button is visible and enabled
3. **Tap "Start workout"**
4. **Verify Workout Page Access**:
   - Confirm navigation to workout page
   - Verify workout interface loads correctly

**Final Success Criteria**: User successfully completes full cycle (signup → onboarding → logout → login → start workout) within 5 minutes.

---

## Phase 2: Error Scenarios & Edge Cases

### Test Case 2.1: Registration Validation Errors
- **Invalid Email Formats**: Missing @, invalid domain, empty field
- **Password Validation**: Too short (<6 chars), empty field
- **Duplicate Email**: Attempt to register with existing email
- **Network Failures**: Simulate offline/timeout during registration

### Test Case 2.2: Login Validation Errors  
- **Invalid Credentials**: Wrong password, non-existent email
- **Empty Fields**: Missing email or password
- **Network Issues**: Timeout during authentication
- **Session Expiry**: Expired token handling

### Test Case 2.3: Onboarding Edge Cases
- **Back Navigation**: Test back button behavior during onboarding
- **Field Validation**: Required fields, invalid inputs
- **App Backgrounding**: Test onboarding resume after app backgrounding
- **Network Interruption**: Handle network loss during onboarding

### Test Case 2.4: Logout Edge Cases
- **Multiple Logout Attempts**: Ensure idempotent logout
- **Logout During Network Operations**: Interrupt ongoing API calls
- **App State Cleanup**: Verify complete session cleanup

---

## UI Element Targeting Strategy

### Custom Controls
- **DrMuscleEntry**: Target using `x:Name` attributes (`EmailEntry`, `PasswordEntry`)
- **DrMuscleButton**: Target using `x:Name` or `Text` content
- **Frame Buttons**: Target frames with `TapGestureRecognizer` using container identification

### Selectors Priority
1. **AutomationId** (to be added in implementation)
2. **x:Name** attributes
3. **Text Content** (button text, placeholders)
4. **Class Name + Index** (fallback for frames/containers)

### Key UI Elements
```csharp
// Registration Page
EmailEntry (DrMuscleEntry)
PasswordEntry (DrMuscleEntry) 
EmailBtnFrame (Frame with TapGestureRecognizer)
GoogleBtnFrame, AppleBtnFrame (alternative auth)

// Welcome/Login Page  
EmailEntry, PasswordEntry (DrMuscleEntry)
EmailBtnFrame (login frame)
CreateNewAccountButton (Label with TapGestureRecognizer)

// Onboarding Elements
FirstnameEntry (Entry)
YesButton, NoButton (options)
ManButton, WomanButton (gender selection)
NextButton (DrMuscleButton)

// Main App
LogoutGesture (TapGestureRecognizer in side menu)
StartWorkoutButton (bottom navigation)
```

---

## Implementation Phases

### Phase 1A: Test Infrastructure (Week 1)
- Set up XHarness MAUI UITest project
- Configure iOS Simulator in GitHub Actions
- Implement test data generation utilities
- Create base test classes and helpers

### Phase 1B: Happy Path Implementation (Week 1-2)
- Implement complete user journey test
- Add verification helpers for authentication states
- Test against production API
- Validate success criteria

### Phase 2A: Error Scenario Framework (Week 2-3)  
- Implement validation error testing
- Add network simulation capabilities
- Create edge case test scenarios
- Add comprehensive assertion helpers

### Phase 2B: Reporting & Monitoring (Week 3-4)
- Integrate test results with CI/CD pipeline
- Add test artifacts (screenshots, logs)
- Set up failure notifications
- Performance metrics collection

---

## Risk Mitigation

### Test Stability
- **Retry Logic**: Implement retry for flaky network operations
- **Wait Strategies**: Smart waits for UI element availability
- **Cleanup**: Ensure proper test isolation and cleanup

### Production Testing
- **Rate Limiting**: Respect API rate limits with delays
- **Account Management**: Monitor test account creation volume
- **Data Isolation**: Use unique identifiers to avoid test interference

### CI/CD Integration
- **Timeout Handling**: Set appropriate test timeouts (5min per test)
- **Parallel Execution**: Design for potential parallel test execution
- **Resource Management**: Efficient iOS Simulator usage

---

## Success Metrics

### Functional Metrics
- **Test Coverage**: 100% of critical auth flows covered
- **Success Rate**: >95% test pass rate on main branch
- **Execution Time**: Complete test suite <10 minutes

### Quality Metrics  
- **Bug Detection**: Catch auth regressions before production
- **User Experience**: Validate smooth onboarding flow
- **API Integration**: Confirm backend compatibility

### Operational Metrics
- **Test Reliability**: <5% flaky test rate
- **Maintenance Overhead**: <2 hours/week test maintenance
- **Feedback Loop**: Test results available within 15 minutes of commit 