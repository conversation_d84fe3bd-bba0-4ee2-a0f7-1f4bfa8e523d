# DrMuscle MAUI Automated Testing Project – Phase 2: Workout Flow Testing

---

## 0  Overview
This plan delivers comprehensive test coverage for the core workout functionality - the heart of the DrMuscle app. Building on Phase 1's authentication and infrastructure foundation, Phase 2 implements end-to-end workout flow testing using our proven SimCtl approach.

**Primary Goal:** Validate the complete workout lifecycle from start to finish, ensuring data integrity and user experience quality.

---

## 1  Phase 2 Blueprint (High-level Goals)
| Component | Goal | Success Criteria |
|-----------|------|------------------|
| P2.1 | Workout initialization | User can start workout from home screen |
| P2.2 | Exercise selection | First exercise loads with proper data |
| P2.3 | Set recording | All sets can be saved with weight/reps |
| P2.4 | Exercise completion | Exercise marked complete, data persisted |
| P2.5 | Workout completion | Full workout saved, summary displayed |
| P2.6 | Data validation | Workout data accessible in history |

---

## 2  Implementation Chunks (3-5 day iterations)
1. **C1 – Test Data Setup**
   - Create test workout templates
   - Implement test user with workout history
   - Add workout data validation helpers

2. **C2 – Workout Start Flow**
   - Launch → Login → Navigate to home
   - Tap "Start Workout" button
   - Verify workout screen loads

3. **C3 – Exercise Screen Testing**
   - Capture exercise details screen
   - Verify exercise name, sets, rest timer
   - Document UI state changes

4. **C4 – Set Recording Flow**
   - Simulate set completion timing
   - Capture weight/rep entry screens
   - Verify set save confirmation

5. **C5 – Exercise Completion**
   - Complete all sets for exercise
   - Capture completion state
   - Verify navigation to next exercise

6. **C6 – Workout Summary**
   - Complete final exercise
   - Capture workout summary
   - Verify stats (duration, volume, exercises)

7. **C7 – Data Persistence**
   - Navigate to workout history
   - Verify completed workout appears
   - Capture history screen

8. **C8 – Edge Cases**
   - Test workout abandonment
   - Test app crash recovery
   - Test offline workout completion

---

## 3  Detailed Test Implementation

### 3.1 SimCtl Workout Test Suite

```csharp
[TestFixture]
public class SimCtlWorkoutFlowTests
{
    // Test 1: Happy Path - Complete Workout
    [Test]
    public void CompleteWorkoutHappyPath()
    {
        // 1. Launch and login
        // 2. Start workout
        // 3. Complete first exercise (3 sets)
        // 4. Save workout
        // 5. Verify summary
    }

    // Test 2: Exercise Navigation
    [Test]
    public void NavigateBetweenExercises()
    {
        // Test forward/back navigation
        // Verify exercise order
        // Capture each exercise screen
    }

    // Test 3: Set Recording Variations
    [Test]
    public void RecordDifferentSetTypes()
    {
        // Regular sets
        // Drop sets
        // Rest-pause sets
        // Failure sets
    }

    // Test 4: Workout Metrics
    [Test]
    public void ValidateWorkoutMetrics()
    {
        // Total volume calculation
        // Duration tracking
        // Exercise count
        // Personal records
    }

    // Test 5: State Recovery
    [Test]
    public void RecoverFromAppTermination()
    {
        // Start workout
        // Terminate app mid-workout
        // Relaunch and verify state
    }
}
```

### 3.2 Screenshot Validation Points

Each test captures screenshots at critical moments:

1. **Pre-Workout**
   - Home screen with "Start Workout" button
   - Workout selection screen
   - Loading state

2. **During Workout**
   - Exercise instruction screen
   - Set entry screen
   - Rest timer screen
   - Weight/rep picker
   - Set completion confirmation

3. **Post-Workout**
   - Workout summary
   - Stats/achievements
   - History update

### 3.3 Data Validation Strategy

Since we can't read UI text with SimCtl, we'll use:
- Screenshot naming conventions to track state
- Timing-based validation (expected durations)
- File system checks for local data persistence
- API calls to verify backend data (if possible)

### 3.4 Screenshot Accessibility for Team Review

To make screenshots easily viewable while keeping them on the Mac runner:

#### Implementation Steps:

1. **Add HTTP Server Step to Workflow**
```yaml
- name: Start Screenshot Server
  if: always()
  run: |
    # Start Python HTTP server to serve screenshots
    cd "$RUN_DIR"
    python3 -m http.server 9000 --bind 0.0.0.0 > server.log 2>&1 &
    SERVER_PID=$!
    echo "SERVER_PID=$SERVER_PID" >> $GITHUB_ENV
    
    # Generate HTML index for easy browsing
    python3 << 'EOF'
import os
import html

screenshots = []
for root, dirs, files in os.walk('Screenshots'):
    for file in files:
        if file.endswith('.png'):
            path = os.path.join(root, file).replace('\\', '/')
            screenshots.append(path)

with open('index.html', 'w') as f:
    f.write('''<!DOCTYPE html>
<html>
<head>
    <title>Test Screenshots - Run #${{ github.run_number }}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .screenshot { 
            display: inline-block; 
            margin: 10px; 
            text-align: center;
            border: 1px solid #ddd;
            padding: 10px;
        }
        .screenshot img { 
            max-width: 300px; 
            cursor: pointer;
        }
        .screenshot:hover { 
            box-shadow: 0 0 10px rgba(0,0,0,0.3);
        }
        h2 { color: #333; }
    </style>
</head>
<body>
    <h1>UI Test Screenshots</h1>
    <h2>Run #${{ github.run_number }} - ${{ github.ref_name }}</h2>
    <p>Total Screenshots: ''' + str(len(screenshots)) + '''</p>
    <div class="gallery">
''')
    
    for screenshot in sorted(screenshots):
        name = os.path.basename(screenshot)
        f.write(f'''
        <div class="screenshot">
            <a href="{html.escape(screenshot)}" target="_blank">
                <img src="{html.escape(screenshot)}" alt="{html.escape(name)}">
            </a>
            <p>{html.escape(name)}</p>
        </div>
        ''')
    
    f.write('''
    </div>
</body>
</html>
''')
EOF
    
    # Add access instructions to job summary
    echo "" >> $GITHUB_STEP_SUMMARY
    echo "### 🖼️ Browse Screenshots Online" >> $GITHUB_STEP_SUMMARY
    echo "1. **HTTP Server:** http://*************:9000" >> $GITHUB_STEP_SUMMARY
    echo "   - Gallery view with thumbnails" >> $GITHUB_STEP_SUMMARY
    echo "   - Click any image to view full size" >> $GITHUB_STEP_SUMMARY
    echo "" >> $GITHUB_STEP_SUMMARY
    echo "2. **Direct VNC Access:** vnc://*************:59010" >> $GITHUB_STEP_SUMMARY
    echo "   - Open path: $RUN_DIR/Screenshots" >> $GITHUB_STEP_SUMMARY
    echo "" >> $GITHUB_STEP_SUMMARY
    echo "3. **Download ZIP:** http://*************:9000/screenshots.zip" >> $GITHUB_STEP_SUMMARY
    
    # Create downloadable ZIP
    zip -r screenshots.zip Screenshots/ > /dev/null 2>&1
```

2. **Add Cleanup Step**
```yaml
- name: Stop Screenshot Server
  if: always() && env.SERVER_PID != ''
  run: |
    kill $SERVER_PID || true
    echo "Screenshot server stopped"
```

#### Access Methods for Team:

1. **Web Browser** (Recommended)
   - Navigate to http://*************:9000
   - Browse thumbnails in gallery view
   - Click for full-size images

2. **VNC Connection** (Already available)
   - Connect to vnc://*************:59010
   - Navigate to screenshot directory in Finder
   - Use Quick Look (Space bar) for preview

3. **ZIP Download**
   - Access http://*************:9000/screenshots.zip
   - Download entire screenshot collection
   - Extract and browse locally

#### Security Considerations:
- HTTP server binds to specific port (9000)
- Access controlled by network/VPN
- Server auto-stops after workflow completion
- No screenshots leave the runner

---

## 4  Test Data Requirements

### 4.1 Test User Account
```
Email: <EMAIL>
Password: Test123!
Pre-configured with:
- Basic workout template
- Previous workout history
- Some personal records
```

### 4.2 Test Workout Template
```
"UI Test Workout"
- Bench Press: 3 sets
- Squats: 3 sets  
- Pull-ups: 3 sets
```

### 4.3 Expected Timings
- App launch: 3-5 seconds
- Login: 2-3 seconds
- Workout start: 2 seconds
- Per set: 30-60 seconds
- Total workout: 5-8 minutes

---

## 5  Implementation Schedule

### Week 1: Foundation
- Day 1-2: Test data setup, user account creation
- Day 3-4: Basic workout start test  
- Day 5: Screenshot validation framework

### Week 2: Core Flow
- Day 1-2: Exercise navigation tests
- Day 3-4: Set recording tests
- Day 5: Exercise completion flow

### Week 3: Completion & Edge Cases  
- Day 1-2: Workout summary tests
- Day 3-4: Data persistence validation
- Day 5: Edge case scenarios

### Week 4: Polish & Integration
- Day 1-2: Performance metrics
- Day 3-4: Integration with Phase 1 tests
- Day 5: Documentation and cleanup

---

## 6  Success Metrics

### Test Coverage
- [ ] 15+ new SimCtl tests
- [ ] 50+ workout flow screenshots
- [ ] All critical paths covered

### Quality Metrics  
- [ ] 100% pass rate
- [ ] <10 second average test time
- [ ] Zero flaky tests

### Business Value
- [ ] Workout completion validated
- [ ] Data integrity confirmed
- [ ] User journey documented

---

## 7  Risk Mitigation

### Technical Risks
1. **Limited interaction** → Mitigated by timing-based flows
2. **State verification** → Screenshot comparison + data checks
3. **Test duration** → Parallel execution where possible

### Process Risks
1. **Test data corruption** → Fresh user per test run
2. **Simulator issues** → Retry logic + cleanup
3. **Screenshot storage** → Automated cleanup (30 days)

---

## 8  Future Enhancements (Phase 3 Preview)

Once Phase 2 is complete, consider:
- Workout plan testing (multi-day)
- Exercise substitution flows
- Advanced features (supersets, circuits)
- Performance testing (100+ exercise workouts)
- Apple Watch companion app testing

---

## 9  Prompt Sequence for Implementation

### Prompt 1: Test Data Setup
```text
Create test data infrastructure for workout testing:
1. Add TestDataHelper class with CreateTestUser() method
2. Add WorkoutTestData class with template workouts
3. Ensure consistent test data across runs
Write failing test first that requires this data.
```

### Prompt 2: Workout Start Test
```text
Implement failing test for workout start flow:
1. Login with test user
2. Navigate to home tab
3. Tap "Start Workout" 
4. Verify workout screen appears
Use SimCtl screenshot validation.
```

### Prompt 3: Exercise Screen Capture
```text
Add test to capture exercise screens:
1. After workout starts, wait for exercise load
2. Capture exercise name/details screen
3. Capture set information
4. Document timer states
Create organized screenshot structure.
```

### Prompt 4: Set Recording Simulation
```text
Implement set recording test:
1. Simulate timing for set completion
2. Capture weight entry screen
3. Capture rep entry screen  
4. Verify set saved (via screenshot)
Add multiple set variations.
```

### Prompt 5: Exercise Completion Flow
```text
Test exercise completion:
1. Complete all sets for exercise
2. Capture completion confirmation
3. Verify navigation to next exercise
4. Handle last exercise specially
```

### Prompt 6: Workout Summary Validation
```text
Implement workout completion test:
1. Finish final exercise
2. Capture summary screen
3. Verify key metrics visible
4. Document achievement screens
Save summary for comparison.
```

### Prompt 7: History Verification
```text
Add workout history test:
1. Navigate to history/profile
2. Find completed workout
3. Capture workout details
4. Verify data matches expected
Close the loop on data flow.
```

### Prompt 8: Edge Case Tests
```text
Implement edge cases:
1. Abandon workout mid-flow
2. Kill app during workout
3. Offline workout completion
4. Quick workout vs. planned
Document recovery behavior.
```

---

## 10  Definition of Done

Phase 2 is complete when:
- ✅ All 8 prompt implementations merged
- ✅ 15+ tests passing in CI
- ✅ Test execution < 5 minutes
- ✅ Screenshots organized by flow
- ✅ Documentation updated
- ✅ No degradation to Phase 1 tests
- ✅ Workout data validation proven

---

This plan provides a comprehensive, TDD-driven approach to testing the most critical user journey in DrMuscle - completing a workout successfully. 