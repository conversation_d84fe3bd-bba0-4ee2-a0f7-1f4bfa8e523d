using NUnit.Framework;
using DrMuscle.UITests.Helpers;
using DrMuscle.UITests.Pages;
using System;
using System.Threading;
using OpenQA.Selenium.Appium;

namespace DrMuscle.UITests.Tests
{
    /// <summary>
    /// Comprehensive tests for Weight Entry Variations
    /// Tests bodyweight, resistance bands, cable machines, assisted exercises, and unusual increments
    /// </summary>
    [TestFixture]
    [Category(TestCategories.CoreFeatures)]
    public class WeightEntryVariationsTests : AppiumSetup
    {
        private WorkoutPage workoutPage = null!;
        private WeightEntryPage weightEntryPage = null!;
        
        [SetUp]
        public void TestSetup()
        {
            workoutPage = new WorkoutPage(Driver!);
            weightEntryPage = new WeightEntryPage(Driver!);
            
            // Login and start workout
            LoginAndStartWorkout();
        }
        
        [Test]
        [Order(1)]
        [Description("Tests bodyweight exercise functionality")]
        public void TEST_Bodyweight_Exercises()
        {
            TestContext.WriteLine("=== Testing Bodyweight Exercises ===");
            
            // Select bodyweight exercise
            workoutPage.SelectExerciseByName("Pull-ups");
            Thread.Sleep(2000);
            
            // Verify weight shows "BW"
            var displayedWeight = weightEntryPage.GetDisplayedWeight();
            Assert.That(displayedWeight, Is.EqualTo("BW") | Does.Contain("Bodyweight"),
                "Should show bodyweight indicator for bodyweight exercises");
            
            TakeScreenshot("01-bodyweight-display");
            
            // Complete reps with just bodyweight
            workoutPage.EnterReps("8");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            // Verify saved correctly
            var setDetails = workoutPage.GetLastSetDetails();
            TestContext.WriteLine($"Bodyweight set saved: {setDetails}");
            Assert.That(setDetails, Does.Contain("BW") | Does.Contain("8 reps"),
                "Should save bodyweight set correctly");
            
            // Test with added weight
            TestContext.WriteLine("Testing weighted pull-ups...");
            weightEntryPage.SwitchToWeightedMode();
            Thread.Sleep(1000);
            
            weightEntryPage.EnterAddedWeight("25");
            TakeScreenshot("02-bodyweight-plus-weight");
            
            // Verify shows BW+25
            displayedWeight = weightEntryPage.GetDisplayedWeight();
            Assert.That(displayedWeight, Is.EqualTo("BW+25") | Does.Contain("BW+25"),
                "Should show bodyweight plus added weight");
            
            workoutPage.EnterReps("6");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            // Test with assistance (negative weight)
            TestContext.WriteLine("Testing assisted pull-ups...");
            weightEntryPage.SwitchToAssistedMode();
            Thread.Sleep(1000);
            
            weightEntryPage.EnterAssistanceWeight("40");
            TakeScreenshot("03-bodyweight-assisted");
            
            // Verify shows BW-40
            displayedWeight = weightEntryPage.GetDisplayedWeight();
            Assert.That(displayedWeight, Is.EqualTo("BW-40") | Does.Contain("BW-40"),
                "Should show bodyweight minus assistance");
            
            workoutPage.EnterReps("12");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            // Verify progression tracking
            var progressionInfo = weightEntryPage.GetProgressionInfo();
            TestContext.WriteLine($"Progression info: {progressionInfo}");
            
            TakeScreenshot("04-bodyweight-progression");
        }
        
        [Test]
        [Order(2)]
        [Description("Tests resistance band functionality")]
        public void TEST_Resistance_Bands()
        {
            TestContext.WriteLine("=== Testing Resistance Bands ===");
            
            // Select band-compatible exercise
            workoutPage.SelectExerciseByName("Face Pulls");
            Thread.Sleep(2000);
            
            // Switch to band mode
            weightEntryPage.OpenWeightOptions();
            weightEntryPage.SelectWeightType("Resistance Band");
            Thread.Sleep(1000);
            
            TakeScreenshot("05-band-mode-selected");
            
            // Choose band resistance
            var availableBands = weightEntryPage.GetAvailableBands();
            TestContext.WriteLine($"Available bands: {string.Join(", ", availableBands)}");
            
            Assert.That(availableBands.Count, Is.GreaterThan(0),
                "Should have band resistance options");
            
            // Select medium band
            weightEntryPage.SelectBandResistance("Medium");
            Thread.Sleep(1000);
            
            var displayedResistance = weightEntryPage.GetDisplayedWeight();
            Assert.That(displayedResistance, Does.Contain("Medium") | Does.Contain("Band"),
                "Should show band resistance level");
            
            TakeScreenshot("06-band-resistance-selected");
            
            // Complete set with band
            workoutPage.EnterReps("15");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            // Test combining bands with weights
            TestContext.WriteLine("Testing band + weight combination...");
            weightEntryPage.EnableWeightPlusBand(true);
            Thread.Sleep(1000);
            
            weightEntryPage.EnterAdditionalWeight("20");
            
            displayedResistance = weightEntryPage.GetDisplayedWeight();
            TestContext.WriteLine($"Combined resistance: {displayedResistance}");
            
            Assert.That(displayedResistance, Does.Contain("Band") & Does.Contain("20"),
                "Should show both band and weight");
            
            TakeScreenshot("07-band-plus-weight");
            
            workoutPage.EnterReps("12");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            // Test band progression
            weightEntryPage.SelectBandResistance("Heavy");
            Thread.Sleep(1000);
            
            var bandProgression = weightEntryPage.GetBandProgressionInfo();
            TestContext.WriteLine($"Band progression: {bandProgression}");
            
            TakeScreenshot("08-band-progression");
        }
        
        [Test]
        [Order(3)]
        [Description("Tests cable machine weight entry")]
        public void TEST_Cable_Machine_Weights()
        {
            TestContext.WriteLine("=== Testing Cable Machine Weights ===");
            
            // Select cable exercise
            workoutPage.SelectExerciseByName("Cable Row");
            Thread.Sleep(2000);
            
            // Switch to cable mode
            weightEntryPage.OpenWeightOptions();
            weightEntryPage.SelectWeightType("Cable Machine");
            Thread.Sleep(1000);
            
            TakeScreenshot("09-cable-mode");
            
            // Test plate/pin number entry
            weightEntryPage.SelectCableSystem("Pin Stack");
            Thread.Sleep(500);
            
            var pinNumbers = weightEntryPage.GetPinNumbers();
            TestContext.WriteLine($"Available pins: {string.Join(", ", pinNumbers)}");
            
            weightEntryPage.SelectPinNumber("12");
            Thread.Sleep(1000);
            
            // Verify shows pin number or converted weight
            var displayedWeight = weightEntryPage.GetDisplayedWeight();
            TestContext.WriteLine($"Cable weight display: {displayedWeight}");
            
            Assert.That(displayedWeight, Does.Contain("12") | Does.Contain("120"),
                "Should show pin number or converted weight");
            
            TakeScreenshot("10-cable-pin-selected");
            
            // Test different cable systems
            TestContext.WriteLine("Testing plate-loaded cable...");
            weightEntryPage.SelectCableSystem("Plate Loaded");
            Thread.Sleep(500);
            
            weightEntryPage.EnterCableWeight("90");
            
            workoutPage.EnterReps("12");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            // Test cable weight conversion
            if (weightEntryPage.HasWeightConversion())
            {
                var conversionInfo = weightEntryPage.GetConversionInfo();
                TestContext.WriteLine($"Cable conversion: {conversionInfo}");
                TakeScreenshot("11-cable-conversion");
            }
        }
        
        [Test]
        [Order(4)]
        [Description("Tests assisted exercise functionality")]
        public void TEST_Assisted_Exercises()
        {
            TestContext.WriteLine("=== Testing Assisted Exercises ===");
            
            // Select assisted exercise
            workoutPage.SelectExerciseByName("Assisted Dips");
            Thread.Sleep(2000);
            
            // Verify shows assistance mode
            Assert.That(weightEntryPage.IsAssistanceModeActive(), Is.True,
                "Should automatically detect assisted exercise");
            
            TakeScreenshot("12-assisted-exercise");
            
            // Get user bodyweight for calculations
            var userBodyweight = weightEntryPage.GetUserBodyweight();
            TestContext.WriteLine($"User bodyweight: {userBodyweight}");
            
            // Enter assistance weight
            weightEntryPage.EnterAssistanceWeight("60");
            Thread.Sleep(1000);
            
            // Verify calculation (BW - assistance)
            var effectiveWeight = weightEntryPage.GetEffectiveWeight();
            TestContext.WriteLine($"Effective weight: {effectiveWeight}");
            
            if (!string.IsNullOrEmpty(userBodyweight))
            {
                var bw = ParseWeight(userBodyweight);
                var effective = ParseWeight(effectiveWeight);
                Assert.That(effective, Is.EqualTo(bw - 60).Within(5),
                    "Effective weight should be bodyweight minus assistance");
            }
            
            TakeScreenshot("13-assistance-calculation");
            
            // Complete set
            workoutPage.EnterReps("10");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            // Test progression (less assistance)
            TestContext.WriteLine("Testing progression with less assistance...");
            weightEntryPage.EnterAssistanceWeight("40");
            Thread.Sleep(1000);
            
            var newEffectiveWeight = weightEntryPage.GetEffectiveWeight();
            TestContext.WriteLine($"New effective weight: {newEffectiveWeight}");
            
            Assert.That(ParseWeight(newEffectiveWeight), Is.GreaterThan(ParseWeight(effectiveWeight)),
                "Less assistance should increase effective weight");
            
            TakeScreenshot("14-assistance-progression");
            
            workoutPage.EnterReps("8");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
        }
        
        [Test]
        [Order(5)]
        [Description("Tests unusual weight increments")]
        public void TEST_Unusual_Weight_Increments()
        {
            TestContext.WriteLine("=== Testing Unusual Weight Increments ===");
            
            workoutPage.SelectExerciseByName("Barbell Curl");
            Thread.Sleep(2000);
            
            // Test gym with odd plates
            TestContext.WriteLine("Testing odd plate weights...");
            
            // Test 15kg plates
            weightEntryPage.EnterExactWeight("15", "kg");
            Thread.Sleep(500);
            
            var acceptedWeight = weightEntryPage.GetDisplayedWeight();
            Assert.That(acceptedWeight, Does.Contain("15"),
                "Should accept 15kg weight");
            
            TakeScreenshot("15-odd-weight-15kg");
            
            workoutPage.SaveSet();
            
            // Test 35lb plates
            weightEntryPage.SwitchUnits("lbs");
            weightEntryPage.EnterExactWeight("35", "lbs");
            Thread.Sleep(500);
            
            acceptedWeight = weightEntryPage.GetDisplayedWeight();
            Assert.That(acceptedWeight, Does.Contain("35"),
                "Should accept 35lb weight");
            
            TakeScreenshot("16-odd-weight-35lbs");
            
            // Test decimal weights
            TestContext.WriteLine("Testing decimal weights...");
            weightEntryPage.EnterExactWeight("52.5", "kg");
            Thread.Sleep(500);
            
            acceptedWeight = weightEntryPage.GetDisplayedWeight();
            Assert.That(acceptedWeight, Does.Contain("52.5") | Does.Contain("52,5"),
                "Should accept decimal weights");
            
            TakeScreenshot("17-decimal-weight");
            
            workoutPage.EnterReps("8");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            // Test metric/imperial conversion
            TestContext.WriteLine("Testing unit conversion...");
            
            // Enter weight in kg
            weightEntryPage.SwitchUnits("kg");
            weightEntryPage.EnterExactWeight("100", "kg");
            Thread.Sleep(500);
            
            var kgWeight = weightEntryPage.GetDisplayedWeight();
            TestContext.WriteLine($"Weight in kg: {kgWeight}");
            
            // Switch to lbs
            weightEntryPage.SwitchUnits("lbs");
            Thread.Sleep(500);
            
            var lbsWeight = weightEntryPage.GetDisplayedWeight();
            TestContext.WriteLine($"Weight in lbs: {lbsWeight}");
            
            // Verify conversion (100kg = ~220.5 lbs)
            var lbsValue = ParseWeight(lbsWeight);
            Assert.That(lbsValue, Is.InRange(220, 221),
                "100kg should convert to approximately 220.5 lbs");
            
            TakeScreenshot("18-unit-conversion");
            
            // Test very precise weights
            weightEntryPage.EnterExactWeight("47.625", "kg");
            Thread.Sleep(500);
            
            acceptedWeight = weightEntryPage.GetDisplayedWeight();
            TestContext.WriteLine($"Precise weight: {acceptedWeight}");
            
            TakeScreenshot("19-precise-weight");
        }
        
        [Test]
        [Order(6)]
        [Description("Tests weight entry validation and limits")]
        public void TEST_Weight_Entry_Validation()
        {
            TestContext.WriteLine("=== Testing Weight Entry Validation ===");
            
            workoutPage.SelectExerciseByName("Squat");
            Thread.Sleep(2000);
            
            // Test maximum weight limit
            weightEntryPage.EnterExactWeight("9999", "kg");
            Thread.Sleep(500);
            
            var validationMessage = weightEntryPage.GetValidationMessage();
            if (!string.IsNullOrEmpty(validationMessage))
            {
                TestContext.WriteLine($"Max weight validation: {validationMessage}");
                TakeScreenshot("20-max-weight-validation");
            }
            
            // Test negative weight
            weightEntryPage.EnterExactWeight("-50", "kg");
            Thread.Sleep(500);
            
            validationMessage = weightEntryPage.GetValidationMessage();
            Assert.That(validationMessage, Is.Not.Empty | Is.EqualTo("0"),
                "Should not accept negative weights");
            
            TakeScreenshot("21-negative-weight-validation");
            
            // Test text input
            weightEntryPage.EnterExactWeight("abc", "kg");
            Thread.Sleep(500);
            
            validationMessage = weightEntryPage.GetValidationMessage();
            var displayedWeight = weightEntryPage.GetDisplayedWeight();
            
            Assert.That(string.IsNullOrEmpty(displayedWeight) || displayedWeight == "0",
                "Should not accept text as weight");
            
            TakeScreenshot("22-text-weight-validation");
        }
        
        private void LoginAndStartWorkout()
        {
            Thread.Sleep(3000);
            
            workoutPage.WaitForStartWorkout();
            workoutPage.StartWorkout();
            Thread.Sleep(2000);
            
            workoutPage.WaitForExerciseList();
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private double ParseWeight(string weightText)
        #pragma warning restore CA1822 // Mark members as static
        {
            var cleanWeight = weightText
                .Replace("lbs", "")
                .Replace("kg", "")
                .Replace("BW+", "")
                .Replace("BW-", "")
                .Replace("BW", "0")
                .Trim();
                
            return double.TryParse(cleanWeight, out double weight) ? weight : 0;
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private void TakeScreenshot(string name)
        #pragma warning restore CA1822 // Mark members as static
        {
            TestTimings.TakeScreenshot(Driver, name);
        }
    }
}