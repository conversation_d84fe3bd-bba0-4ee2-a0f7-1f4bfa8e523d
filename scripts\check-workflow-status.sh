#!/bin/bash

# Script to check GitHub Actions workflow status
# Usage: ./scripts/check-workflow-status.sh

echo "=== GitHub Actions Workflow Status Check ==="
echo ""
echo "Since GitHub CLI is not authenticated, please check the workflow status at:"
echo "https://github.com/dr-muscle/DrMuscle/actions/workflows/maui-test-workflow.yml"
echo ""
echo "Latest commits pushed:"
git log --oneline -5
echo ""
echo "Current branch:"
git branch --show-current
echo ""
echo "To authenticate GitHub CLI for future use:"
echo "gh auth login" 