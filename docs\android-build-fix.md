# Android Build Error Fix - July 13, 2025

## Issue
The Android build in `.github/workflows/maui-build-workflow.yml` was failing with:
```
error MSB6006: "java" exited with code 2
```

## Root Causes
1. **Duplicate resource entries** in localization files causing resource compilation errors
2. **Java heap space exhaustion** during the build process

## Solutions Implemented

### 1. Fixed Duplicate Resources
Removed duplicate "MyWorkouts" entries from:
- `DrMaxMuscle/Resx/AppResources.fr.resx` (line 995)
- `DrMaxMuscle/Resx/AppResources.sv.resx` (line 995)

### 2. Increased Java Heap Size
Added Java heap size configuration to Android build step:
```yaml
# Set Java heap size to prevent out-of-memory errors
export _JAVA_OPTIONS="-Xmx4096m"
```

Also added MSBuild parameter:
```
/p:JavaMaximumHeapSize=4096m
```

## Files Modified
- `.github/workflows/maui-build-workflow.yml`
- `DrMaxMuscle/Resx/AppResources.fr.resx`
- `DrMaxMuscle/Resx/AppResources.sv.resx`

## Next Steps
The workflow should now complete successfully. Monitor the build to ensure:
1. No more duplicate resource warnings
2. Java build completes without memory errors
3. Android AAB package is generated successfully