﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             Shell.NavBarIsVisible="false"
             NavigationPage.HasNavigationBar="False"
             x:Class="DrMaxMuscle.OnBoarding.Page1">
    <Grid AutomationId="OnboardingPage1">
        <Image Source="splash" Aspect="AspectFill" AutomationId="Page1BackgroundImage" />
        <StackLayout HorizontalOptions="Center" x:Name="mainStack" AutomationId="Page1MainStack" Margin="{OnPlatform Android= '0,100,0,50',iOS='0,150,0,50'}" >
            <Image HeightRequest="200" WidthRequest="150" x:Name="imgLogo" AutomationId="Page1Logo" Source="logo.png" />

            <StackLayout  Padding="0">
                <Image Margin="{OnPlatform Android='0,24,0,0',iOS='0,40,0,0'}" x:Name="ImgStar1" HeightRequest="{OnPlatform iOS='30'}" Source="stars_5.png"  WidthRequest="90" VerticalOptions="{OnPlatform Android='Center',iOS='Fill'}" HorizontalOptions="CenterAndExpand" />
                <StackLayout Orientation="Vertical" HorizontalOptions="CenterAndExpand">
                    <Label TextColor="White" FontAttributes="Italic" Text="&quot;Stronger at 62 than I was at 31&quot;" HorizontalOptions="CenterAndExpand" Style="{StaticResource OnBoardingLabelStyle}" FontSize="14" x:Name="LblReview1" />
                    <Label TextColor="LightGray" Text="-Rodney Spence" HorizontalOptions="CenterAndExpand" Style="{StaticResource OnBoardingLabelStyle}" FontSize="13"  x:Name="LblAuthor1" />
                </StackLayout>

                <StackLayout>
                    <Image Margin="0,24,0,0" x:Name="ImgStar2" Source="stars_5.png" WidthRequest="90" HeightRequest="{OnPlatform iOS='30'}" VerticalOptions="Center" HorizontalOptions="CenterAndExpand" />
                    <StackLayout Orientation="Vertical" HorizontalOptions="CenterAndExpand">
                        <Label TextColor="White" FontAttributes="Italic" Text="&quot;I’ve never looked or felt better&quot;" HorizontalOptions="CenterAndExpand" Style="{StaticResource OnBoardingLabelStyle}" FontSize="14"  x:Name="LblReview2"/>
                        <Label TextColor="LightGray" Text="-Matthew Hammond" HorizontalOptions="CenterAndExpand" Style="{StaticResource OnBoardingLabelStyle}" FontSize="13"  x:Name="LblAuthor2"/>
                    </StackLayout>

                </StackLayout>
            </StackLayout>

        </StackLayout>
    </Grid>
</ContentView>