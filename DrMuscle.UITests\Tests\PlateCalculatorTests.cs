using NUnit.Framework;
using DrMuscle.UITests.Helpers;
using DrMuscle.UITests.Pages;
using System;
using System.Threading;
using OpenQA.Selenium.Appium;

namespace DrMuscle.UITests.Tests
{
    /// <summary>
    /// Comprehensive tests for the Plate Calculator feature
    /// Tests plate breakdown calculations for barbells, dumbbells, and custom plates
    /// </summary>
    [TestFixture]
    [Category(TestCategories.CoreFeatures)]
    public class PlateCalculatorTests : AppiumSetup
    {
        private WorkoutPage workoutPage = null!;
        private PlateCalculatorPage plateCalculatorPage = null!;
        
        [SetUp]
        public void TestSetup()
        {
            workoutPage = new WorkoutPage(Driver!);
            plateCalculatorPage = new PlateCalculatorPage(Driver!);
            
            // Login and navigate to workout
            LoginAndStartWorkout();
        }
        
        [Test]
        [Order(1)]
        [Description("Tests opening plate calculator for barbell exercises")]
        public void TEST_Open_Plate_Calculator_Barbell()
        {
            TestContext.WriteLine("=== Testing Plate Calculator for Barbell ===");
            
            // Navigate to a barbell exercise
            workoutPage.SelectExerciseByName("Bench Press");
            Thread.Sleep(2000);
            
            // Enter target weight
            workoutPage.EnterWeight("135");
            
            // Open plate calculator
            plateCalculatorPage.OpenPlateCalculator();
            Thread.Sleep(1000);
            TakeScreenshot("01-plate-calculator-opened");
            
            // Verify calculator is open
            Assert.That(plateCalculatorPage.IsPlateCalculatorVisible(), 
                "Plate calculator should be visible");
            
            // Verify barbell type selector
            Assert.That(plateCalculatorPage.GetSelectedBarbellType(), Is.EqualTo("Olympic"),
                "Olympic barbell should be selected by default");
            
            // Verify plate breakdown
            var plateBreakdown = plateCalculatorPage.GetPlateBreakdown();
            TestContext.WriteLine($"Plate breakdown for 135 lbs: {plateBreakdown}");
            
            // 135 lbs = 45 lb bar + 45 lb plate on each side
            Assert.That(plateBreakdown, Does.Contain("45"), 
                "Should show 45 lb plates");
            
            TakeScreenshot("02-plate-breakdown-135lbs");
            
            // Test with different weight
            plateCalculatorPage.EnterTargetWeight("225");
            Thread.Sleep(500);
            
            plateBreakdown = plateCalculatorPage.GetPlateBreakdown();
            TestContext.WriteLine($"Plate breakdown for 225 lbs: {plateBreakdown}");
            
            // 225 lbs = 45 lb bar + 90 lbs each side (45+45)
            Assert.That(plateBreakdown, Does.Contain("45"), 
                "Should show 45 lb plates for 225 lbs");
            
            TakeScreenshot("03-plate-breakdown-225lbs");
        }
        
        [Test]
        [Order(2)]
        [Description("Tests plate calculator for dumbbell exercises")]
        public void TEST_Plate_Calculator_Dumbbell()
        {
            TestContext.WriteLine("=== Testing Plate Calculator for Dumbbells ===");
            
            // Navigate to a dumbbell exercise
            workoutPage.SelectExerciseByName("Dumbbell Curl");
            Thread.Sleep(2000);
            
            // Enter target weight
            workoutPage.EnterWeight("40");
            
            // Open plate calculator
            plateCalculatorPage.OpenPlateCalculator();
            Thread.Sleep(1000);
            
            // Verify dumbbell mode
            Assert.That(plateCalculatorPage.IsDumbbellModeActive(), 
                "Calculator should be in dumbbell mode for dumbbell exercises");
            
            // Verify shows single dumbbell weight
            var singleWeight = plateCalculatorPage.GetSingleDumbbellWeight();
            Assert.That(singleWeight, Is.EqualTo("40"),
                "Should show single dumbbell weight");
            
            TakeScreenshot("04-dumbbell-weight-display");
            
            // Test adjustable vs fixed dumbbells
            plateCalculatorPage.SelectDumbbellType("Adjustable");
            Thread.Sleep(500);
            
            var adjustableBreakdown = plateCalculatorPage.GetPlateBreakdown();
            TestContext.WriteLine($"Adjustable dumbbell breakdown: {adjustableBreakdown}");
            
            plateCalculatorPage.SelectDumbbellType("Fixed");
            Thread.Sleep(500);
            
            var fixedOptions = plateCalculatorPage.GetFixedDumbbellOptions();
            TestContext.WriteLine($"Fixed dumbbell options: {string.Join(", ", fixedOptions)}");
            
            TakeScreenshot("05-dumbbell-type-options");
        }
        
        [Test]
        [Order(3)]
        [Description("Tests custom plate configuration for home gyms")]
        public void TEST_Plate_Calculator_Custom_Plates()
        {
            TestContext.WriteLine("=== Testing Custom Plate Configuration ===");
            
            // Open plate calculator settings
            plateCalculatorPage.OpenPlateSettings();
            Thread.Sleep(1000);
            TakeScreenshot("06-plate-settings-opened");
            
            // Add custom plates
            plateCalculatorPage.AddCustomPlate("15", "kg");
            plateCalculatorPage.AddCustomPlate("20", "kg");
            plateCalculatorPage.AddCustomPlate("1.25", "kg");
            
            // Remove standard plates that aren't available
            plateCalculatorPage.RemovePlate("35", "lbs");
            plateCalculatorPage.RemovePlate("2.5", "lbs");
            
            TakeScreenshot("07-custom-plates-configured");
            
            // Save and test calculation
            plateCalculatorPage.SavePlateSettings();
            Thread.Sleep(1000);
            
            // Test with custom plates
            workoutPage.SelectExerciseByName("Squat");
            workoutPage.EnterWeight("60", "kg");
            
            plateCalculatorPage.OpenPlateCalculator();
            Thread.Sleep(500);
            
            var customBreakdown = plateCalculatorPage.GetPlateBreakdown();
            TestContext.WriteLine($"Custom plate breakdown for 60kg: {customBreakdown}");
            
            // 60kg = 20kg bar + 20kg each side
            Assert.That(customBreakdown, Does.Contain("20"),
                "Should use available 20kg plates");
            
            TakeScreenshot("08-custom-plate-calculation");
            
            // Test home gym scenario with limited plates
            TestContext.WriteLine("Testing home gym scenario with limited plates...");
            
            plateCalculatorPage.SelectPlateSetPreset("Home Gym Basic");
            Thread.Sleep(500);
            
            var homeGymPlates = plateCalculatorPage.GetAvailablePlates();
            TestContext.WriteLine($"Home gym plates: {string.Join(", ", homeGymPlates)}");
            
            TakeScreenshot("09-home-gym-preset");
        }
        
        [Test]
        [Order(4)]
        [Description("Tests microplate calculations for small increments")]
        public void TEST_Plate_Calculator_Microplates()
        {
            TestContext.WriteLine("=== Testing Microplate Calculations ===");
            
            // Test with small weight increments
            workoutPage.SelectExerciseByName("Overhead Press");
            
            // Test 0.5kg increment
            workoutPage.EnterWeight("32.5", "kg");
            plateCalculatorPage.OpenPlateCalculator();
            Thread.Sleep(500);
            
            var microplateBreakdown = plateCalculatorPage.GetPlateBreakdown();
            TestContext.WriteLine($"Microplate breakdown for 32.5kg: {microplateBreakdown}");
            
            Assert.That(microplateBreakdown, Does.Contain("0.5") | Does.Contain("0,5"),
                "Should include 0.5kg microplates");
            
            TakeScreenshot("10-microplate-calculation");
            
            // Test 1.25 lbs increment
            plateCalculatorPage.SwitchUnits("lbs");
            workoutPage.EnterWeight("47.5", "lbs");
            
            Thread.Sleep(500);
            microplateBreakdown = plateCalculatorPage.GetPlateBreakdown();
            TestContext.WriteLine($"Microplate breakdown for 47.5lbs: {microplateBreakdown}");
            
            Assert.That(microplateBreakdown, Does.Contain("1.25") | Does.Contain("1,25"),
                "Should include 1.25lb microplates");
            
            // Test impossible weight (shows closest possible)
            workoutPage.EnterWeight("46.7", "lbs");
            Thread.Sleep(500);
            
            var closestWeight = plateCalculatorPage.GetClosestPossibleWeight();
            var warningMessage = plateCalculatorPage.GetWarningMessage();
            
            TestContext.WriteLine($"Closest possible weight to 46.7lbs: {closestWeight}");
            TestContext.WriteLine($"Warning message: {warningMessage}");
            
            Assert.That(warningMessage, Is.Not.Empty,
                "Should show warning for impossible weight");
            
            TakeScreenshot("11-impossible-weight-warning");
        }
        
        [Test]
        [Order(5)]
        [Description("Tests barbell type selection (Olympic vs Standard)")]
        public void TEST_Barbell_Type_Selection()
        {
            TestContext.WriteLine("=== Testing Barbell Type Selection ===");
            
            workoutPage.SelectExerciseByName("Deadlift");
            workoutPage.EnterWeight("315", "lbs");
            
            plateCalculatorPage.OpenPlateCalculator();
            Thread.Sleep(500);
            
            // Test Olympic barbell (45 lbs)
            plateCalculatorPage.SelectBarbellType("Olympic");
            Thread.Sleep(500);
            
            var olympicBreakdown = plateCalculatorPage.GetPlateBreakdown();
            var olympicBarWeight = plateCalculatorPage.GetBarWeight();
            
            Assert.That(olympicBarWeight, Is.EqualTo("45"),
                "Olympic bar should be 45 lbs");
            
            TestContext.WriteLine($"Olympic bar breakdown for 315lbs: {olympicBreakdown}");
            TakeScreenshot("12-olympic-barbell");
            
            // Test Standard barbell (typically lighter)
            plateCalculatorPage.SelectBarbellType("Standard");
            Thread.Sleep(500);
            
            var standardBreakdown = plateCalculatorPage.GetPlateBreakdown();
            var standardBarWeight = plateCalculatorPage.GetBarWeight();
            
            TestContext.WriteLine($"Standard bar weight: {standardBarWeight}");
            TestContext.WriteLine($"Standard bar breakdown for 315lbs: {standardBreakdown}");
            
            TakeScreenshot("13-standard-barbell");
            
            // Test women's barbell (35 lbs)
            if (plateCalculatorPage.HasBarbellType("Women's"))
            {
                plateCalculatorPage.SelectBarbellType("Women's");
                Thread.Sleep(500);
                
                var womensBarWeight = plateCalculatorPage.GetBarWeight();
                Assert.That(womensBarWeight, Is.EqualTo("35"),
                    "Women's bar should be 35 lbs");
                
                TakeScreenshot("14-womens-barbell");
            }
        }
        
        [Test]
        [Order(6)]
        [Description("Tests unit conversion in plate calculator")]
        public void TEST_Unit_Conversion()
        {
            TestContext.WriteLine("=== Testing Unit Conversion ===");
            
            workoutPage.SelectExerciseByName("Front Squat");
            
            // Enter weight in kg
            workoutPage.EnterWeight("100", "kg");
            plateCalculatorPage.OpenPlateCalculator();
            Thread.Sleep(500);
            
            var kgBreakdown = plateCalculatorPage.GetPlateBreakdown();
            TestContext.WriteLine($"Plate breakdown for 100kg: {kgBreakdown}");
            TakeScreenshot("15-kg-calculation");
            
            // Switch to lbs
            plateCalculatorPage.SwitchUnits("lbs");
            Thread.Sleep(500);
            
            var convertedWeight = plateCalculatorPage.GetTargetWeight();
            var lbsBreakdown = plateCalculatorPage.GetPlateBreakdown();
            
            TestContext.WriteLine($"Converted weight: {convertedWeight} lbs");
            TestContext.WriteLine($"Plate breakdown in lbs: {lbsBreakdown}");
            
            // Verify conversion (100kg ≈ 220.5 lbs)
            var weightValue = double.Parse(convertedWeight.Replace("lbs", "").Trim());
            Assert.That(weightValue, Is.InRange(220, 221),
                "100kg should convert to approximately 220.5 lbs");
            
            TakeScreenshot("16-lbs-conversion");
        }
        
        private void LoginAndStartWorkout()
        {
            // This would use the actual login flow
            // For now, we'll assume we're already logged in
            Thread.Sleep(3000);
            
            workoutPage.WaitForStartWorkout();
            workoutPage.StartWorkout();
            Thread.Sleep(2000);
            
            workoutPage.WaitForExerciseList();
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private void TakeScreenshot(string name)
        {
            TestTimings.TakeScreenshot(Driver, name);
        }
        #pragma warning restore CA1822 // Mark members as static
    }
}