using Acr.UserDialogs;
using CommunityToolkit.Maui.Views;
using DrMaxMuscle.Message;
using System.Text.RegularExpressions;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using RGPopup.Maui.Pages;
using RGPopup.Maui.Services;
using DrMaxMuscle.Utility;
using CommunityToolkit.Maui.Core;

namespace DrMaxMuscle.Views;

public partial class BodyweightPopup : Popup
{
    public BodyweightPopup()
    {
       
        InitializeComponent();
        LocalDBManager.Instance.SetDBSetting("massunit", "lb");
        LbGradient.BackgroundColor = Constants.AppThemeConstants.BlueColor;
        Console.WriteLine("Body Weight popup calling...");

        var screenWidth = DeviceDisplay.MainDisplayInfo.Width / DeviceDisplay.MainDisplayInfo.Density;
        IOSFrame.WidthRequest = screenWidth * 0.9;

        this.Opened += Popup_Opened;
    }


    private void Popup_Opened(object sender, PopupOpenedEventArgs e)
    {
        //base.OnAppearing();
        Device.BeginInvokeOnMainThread(() =>
        {
            EntryBodyWeight.Focus();
        });
    }
    public async void BtnDoneClicked(object sender, EventArgs args)
    {
        if (string.IsNullOrEmpty(EntryBodyWeight.Text) || string.IsNullOrWhiteSpace(EntryBodyWeight.Text))
            return;
        try
        {
            var weight = int.Parse(EntryBodyWeight.Text);
            if (weight < 1)
            {

                await HelperClass.DisplayCustomPopupForResult("Error",
                        "Please enter valid weight","Ok","");

                // await UserDialogs.Instance.AlertAsync(new AlertConfig()
                // {
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     Message = "Please enter valid weight",
                //     Title = "Error"
                // });
                return;
            }
        }
        catch (Exception ex)
        {

        }

        var bodyweight = EntryBodyWeight.Text;

        //await MauiProgram.SafeDismissTopPopup();
        //if (PopupNavigation.Instance.PopupStack.Count() > 0)
        //    await PopupNavigation.Instance.PopAsync();

        try
        {
            if (this.Handler != null)
                await this.CloseAsync();
        }
        catch (Exception ex)
        {
            // Log exception if needed
        }

        MessagingCenter.Send<BodyweightMessage>(new BodyweightMessage() { BodyWeight = bodyweight }, "BodyweightMessage");
    }

    public async void BtnLbsClicked(object sender, EventArgs args)
    {
        //BtnLbs.BackgroundColor = Color.FromHex("#5CD196");
        BtnKg.BackgroundColor = Colors.Transparent;
        LocalDBManager.Instance.SetDBSetting("massunit", "lb");
        KgGradient.BackgroundColor = Colors.Transparent;
        LbGradient.BackgroundColor = Constants.AppThemeConstants.BlueColor;
        BtnKg.TextColor = Color.FromHex("#0C2432");
        BtnLbs.TextColor = Colors.White;
    }

    public async void BtnKgClicked(object sender, EventArgs args)
    {
        BtnLbs.BackgroundColor = Colors.Transparent;
        //BtnKg.BackgroundColor = Color.FromHex("#5CD196");
        LocalDBManager.Instance.SetDBSetting("massunit", "kg");
        BtnKg.TextColor = Colors.White;
        BtnLbs.TextColor = Color.FromHex("#0C2432");
        KgGradient.BackgroundColor = Constants.AppThemeConstants.BlueColor;
        LbGradient.BackgroundColor = Colors.Transparent;
    }

    protected void BodyweightPopup_OnTextChanged(object obj, TextChangedEventArgs args)
    {
        try
        {

            Entry entry = (Entry)obj;
            const string textRegex = @"^\d+(?:[\.,]\d{0,5})?$";
            var text = entry.Text.Replace(",", ".");
            bool IsValid = Regex.IsMatch(text, textRegex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
            if (IsValid == false && !string.IsNullOrEmpty(entry.Text))
            {
                double result;
                entry.Text = entry.Text.Substring(0, entry.Text.Length - 1);
                double.TryParse(entry.Text, out result);
                entry.Text = result.ToString();
            }

        }
        catch (Exception ex)
        {

        }
    }
}