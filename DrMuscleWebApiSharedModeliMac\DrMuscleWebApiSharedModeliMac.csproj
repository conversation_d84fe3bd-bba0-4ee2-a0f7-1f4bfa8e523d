<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks>net7.0;net7.0-android;net7.0-ios;net7.0-maccatalyst</TargetFrameworks>
		<UseMaui>true</UseMaui>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>

		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">14.2</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">14.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'tizen'">6.5</SupportedOSPlatformVersion>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net7.0-ios|AnyCPU'">
	  <CreatePackage>false</CreatePackage>
	</PropertyGroup>
	<ItemGroup>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\AddUserExerciseModel.cs">
	    <Link>AddUserExerciseModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\AddUserWorkoutTemplateModel.cs">
	    <Link>AddUserWorkoutTemplateModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\ApiResponse.cs">
	    <Link>ApiResponse.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\AppleNotificationModel.cs">
	    <Link>AppleNotificationModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\AvailablePlateModel.cs">
	    <Link>AvailablePlateModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\BaseModel.cs">
	    <Link>BaseModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\BodyPartModel.cs">
	    <Link>BodyPartModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\BooleanModel.cs">
	    <Link>BooleanModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\ChatModel.cs">
	    <Link>ChatModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\ChatRoomModel.cs">
	    <Link>ChatRoomModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\ConsecutiveWeeksModel.cs">
	    <Link>ConsecutiveWeeksModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\DeleteWorkoutLogExerciseModel.cs">
	    <Link>DeleteWorkoutLogExerciseModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\DeviceModel.cs">
	    <Link>DeviceModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\EquipmentModel.cs">
	    <Link>EquipmentModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\ErrorResponse.cs">
	    <Link>ErrorResponse.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\ExerciceModel.cs">
	    <Link>ExerciceModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\ExerciseSettingsModel.cs">
	    <Link>ExerciseSettingsModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\ExerciseStates.cs">
	    <Link>ExerciseStates.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\FeaturedProgramModel.cs">
	    <Link>FeaturedProgramModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\ForgotPasswordModel.cs">
	    <Link>ForgotPasswordModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\ForgotPasswordResponseModel.cs">
	    <Link>ForgotPasswordResponseModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\GetExerciseRequest.cs">
	    <Link>GetExerciseRequest.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\GetExercisesLogResponseModel.cs">
	    <Link>GetExercisesLogResponseModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\GetOneRMforExerciseModel.cs">
	    <Link>GetOneRMforExerciseModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\GetRecommendationForExerciseModel.cs">
	    <Link>GetRecommendationForExerciseModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\GetUserExerciseResponseModel.cs">
	    <Link>GetUserExerciseResponseModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\GetUserProgramInfoResponseModel.cs">
	    <Link>GetUserProgramInfoResponseModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\GetUserWorkoutLogAverageForExerciseRequest.cs">
	    <Link>GetUserWorkoutLogAverageForExerciseRequest.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\GetUserWorkoutLogAverageResponse.cs">
	    <Link>GetUserWorkoutLogAverageResponse.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\GetUserWorkoutResponseModel.cs">
	    <Link>GetUserWorkoutResponseModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\GetUserWorkoutTemplateGroupResponseModel.cs">
	    <Link>GetUserWorkoutTemplateGroupResponseModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\GooglePushNotificationModel.cs">
	    <Link>GooglePushNotificationModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\GroupChatModel.cs">
	    <Link>GroupChatModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\HistoryExerciseModel.cs">
	    <Link>HistoryExerciseModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\HistoryModel.cs">
	    <Link>HistoryModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\IsEmailAlreadyExistModel.cs">
	    <Link>IsEmailAlreadyExistModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\LifeCycle.cs">
	    <Link>LifeCycle.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\LightSessionModel.cs">
	    <Link>LightSessionModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\LoginErrorResponseModel.cs">
	    <Link>LoginErrorResponseModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\LoginModel.cs">
	    <Link>LoginModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\LoginSuccessResult.cs">
	    <Link>LoginSuccessResult.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\MealPlanModel.cs">
	    <Link>MealPlanModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\MultiUnityWeight.cs">
	    <Link>MultiUnityWeight.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\NewExerciceLogModel.cs">
	    <Link>NewExerciceLogModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\NewExerciceModel.cs">
	    <Link>NewExerciceModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\NewExerciseLogResponseModel.cs">
	    <Link>NewExerciseLogResponseModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\NewUserSubscriptionModel.cs">
	    <Link>NewUserSubscriptionModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\OneRMAverage.cs">
	    <Link>OneRMAverage.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\OneRMModel.cs">
	    <Link>OneRMModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\PhoneToWatchModel.cs">
	    <Link>PhoneToWatchModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\ProgramExerciseModel.cs">
	    <Link>ProgramExerciseModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\Receipt.cs">
	    <Link>Receipt.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\RecommendationModel.cs">
	    <Link>RecommendationModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\RegisterModel.cs">
	    <Link>RegisterModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\ResetPasswordModel.cs">
	    <Link>ResetPasswordModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\SatisfactionSurveyModel.cs">
	    <Link>SatisfactionSurveyModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\SaveWorkoutModel.cs">
	    <Link>SaveWorkoutModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\SendBirdNotificationModel.cs">
	    <Link>SendBirdNotificationModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\SetLog.cs">
	    <Link>SetLog.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\SetModel.cs">
	    <Link>SetModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\SingleIntegerModel.cs">
	    <Link>SingleIntegerModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\SubscriptionModel.cs">
	    <Link>SubscriptionModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\SubscriptionSourceModel.cs">
	    <Link>SubscriptionSourceModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\ThriveCartSubscription.cs">
	    <Link>ThriveCartSubscription.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\UnlockCodeResponseModel.cs">
	    <Link>UnlockCodeResponseModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\UnsubscribeModel.cs">
	    <Link>UnsubscribeModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\UserInfoMealPlanModel.cs">
	    <Link>UserInfoMealPlanModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\UserInfosModel.cs">
	    <Link>UserInfosModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\UserWeight.cs">
	    <Link>UserWeight.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\WorkoutLogSerieModel.cs">
	    <Link>WorkoutLogSerieModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\WorkoutTemplateGroupModel.cs">
	    <Link>WorkoutTemplateGroupModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\WorkoutTemplateModel.cs">
	    <Link>WorkoutTemplateModel.cs</Link>
	  </Compile>
	  <Compile Include="..\DrMuscleWebApiSharedModel1\WorkoutTemplateSettingsModel.cs">
	    <Link>WorkoutTemplateSettingsModel.cs</Link>
	  </Compile>
	</ItemGroup>
	<ItemGroup>
	  <None Remove="Platforms\Android\" />
	  <None Remove="Platforms\iOS\" />
	  <None Remove="Platforms\MacCatalyst\" />
	  <None Remove="Platforms\Tizen\" />
	  <None Remove="Platforms\Windows\" />
	</ItemGroup>
	<ItemGroup>
	  <None Include="..\DrMuscleWebApiSharedModel1\app.config">
	    <Link>app.config</Link>
	  </None>
	  <None Include="..\DrMuscleWebApiSharedModel1\packages.config">
	    <Link>packages.config</Link>
	  </None>
	</ItemGroup>
	<ItemGroup>
	  <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
	</ItemGroup>
	<ItemGroup>
	  <Folder Include="Platforms\Android\" />
	  <Folder Include="Platforms\iOS\" />
	  <Folder Include="Platforms\MacCatalyst\" />
	  <Folder Include="Platforms\Tizen\" />
	  <Folder Include="Platforms\Windows\" />
	</ItemGroup>
</Project>
