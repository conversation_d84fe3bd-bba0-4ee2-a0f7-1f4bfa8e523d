using NUnit.Framework;
using DrMuscle.UITests.Helpers;
using DrMuscle.UITests.Pages;
using System;
using System.Threading;

namespace DrMuscle.UITests.Tests
{
    [TestFixture]
    public class NavigationTests : AppiumSetup
    {
        [Test]
        public void NavigateThroughMainTabs()
        {
            // Arrange
            var account = TestAccount.GenerateWithSeed("navigation");
            var loginPage = new LoginPage(Driver!);
            var mainPage = new MainPage(Driver!);
            
            Console.WriteLine($"Testing navigation with account: {account.email}");
            
            // Act - Login first
            Thread.Sleep(3000);
            loginPage.WaitForPageToLoad();
            loginPage.Login(account.email, account.password);
            
            Thread.Sleep(5000);
            TakeScreenshot("01-after-login");
            
            // Wait for main page to load
            mainPage.WaitForPageToLoad();
            Assert.That(mainPage.IsDisplayed(), "Main page should be displayed after login");
            
            // Navigate through tabs
            Console.WriteLine("Testing Home tab...");
            mainPage.NavigateToHome();
            Thread.Sleep(2000);
            TakeScreenshot("02-home-tab");
            Assert.That(mainPage.IsHomeTabActive(), "Home tab should be active");
            
            Console.WriteLine("Testing History tab...");
            mainPage.NavigateToHistory();
            Thread.Sleep(2000);
            TakeScreenshot("03-history-tab");
            Assert.That(mainPage.IsHistoryTabActive(), "History tab should be active");
            
            Console.WriteLine("Testing Chat tab...");
            mainPage.NavigateToChat();
            Thread.Sleep(2000);
            TakeScreenshot("04-chat-tab");
            Assert.That(mainPage.IsChatTabActive(), "Chat tab should be active");
            
            Console.WriteLine("Testing Settings tab...");
            mainPage.NavigateToSettings();
            Thread.Sleep(2000);
            TakeScreenshot("05-settings-tab");
            Assert.That(mainPage.IsSettingsTabActive(), "Settings tab should be active");
            
            // Return to Home
            mainPage.NavigateToHome();
            Thread.Sleep(1000);
            TakeScreenshot("06-back-to-home");
            
            Assert.Pass("Successfully navigated through all main tabs");
        }
        
        [Test]
        public void AccessSettingsAndLogout()
        {
            // Arrange
            var account = TestAccount.GenerateWithSeed("logout-test");
            var loginPage = new LoginPage(Driver!);
            var mainPage = new MainPage(Driver!);
            
            // Act - Login
            Thread.Sleep(3000);
            loginPage.WaitForPageToLoad();
            loginPage.Login(account.email, account.password);
            
            Thread.Sleep(5000);
            
            // Navigate to Settings
            mainPage.WaitForPageToLoad();
            mainPage.NavigateToSettings();
            Thread.Sleep(2000);
            TakeScreenshot("01-settings-page");
            
            // Look for logout option in settings
            var logoutButton = Driver?.FindElement(By.XPath("//XCUIElementTypeStaticText[contains(@name,'Log out') or contains(@name,'Logout') or contains(@name,'Sign out')]"));
            if (logoutButton != null)
            {
                logoutButton.Click();
                Thread.Sleep(3000);
                TakeScreenshot("02-after-logout");
                
                // Verify we're back at welcome screen
                Assert.That(loginPage.IsDisplayed(), "Should be back at login screen after logout");
            }
            else
            {
                Assert.Inconclusive("Could not find logout button in settings");
            }
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private void TakeScreenshot(string name)
        {
            TakeScreenshot(Driver, name);
        }
        #pragma warning restore CA1822 // Mark members as static
        
        private static void TakeScreenshot(AppiumDriver? driver, string name)
        {
            try
            {
                var screenshot = driver?.GetScreenshot();
                if (screenshot != null)
                {
                    var screenshotPath = Path.Combine(
                        TestContext.CurrentContext.WorkDirectory, 
                        $"{TestContext.CurrentContext.Test.Name}_{name}.png"
                    );
                    screenshot.SaveAsFile(screenshotPath);
                    TestContext.AddTestAttachment(screenshotPath, name);
                    Console.WriteLine($"Screenshot saved: {name}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to take screenshot {name}: {ex.Message}");
            }
        }
    }
}