namespace DrMuscle.UITests.Extensions
{
    public static class AppExtensions
    {
        private const int _defaultTimeout = 30;
        private const int _shortTimeout = 10;
        private const int _longTimeout = 60;

        /// <summary>
        /// Waits for an element to appear and returns it
        /// </summary>
        public static AppResult WaitForElement(this IApp app, string automationId, int timeoutSeconds = _defaultTimeout)
        {
            var results = app.WaitForElement(c => c.Marked(automationId), timeout: TimeSpan.FromSeconds(timeoutSeconds));
            return results.FirstOrDefault()
                ?? throw new InvalidOperationException($"Element '{automationId}' not found within {timeoutSeconds} seconds");
        }

        /// <summary>
        /// Waits for an element and taps it
        /// </summary>
        public static void WaitAndTap(this IApp app, string automationId, int timeoutSeconds = _defaultTimeout)
        {
            app.WaitForElement(automationId, timeoutSeconds);
            app.Tap(c => c.Marked(automationId));
        }

        /// <summary>
        /// Waits for an element and enters text
        /// </summary>
        public static void WaitAndEnterText(this IApp app, string automationId, string text, int timeoutSeconds = _defaultTimeout)
        {
            app.WaitForElement(automationId, timeoutSeconds);
            app.ClearText(c => c.Marked(automationId));
            app.EnterText(c => c.Marked(automationId), text);
        }

        /// <summary>
        /// Checks if an element exists without throwing
        /// </summary>
        public static bool ElementExists(this IApp app, string automationId, int timeoutSeconds = _shortTimeout)
        {
            try
            {
                var elements = app.WaitForElement(c => c.Marked(automationId), timeout: TimeSpan.FromSeconds(timeoutSeconds));
                return elements.Length > 0;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Waits for an element to disappear
        /// </summary>
        public static void WaitForNoElement(this IApp app, string automationId, int timeoutSeconds = _defaultTimeout)
        {
            var timeout = DateTime.UtcNow.AddSeconds(timeoutSeconds);
            while (DateTime.UtcNow < timeout)
            {
                if (!app.ElementExists(automationId, 1))
                    return;

                Thread.Sleep(500);
            }

            throw new TimeoutException($"Element '{automationId}' still visible after {timeoutSeconds} seconds");
        }

        /// <summary>
        /// Takes a screenshot with a descriptive name
        /// </summary>
        public static void Screenshot(this IApp app, string name)
        {
            app.Screenshot(name);
        }

        /// <summary>
        /// Captures screenshot on test failure
        /// </summary>
        public static void CaptureScreenshotOnFailure(this IApp app, string testName)
        {
            try
            {
                var timestamp = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss");
                var screenshotName = $"FAILED_{testName}_{timestamp}";
                app.Screenshot(screenshotName);
            }
            catch
            {
                // Don't fail the test if screenshot fails
            }
        }

        /// <summary>
        /// Scrolls until an element is visible
        /// </summary>
        public static void ScrollDownTo(this IApp app, string automationId, string? withinElement = null, int maxScrolls = 10)
        {
            for (int i = 0; i < maxScrolls; i++)
            {
                if (app.ElementExists(automationId, 1))
                    return;

                if (withinElement != null)
                    app.ScrollDownTo(c => c.Marked(automationId), c => c.Marked(withinElement), ScrollStrategy.Auto);
                else
                    app.ScrollDownTo(c => c.Marked(automationId));
            }

            throw new InvalidOperationException($"Could not find element '{automationId}' after {maxScrolls} scrolls");
        }
    }
}
