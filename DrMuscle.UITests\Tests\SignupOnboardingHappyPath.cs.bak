using DrMuscle.UITests.Extensions;
using DrMuscle.UITests.Helpers;
using DrMuscle.UITests.PageObjects;

namespace DrMuscle.UITests.Tests
{
    [TestFixture]
    public class SignupOnboardingHappyPath
    {
        private IApp? _app;
        private WelcomePage? _welcomePage;

        [SetUp]
        public void SetUp()
        {
            _app = AppInitializer.StartApp();
            _welcomePage = new WelcomePage(_app);
        }

        [Test]
        public void UserCanCompleteSignupAndOnboardingFlow()
        {
            // Arrange
            var (email, password) = TestAccount.Generate();

            // Act - Navigate through signup flow
            _app!.Screenshot("Welcome Page");
            _welcomePage!.WaitForPageToLoad();

            var registrationPage = _welcomePage.TapGetStarted();
            _app.Screenshot("Registration Page");

            registrationPage.WaitForPageToLoad();
            registrationPage.EnterEmail(email);
            registrationPage.EnterPassword(password);
            // No confirm password field in the app

            var onboardingPage = registrationPage.TapSignUp();
            _app.Screenshot("Onboarding Start");

            // Complete onboarding flow
            var mainPage = onboardingPage.CompleteOnboarding();
            _app.Screenshot("Main Page After Onboarding");

            // Assert - Verify we reached the main page with Start Workout visible
            mainPage.WaitForPageToLoad();
            Assert.That(mainPage.IsStartWorkoutButtonVisible(), Is.True,
                "Start Workout button should be visible after completing signup and onboarding");
        }
    }
}
