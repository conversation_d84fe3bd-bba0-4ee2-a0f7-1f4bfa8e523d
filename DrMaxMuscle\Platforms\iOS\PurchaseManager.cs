﻿using Controls.UserDialogs.Maui;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Platforms.iOS.Dependencies;
using DrMuscleWebApiSharedModel;
using Foundation;
using Newtonsoft.Json.Linq;
using Microsoft.Maui.Networking;
using Plugin.InAppBilling;
using StoreKit;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Rollbar;


namespace DrMaxMuscle.Platforms.iOS
{
    public class PurchaseManager
    {
        private static PurchaseManager _purchaseManager;
        private IEnumerable<InAppBillingProduct> _billingSubscription;
        private SKProduct _skProductMonthly;

        private IEnumerable<InAppBillingPurchase> _purchasedSubscription;

        // private InAppPurchaseManager _manager;
        public delegate void LifetimeAccessPurchased();
        private string _payload = "b1b4d8c8d7f3464c8486a731a01e8c59";
        public event MonthlyAccessPurchased OnMonthlyAccessPurchased;
        public event MonthlyAccessPurchased OnYearlyAccessPurchased;
        public event MonthlyAccessPurchased OnMealPlanAccessPurchased;
        public delegate void MonthlyAccessPurchased();
        public delegate void YearlyAccessPurchased();
        public delegate void MealPlanAccessPurchased();
        public bool IsValidUserForDiscount { get; set; }
        public bool IsMonthly = false;
        public bool IsYearly = false;
        bool isOpenAlert = false;
        bool isMealRunning = false;
        private PurchaseManager()
        {
            IsValidUserForDiscount = true;
            if (Connectivity.NetworkAccess == NetworkAccess.Internet)
                LoadProducts();
            else
            {

            }
        }
        public async void LoadProducts()
        {
            try
            {
                var billing = CrossInAppBilling.Current;
                if (billing == null)
                {
                    Console.WriteLine("InAppBilling is not available");
                    //return;
                }

                //_billingProduct = await CrossInAppBilling.Current.GetProductInfoAsync(ItemType.InAppPurchase, new string[] { "drmuscle.lifetimeaccess.nonconsumable" });
                try
                {
                    var str = new string[] { "monthly49", "annual499", "mealaddon" };
                    _billingSubscription = await CrossInAppBilling.Current.GetProductInfoAsync(ItemType.Subscription, str);
                }
                catch (MissingMethodException ex)
                {
                    Console.WriteLine($"InAppBilling method not found: {ex.Message}");
                }

                var request = new SKProductsRequest(new NSSet(new string[] { "monthly49", "annual499", "mealaddon" }));
                request.Start();
                request.ReceivedResponse += Request_ReceivedResponse;

                NSUrl receiptURL = NSBundle.MainBundle.AppStoreReceiptUrl;
                NSData receipt = NSData.FromUrl(receiptURL);
                if (receipt != null)
                    LoadPurchases();


            }
            catch (System.Exception ex)
            {
                Console.WriteLine($"InAppBilling method not found: {ex.Message}");
                Console.WriteLine($"Error in LoadProducts: {ex.Message}");
                RollbarLocator.RollbarInstance.Error(ex);

            }


        }

        void Request_ReceivedResponse(object sender, SKProductsRequestResponseEventArgs e)
        {
            if (e.Response.Products != null)
            {
                _skProductMonthly = e.Response.Products.Where(x => x.ProductIdentifier.Equals("monthly49")).FirstOrDefault();
            }
        }

        public async void LoadPurchases()
        {
            try
            {
                var billing = CrossInAppBilling.Current;
                try
                {
                    var connected = await billing.ConnectAsync(true);
                    if (!connected)
                    {
                        //we are offline or can't connect, don't try to purchase
                        return;
                    }
                    //check purchases
                    var verify = DependencyService.Get<IInAppBillingVerifyPurchase>();

                    _purchasedSubscription = await billing.GetPurchasesAsync(ItemType.Subscription);

                    //IsValidSubscription("monthly49");
                    IsValidSubscription();
                }
                catch (InAppBillingPurchaseException purchaseEx)
                {
                    Console.WriteLine("Error: " + purchaseEx);
                }
                catch (Exception ex)
                {
                    Console.WriteLine("Issue connecting: " + ex);
                }
                finally
                {
                    await billing.DisconnectAsync();
                }
            }
            catch (Exception ex)
            {

            }
        }

        public void RestorePurchase()
        {
            LoadProducts();
        }

        public SKProduct SkProductMonthly
        {
            get
            {
                return _skProductMonthly;
            }
        }
        public InAppBillingProduct MealPlanAddOnProduct
        {
            get
            {
                try
                {
                    return _billingSubscription?.Where(x => x.ProductId.Equals("mealaddon")).FirstOrDefault();
                }
                catch (System.Exception ex)
                {
                    return null;
                }
            }
        }

        public InAppBillingProduct MonthlyAccessProduct
        {
            get
            {
                try
                {
                    return _billingSubscription?.Where(x => x.ProductId.Equals("monthly49")).FirstOrDefault();
                }
                catch (System.Exception ex)
                {
                    return null;
                }
            }
        }

        public InAppBillingProduct YearlyAccessProduct
        {
            get
            {
                try
                {
                    return _billingSubscription?.Where(x => x.ProductId.Equals("annual499")).FirstOrDefault();

                }
                catch (System.Exception ex)
                {
                    return null;
                }
            }
        }

        public async void BuyProduct(string productId)
        {

            var billing = CrossInAppBilling.Current;
            try
            {
                //await billing.PurchaseAsync(productId, ItemType.InAppPurchase, _payload);
                await billing.PurchaseAsync(productId, ItemType.InAppPurchase);
            }
            catch (InAppBillingPurchaseException purchaseEx)
            {
                //Billing Exception handle this based on the type
                Console.WriteLine("Error: " + purchaseEx);
            }
            catch (Exception ex)
            {
                //Something else has gone wrong, log it
                Console.WriteLine("Issue connecting: " + ex);
            }
            finally
            {
                await billing.DisconnectAsync();
            }

        }

        public async void BuyMealPlanSubscription()
        {
            if (isMealRunning)
                return;
            if (!isMealRunning)
                isMealRunning = true;
            if (!CheckProductionPurchased())
            {
                AlertForPublicBuild();
                isMealRunning = false;
                return;
            }
            var billing = CrossInAppBilling.Current;
            await billing.DisconnectAsync();
            var connected = await billing.ConnectAsync();
            if (!connected)
            {
                //we are offline or can't connect, don't try to purchase
                return;
            }
            try
            {

                //var subscription = await billing.PurchaseAsync("monthly49", ItemType.Subscription, _payload);
                UserDialogs.Instance.ShowLoading("Loading...");
                var subscription = await billing.PurchaseAsync("mealaddon", ItemType.Subscription);
                UserDialogs.Instance.HideHud();
                if (subscription == null)
                {
                    //did not purchase
                    //UserDialogs.Instance.Alert("Did not purchased", "Error", "Ok");
                }
                else if (subscription.State == PurchaseState.Purchased)
                {
                    new Firebase_iOS().LogEvent("ios_mealaddon_purchased", "mealaddon");
                    //purchased!
                    if (CheckProductionPurchased())
                    {
                        if (OnMealPlanAccessPurchased != null)
                            OnMealPlanAccessPurchased();
                        subscription.TransactionDateUtc = DateTime.Now.ToUniversalTime().AddMonths(1);
                        SendDataToServer(subscription, "SubscriptionPurchaseMessage");
                        if (subscription.ProductId == "mealaddon")
                        {
                            App.IsMealPlan = true;
                        }
                        else
                        {
                            App.IsV1User = true;
                            App.IsV1UserTrial = true;
                        }
                        try
                        {
                            var isconsume = await CrossInAppBilling.Current.ConsumePurchaseAsync(subscription.ProductId,
                    subscription.TransactionIdentifier);
                            var isAcknowledge = await CrossInAppBilling.Current.FinalizePurchaseAsync(new[] { subscription.TransactionIdentifier });
                            System.Diagnostics.Debug.WriteLine($"IsConsume: {isconsume}, Acknoledge :{isAcknowledge}");
                        }
                        catch (Exception ex)
                        {
                            // Log the main exception first
                           // RollbarLocator.RollbarInstance.Error(ex);

                            // Log the inner exception if it exists
                           
                        }

                        IsValidSubscription();
                        LoadPurchases();
                    }
                    else
                    {
                        //Give error msg
                        AlertForPublicBuild();
                    }
                }

            }
            catch (InAppBillingPurchaseException purchaseEx)
            {
                UserDialogs.Instance.HideHud();
                //Billing Exception handle this based on the type
                Console.WriteLine("Error: " + purchaseEx);
                //UserDialogs.Instance.Alert(purchaseEx.PurchaseError.ToString(), "Error", "Ok");
            }
            catch (Exception ex)
            {
                UserDialogs.Instance.HideHud();
                //Something else has gone wrong, log it
                Console.WriteLine("Issue connecting: " + ex);
            }
            finally
            {
                await billing.DisconnectAsync();
                isMealRunning = false;
                UserDialogs.Instance.HideHud();
            }
        }

        private async void AlertForPublicBuild()
        {
            if (isOpenAlert)
                return;
            isOpenAlert = true;
            var isOpen = await Acr.UserDialogs.UserDialogs.Instance.ConfirmAsync("You have to subscribe from app store build to access premium features.", null, "Open App Store", "Cancel"); ;
            isOpenAlert = false;
            if (isOpen)
            {
                Browser.OpenAsync("https://apps.apple.com/us/app/dr-muscle-ai-personal-trainer/id1073943857", BrowserLaunchMode.SystemPreferred);

                //Browser.OpenAsync("itms-apps://itunes.apple.com/app/id1073943857", BrowserLaunchMode.SystemPreferred);
                //Device.OpenUri(new Uri("itms-apps://itunes.apple.com/app/id1073943857"));
            }
            //UserDialogs.Instance.Alert(ShowExplainRIRPopUp);
        }

        public bool CheckProductionPurchased()
        {
            try
            {
                if (!string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("Environment")?.Value) && LocalDBManager.Instance.GetDBSetting("Environment")?.Value != "Production")
                {
                    return true;
                }
                NSUrl receiptURL = NSBundle.MainBundle.AppStoreReceiptUrl;
                if (receiptURL == null)
                {
                    return false;
                }
                return !receiptURL.LastPathComponent.Contains("sandboxReceipt");
                // here is the code I was missing
                //NSDictionary requestContents = NSDictionary.FromObjectAndKey((NSString)receipt.GetBase64EncodedString(
                //            NSDataBase64EncodingOptions.None),
                //            (NSString)"receipt-data");

                //string receiptData = (requestContents["receipt-data"] as NSString).ToString();

                //var json = new JObject(new JProperty("receipt-data", receiptData), new JProperty("password", "b1b4d8c8d7f3464c8486a731a01e8c59")).ToString();

                //ASCIIEncoding ascii = new ASCIIEncoding();
                //byte[] postBytes = Encoding.UTF8.GetBytes(json);

                ////  HttpWebRequest request;
                ////https://sandbox.itunes.apple.com
                //var request = System.Net.HttpWebRequest.Create("https://buy.itunes.apple.com/verifyReceipt");
                //request.Method = "POST";
                //request.ContentType = "application/json";
                //request.ContentLength = postBytes.Length;

                //using (var stream = request.GetRequestStream())
                //{
                //    stream.Write(postBytes, 0, postBytes.Length);
                //    stream.Flush();
                //}

                //var sendresponse = request.GetResponse();

                //string sendresponsetext = "";
                //using (var streamReader = new StreamReader(sendresponse.GetResponseStream()))
                //{
                //    sendresponsetext = streamReader.ReadToEnd().Trim();
                //}

                //JObject response = JObject.Parse(sendresponsetext);
                ////Check mealplan access
                //try
                //{
                //    //mealaddon
                //    var env = Convert.ToString(response["environment"]);
                //    if (env.Equals("environment"))
                //        return true;

                //}
                //catch (Exception ex)
                //{

                //}
                //return false;
                // Check if the receipt was generated in a sandbox environment

            }
            catch
            {

            }
            return false;
        }
        public async void BuyMonthlySubscription()
        {
            if (!CheckProductionPurchased())
            {
                AlertForPublicBuild();
                return;
            }
            var billing = CrossInAppBilling.Current;
            var connected = await billing.ConnectAsync();
            if (!connected)
            {
                //we are offline or can't connect, don't try to purchase
                return;
            }
            try
            {
                
                //var subscription = await billing.PurchaseAsync("monthly49", ItemType.Subscription, _payload);
                UserDialogs.Instance.ShowLoading("Loading...");
                var subscription = await billing.PurchaseAsync("monthly49", ItemType.Subscription);
                UserDialogs.Instance.HideHud();
                if (subscription == null)
                {
                    //did not purchase
                    UserDialogs.Instance.Alert("Did not purchased", "Error", "Ok");
                }
                else if (subscription.State == PurchaseState.Purchased)
                {
                    new Firebase_iOS().LogEvent("ios_app_purchased", "monthly49");
                    try
                    {
                        var isconsume = await CrossInAppBilling.Current.ConsumePurchaseAsync(subscription.ProductId,
                            subscription.TransactionIdentifier);
                        System.Diagnostics.Debug.WriteLine($"IsConsume: {isconsume}");
                    }
                    catch (Exception ex)
                    {
                        // Log the main exception first
                        // RollbarLocator.RollbarInstance.Error(ex);

                        // Log the inner exception if it exists
                    }

                    //purchased!
                    if (CheckProductionPurchased())
                    {
                        if (OnMonthlyAccessPurchased != null)
                            OnMonthlyAccessPurchased();
                        subscription.TransactionDateUtc = DateTime.Now.ToUniversalTime().AddMonths(1);
                        SendDataToServer(subscription, "SubscriptionPurchaseMessage");
                        App.IsFreePlan = false;
                        App.IsV1User = true;
                        App.IsV1UserTrial = true;
                        
                        //IsValidSubscription();
                        LoadPurchases();
                    }
                    else { AlertForPublicBuild(); }
                }

            }
            catch (InAppBillingPurchaseException purchaseEx)
            {
                UserDialogs.Instance.HideHud();
                //Billing Exception handle this based on the type
                //UserDialogs.Instance.Alert(purchaseEx.PurchaseError.ToString(), "Error", "Ok");
                Console.WriteLine("Error: " + purchaseEx);
            }
            catch (Exception ex)
            {
                UserDialogs.Instance.HideHud();
                //Something else has gone wrong, log it
                Console.WriteLine("Issue connecting: " + ex);
            }
            finally
            {
                UserDialogs.Instance.HideHud();
                await billing.DisconnectAsync();

            }
        }

        public async void BuyYearlySubscription()
        {
            if (!CheckProductionPurchased())
            {
                AlertForPublicBuild();
                return;
            }
            var billing = CrossInAppBilling.Current;
            var connected = await billing.ConnectAsync();
            if (!connected)
            {
                //we are offline or can't connect, don't try to purchase
                return;
            }
            try
            {
                //var subscription = await billing.PurchaseAsync("annual499", ItemType.Subscription, _payload);
                UserDialogs.Instance.ShowLoading("Loading...");
                var subscription = await billing.PurchaseAsync("annual499", ItemType.Subscription);
                UserDialogs.Instance.HideHud();
                if (subscription == null)
                {
                    //did not purchase
                    
                    UserDialogs.Instance.Alert("Did not purchased", "Error", "Ok");
                }
                else if (subscription.State == PurchaseState.Purchased)
                {
                    new Firebase_iOS().LogEvent("ios_app_purchased", "annual499");
                    try
                    {
                        var isconsume = await CrossInAppBilling.Current.ConsumePurchaseAsync(subscription.ProductId,
                    subscription.TransactionIdentifier);
                        System.Diagnostics.Debug.WriteLine($"IsConsume: {isconsume}");
                    }
                    catch (Exception ex)
                    {

                    }
                    if (CheckProductionPurchased())
                    {
                        if (OnYearlyAccessPurchased != null)
                            OnYearlyAccessPurchased();
                        subscription.TransactionDateUtc = DateTime.Now.ToUniversalTime().AddYears(1);
                        SendDataToServer(subscription, "SubscriptionPurchaseMessage");
                        //IsValidSubscription();
                        LoadPurchases();
                        //if (subscription.ProductId == "mealaddon")
                        //{
                        //    App.IsMealPlan = true;
                        //}
                        //else
                        //{
                        App.IsFreePlan = false;
                        App.IsV1User = true;
                        App.IsV1UserTrial = true;
                        //}
                    }
                    else { AlertForPublicBuild(); }
                }

            }
            catch (InAppBillingPurchaseException purchaseEx)
            {
                UserDialogs.Instance.HideHud();
                //Billing Exception handle this based on the type
                Console.WriteLine("Error: " + purchaseEx);
                //UserDialogs.Instance.Alert(purchaseEx.PurchaseError.ToString(), "Error", "Ok");
            }
            catch (Exception ex)
            {
                UserDialogs.Instance.HideHud();
                //Something else has gone wrong, log it
                Console.WriteLine("Issue connecting: " + ex);
            }
            finally
            {
                await billing.DisconnectAsync();
                UserDialogs.Instance.HideHud();
            }
        }

        public bool IsYearlyAccessSubscribe()
        {

            if (_purchasedSubscription == null)
                return false;

            if (_purchasedSubscription.Any(l => l.ProductId == "annual499") && CheckProductionPurchased())
            {

                var purchase = _purchasedSubscription.Where(l => l.ProductId == "annual499").FirstOrDefault();
                if (purchase == null)
                    return true;
                if (!CurrentLog.Instance.SendInformationToServer)
                {
                    CurrentLog.Instance.SendInformationToServer = true;
                    SendDataToServer(purchase, "SubscriptionPurchaseMessage");
                    SendDataToServer(purchase, "SubscriptionPurchaseIfNotExistMessage");
                }
            }
            return IsYearly;

        }

        public bool IsMonthlyAccessSubscribe()
        {
            if (_purchasedSubscription == null)
                return false;

            if (_purchasedSubscription.Any(l => l.ProductId == "monthly49") && CheckProductionPurchased())
            {
                var purchase = _purchasedSubscription.Where(l => l.ProductId == "monthly49").FirstOrDefault();
                if (purchase == null)
                    return true;
                if (!CurrentLog.Instance.SendInformationToServer)
                {
                    CurrentLog.Instance.SendInformationToServer = true;
                    SendDataToServer(purchase, "SubscriptionPurchaseMessage");
                    SendDataToServer(purchase, "SubscriptionPurchaseIfNotExistMessage");
                }
            }
            return IsMonthly;
        }

        public bool IsMealPlanAccessSubscribe()
        {
            if (_purchasedSubscription == null)
                return false;

            if (_purchasedSubscription.Any(l => l.ProductId == "mealaddon") && CheckProductionPurchased())
            {
                var purchase = _purchasedSubscription.Where(l => l.ProductId == "mealaddon").FirstOrDefault();
                if (purchase == null)
                    return true;
                SendDataToServer(purchase, "SubscriptionPurchaseMessage");
                SendDataToServer(purchase, "SubscriptionPurchaseIfNotExistMessage");
            }
            return false;
        }

        public bool IsActiveSubscription()
        {

            if (_purchasedSubscription != null && CheckProductionPurchased())
            {
                foreach (var item in _purchasedSubscription)
                {
                    try
                    {
                        CrossInAppBilling.Current.ConsumePurchaseAsync(item.ProductId,
                        item.TransactionIdentifier).ContinueWith((b) => {
                            Console.WriteLine(b);
                        });
                    }
                    catch (Exception ex)
                    {
                        // Log the main exception first
                       // RollbarLocator.RollbarInstance.Error(ex);

                        // Log the inner exception if it exists
                        
                    }

                }
                if (_purchasedSubscription.Any(l => l.ProductId == "monthly49"))
                    return true;
                if (_purchasedSubscription.Any(l => l.ProductId == "annual499"))
                    return true;
            }
            return false;
        }

        public bool IsActiveMealPlan()
        {
            if (_purchasedSubscription != null && CheckProductionPurchased())
            {
                if (_purchasedSubscription.Any(l => l.ProductId == "mealaddon"))
                    return true;

            }
            return false;
        }

        private void SendDataToServer(InAppBillingPurchase purchase, string msg)
        {

            SubscriptionModel subscriptions = new SubscriptionModel()
            {
                PurchaseToken = GetReceipt(),
                ExpiryDate = purchase.TransactionDateUtc,
                ProductId = purchase.ProductId,
                Platform = 1,
                OrderId = purchase.Id,
            };
            MessagingCenter.Send<SubscriptionModel>(subscriptions, msg);
        }

        private string GetReceipt()
        {
            NSUrl receiptURL = NSBundle.MainBundle.AppStoreReceiptUrl;
            NSData receipt = NSData.FromUrl(receiptURL);
            if (receipt != null)
            {
                // here is the code I was missing
                NSDictionary requestContents = NSDictionary.FromObjectAndKey((NSString)receipt.GetBase64EncodedString(
                        NSDataBase64EncodingOptions.None),
                        (NSString)"receipt-data");

                return (requestContents["receipt-data"] as NSString).ToString();
            }
            else
            {
                return null;
            }
                
        }



        public bool IsValidSubscription()
        {
            try
            {

                NSUrl receiptURL = NSBundle.MainBundle.AppStoreReceiptUrl;
                NSData receipt = NSData.FromUrl(receiptURL);
                if (receipt == null)
                {
                    IsValidUserForDiscount = true;
                    return false;
                }

                // here is the code I was missing
                NSDictionary requestContents = NSDictionary.FromObjectAndKey((NSString)receipt.GetBase64EncodedString(
                            NSDataBase64EncodingOptions.None),
                            (NSString)"receipt-data");

                string receiptData = (requestContents["receipt-data"] as NSString).ToString();

                var json = new JObject(new JProperty("receipt-data", receiptData), new JProperty("password", "b1b4d8c8d7f3464c8486a731a01e8c59")).ToString();

                ASCIIEncoding ascii = new ASCIIEncoding();
                byte[] postBytes = Encoding.UTF8.GetBytes(json);

                //  HttpWebRequest request;
                //
                var request = receiptURL.LastPathComponent.Contains("sandboxReceipt") ? System.Net.HttpWebRequest.Create("https://sandbox.itunes.apple.com/verifyReceipt") : System.Net.HttpWebRequest.Create("https://buy.itunes.apple.com/verifyReceipt");
                request.Method = "POST";
                request.ContentType = "application/json";
                request.ContentLength = postBytes.Length;

                using (var stream = request.GetRequestStream())
                {
                    stream.Write(postBytes, 0, postBytes.Length);
                    stream.Flush();
                }

                var sendresponse = request.GetResponse();

                string sendresponsetext = "";
                using (var streamReader = new StreamReader(sendresponse.GetResponseStream()))
                {
                    sendresponsetext = streamReader.ReadToEnd().Trim();
                }

                JObject response = JObject.Parse(sendresponsetext);
                try
                {
                    CrossInAppBilling.Current.FinalizePurchaseAsync(GetTransactionIds(response));
                    CrossInAppBilling.Current.FinalizePurchaseAsync(GetLatestTransactionIds(response));
                }
                catch (Exception ex)
                {

                }
                if (!App.IsMealPlan)
                    App.IsMealPlan = IsSubscriptionExpired(response, "mealaddon") != null;

                var isMonth = IsSubscriptionExpired(response, "monthly49");
                if (isMonth != null)
                {
                    App.IsV1User = true;
                    App.IsV1UserTrial = true;
                    SubscriptionModel subscriptions = new SubscriptionModel()
                    {
                        PurchaseToken = GetReceipt(),
                        ExpiryDate = DateTime.ParseExact(isMonth["expires_date"].ToString().Replace(" Etc/GMT", ""), "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture),
                        ProductId = isMonth["product_id"].ToString(),
                        Platform = 1,
                        OrderId = isMonth["transaction_id"].ToString(),
                    };
                    IsMonthly = true;
                    //send to server
                    MessagingCenter.Send<SubscriptionModel>(subscriptions, "SubscriptionPurchaseMessage");
                }

                var isYear = IsSubscriptionExpired(response, "annual499");
                if (isYear != null)
                {
                    App.IsV1User = true;
                    App.IsV1UserTrial = true;
                    SubscriptionModel subscriptions = new SubscriptionModel()
                    {
                        ExpiryDate = DateTime.ParseExact(isYear["expires_date"].ToString().Replace(" Etc/GMT", ""), "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture),
                        PurchaseToken = GetReceipt(),
                        ProductId = isYear["product_id"].ToString(),
                        Platform = 1,
                        OrderId = isYear["transaction_id"].ToString(),
                    };
                    IsYearly = true;
                    //send to server
                    MessagingCenter.Send<SubscriptionModel>(subscriptions, "SubscriptionPurchaseMessage");

                }
                return IsMonthly || IsYearly;
                //Check mealplan access
                try
                {
                    //mealaddon
                    bool isMealPlan = response["receipt"]["in_app"].Children().Any(c => c["product_id"].ToString() == "mealaddon" &&
                                                                          DateTime.ParseExact(c["expires_date"].ToString().Replace(" Etc/GMT", ""), "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture) > DateTime.Now.ToUniversalTime());
                    if (!App.IsMealPlan)
                        App.IsMealPlan = isMealPlan;

                }
                catch (Exception ex)
                {

                }
                //Check for monthly


                IsMonthly = response["receipt"]["in_app"].Children().Any(c => c["product_id"].ToString() == "monthly49" &&
                                                                          DateTime.ParseExact(c["expires_date"].ToString().Replace(" Etc/GMT", ""), "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture) > DateTime.Now.ToUniversalTime());

                if (IsMonthly)
                {
                    //send expirydate to server

                    foreach (var item in response["receipt"]["in_app"])
                    {
                        if (DateTime.ParseExact(item["expires_date"].ToString().Replace(" Etc/GMT", ""), "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture) > DateTime.Now.ToUniversalTime())
                        {
                            App.IsV1User = true;
                            App.IsV1UserTrial = true;
                            SubscriptionModel subscriptions = new SubscriptionModel()
                            {
                                PurchaseToken = GetReceipt(),
                                ExpiryDate = DateTime.ParseExact(item["expires_date"].ToString().Replace(" Etc/GMT", ""), "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture),
                                ProductId = item["product_id"].ToString(),
                                Platform = 1,
                                OrderId = item["transaction_id"].ToString(),
                            };
                            IsMonthly = true;
                            //send to server
                            MessagingCenter.Send<SubscriptionModel>(subscriptions, "SubscriptionPurchaseMessage");
                            break;
                        }
                    }
                    return IsMonthly;
                }
                if (!IsMonthly)
                {
                    try
                    {
                        IsMonthly = response["latest_receipt_info"].Children().Any(c => c["product_id"].ToString() == "monthly49" &&
                                                                             DateTime.ParseExact(c["expires_date"].ToString().Replace(" Etc/GMT", ""), "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture) > DateTime.Now.ToUniversalTime());

                        if (IsMonthly)
                        {
                            App.IsV1User = true;
                            App.IsV1UserTrial = true;
                            foreach (var item in response["latest_receipt_info"])
                            {
                                if (DateTime.ParseExact(item["expires_date"].ToString().Replace(" Etc/GMT", ""), "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture) > DateTime.Now.ToUniversalTime())
                                {
                                    SubscriptionModel subscriptions = new SubscriptionModel()
                                    {
                                        PurchaseToken = GetReceipt(),
                                        ExpiryDate = DateTime.ParseExact(item["expires_date"].ToString().Replace(" Etc/GMT", ""), "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture),
                                        ProductId = item["product_id"].ToString(),
                                        Platform = 1,
                                        OrderId = item["transaction_id"].ToString(),
                                    };
                                    IsMonthly = true;
                                    //send to server
                                    MessagingCenter.Send<SubscriptionModel>(subscriptions, "SubscriptionPurchaseMessage");
                                    break;
                                }
                            }
                            return IsMonthly;
                        }
                    }
                    catch (Exception ex)
                    {

                    }
                }

                //check for yearly
                IsYearly = response["receipt"]["in_app"].Children().Any(c => c["product_id"].ToString() == "annual499" &&
                                                                             DateTime.ParseExact(c["expires_date"].ToString().Replace(" Etc/GMT", ""), "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture) > DateTime.Now.ToUniversalTime());

                if (IsYearly)
                {


                    foreach (var item in response["receipt"]["in_app"])
                    {
                        if (DateTime.ParseExact(item["expires_date"].ToString().Replace(" Etc/GMT", ""), "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture) > DateTime.Now.ToUniversalTime())
                        {
                            App.IsV1User = true;
                            App.IsV1UserTrial = true;
                            SubscriptionModel subscriptions = new SubscriptionModel()
                            {
                                ExpiryDate = DateTime.ParseExact(item["expires_date"].ToString().Replace(" Etc/GMT", ""), "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture),
                                PurchaseToken = GetReceipt(),
                                ProductId = item["product_id"].ToString(),
                                Platform = 1,
                                OrderId = item["transaction_id"].ToString(),
                            };

                            //send to server
                            MessagingCenter.Send<SubscriptionModel>(subscriptions, "SubscriptionPurchaseMessage");

                            break;
                        }
                    }



                    return IsYearly;
                }
                if (!IsYearly)
                {
                    try
                    {
                        IsYearly = response["latest_receipt_info"].Children().Any(c => c["product_id"].ToString() == "annual499" &&
                                                                             DateTime.ParseExact(c["expires_date"].ToString().Replace(" Etc/GMT", ""), "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture) > DateTime.Now.ToUniversalTime());
                        if (IsYearly)
                        {
                            foreach (var item in response["latest_receipt_info"])
                            {
                                if (DateTime.ParseExact(item["expires_date"].ToString().Replace(" Etc/GMT", ""), "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture) > DateTime.Now.ToUniversalTime())
                                {
                                    App.IsV1User = true;
                                    App.IsV1UserTrial = true;
                                    SubscriptionModel subscriptions = new SubscriptionModel()
                                    {
                                        ExpiryDate = DateTime.ParseExact(item["expires_date"].ToString().Replace(" Etc/GMT", ""), "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture),
                                        PurchaseToken = GetReceipt(),
                                        ProductId = item["product_id"].ToString(),
                                        Platform = 1,
                                        OrderId = item["transaction_id"].ToString(),
                                    };

                                    //send to server
                                    MessagingCenter.Send<SubscriptionModel>(subscriptions, "SubscriptionPurchaseMessage");

                                    break;
                                }
                            }
                            return IsYearly;
                        }
                    }
                    catch (Exception ex)
                    {

                    }
                }

            }
            catch (Exception ex)
            {

            }
            return false;
        }

      

       

        private JToken IsSubscriptionExpired(JObject response, string productId)
        {
            try
            {
                var receipts = response["receipt"]["in_app"] as JArray;
                foreach (var item in receipts)
                {
                    
                    if ((string)item["product_id"] == productId)
                    {
                        CrossInAppBilling.Current.ConsumePurchaseAsync(productId,
                                    (string)item["transaction_id"]);
                        var expiresDateString = (string)item["expires_date_ms"];
                        if (long.TryParse(expiresDateString, out long expiresDateMs))
                        {
                            var expiresDate = DateTimeOffset.FromUnixTimeMilliseconds(expiresDateMs).UtcDateTime;
                            if (DateTime.UtcNow < expiresDate)
                                return item;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the main exception first
            //    RollbarLocator.RollbarInstance.Error(ex);

                // Log the inner exception if it exists
           
            }
            var status = (int)response["status"];
            if (status != 0)
            {
                // Status code other than 0 indicates an error in receipt validation
                //throw new Exception($"Receipt validation failed with status code: {status}");
                return false;
            }
            try
            {

            // Extract the latest receipt information
            var receiptInfo = response["latest_receipt_info"] as JArray;
            foreach (var item in receiptInfo)
            {
                if ((string)item["product_id"] == productId)
                {
                    CrossInAppBilling.Current.ConsumePurchaseAsync(productId,
                            (string)item["transaction_id"]);
                    var expiresDateString = (string)item["expires_date_ms"];
                    if (long.TryParse(expiresDateString, out long expiresDateMs))
                    {
                        var expiresDate = DateTimeOffset.FromUnixTimeMilliseconds(expiresDateMs).UtcDateTime;
                        if (DateTime.UtcNow < expiresDate)
                            return item;
                    }
                }
                
            }

            }
            catch (Exception ex)
            {
                // Log the main exception first
                //RollbarLocator.RollbarInstance.Error(ex);

                // Log the inner exception if it exists
              
            }

            // If the product ID is not found, treat it as expired
            return null;
        }


        public string[] GetLatestTransactionIds(JObject jsonResponse)
        {
            // Check if the "latest_receipt_info" field exists
            if (jsonResponse["latest_receipt_info"] != null)
            {
                var latestReceiptInfo = jsonResponse["latest_receipt_info"] as JArray;

                // Loop through each item in the "latest_receipt_info" array
                return latestReceiptInfo
                .Select(item => item["transaction_id"]?.ToString())
                .Where(transactionId => !string.IsNullOrEmpty(transactionId))
                .ToArray();
            }
            else
            {
                Console.WriteLine("No latest_receipt_info found in the response.");
            }
            return Array.Empty<string>();
        }

        public string[] GetTransactionIds(JObject jsonResponse)
        {
            if (jsonResponse["receipt"]["in_app"] != null)
            {
                var latestReceiptInfo = jsonResponse["receipt"]["in_app"] as JArray;

                // Loop through each item in the "latest_receipt_info" array
                return latestReceiptInfo
                .Select(item => item["transaction_id"]?.ToString())
                .Where(transactionId => !string.IsNullOrEmpty(transactionId))
                .ToArray();
            }
            else
            {
                Console.WriteLine("No latest_receipt_info found in the response.");
            }

            return Array.Empty<string>();
        }


        //is_trial_period //is_in_intro_offer_period
        public static PurchaseManager Instance
        {
            get
            {
                if (_purchaseManager == null)
                    _purchaseManager = new PurchaseManager();
                return _purchaseManager;
            }
        }

        private static string ConvertReceiptToPost(string receipt)
        {
            //string itunesDecodedReceipt = Encoding.UTF8.GetString(ReceiptVerification.ConvertAppStoreTokenToBytes(receipt.Replace("<", string.Empty).Replace(">", string.Empty))).Trim();
            string itunesDecodedReceipt = receipt.Replace("<", string.Empty).Replace(">", string.Empty).Trim();
            string encodedReceipt = Base64Encode(itunesDecodedReceipt);
            return string.Format(@"{{""receipt-data"":""{0}""}}", encodedReceipt);
        }

        /// <summary>
        /// Base64 Encoding
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        private static string Base64Encode(string str)
        {
            byte[] encbuff = System.Text.Encoding.UTF8.GetBytes(str);
            return Convert.ToBase64String(encbuff);
        }
    }
}
