using NUnit.Framework;
using DrMuscle.UITests.Helpers;
using DrMuscle.UITests.Pages;
using System;
using System.Threading;
using System.Diagnostics;
using OpenQA.Selenium.Appium;

namespace DrMuscle.UITests.Tests
{
    /// <summary>
    /// Comprehensive tests for Timer features
    /// Tests rest timers, auto-start, customization, and background behavior
    /// </summary>
    [TestFixture]
    [Category(TestCategories.CoreFeatures)]
    public class TimerFeatureTests : AppiumSetup
    {
        private WorkoutPage workoutPage = null!;
        private TimerPage timerPage = null!;
        private SettingsPage settingsPage = null!;
        
        [SetUp]
        public void TestSetup()
        {
            workoutPage = new WorkoutPage(Driver!);
            timerPage = new TimerPage(Driver!);
            settingsPage = new SettingsPage(Driver!);
            
            // Login and start workout
            LoginAndStartWorkout();
        }
        
        [Test]
        [Order(1)]
        [Description("Tests full rest timer cycle from start to completion")]
        public void TEST_Rest_Timer_Full_Cycle()
        {
            TestContext.WriteLine("=== Testing Rest Timer Full Cycle ===");
            
            // Navigate to exercise
            workoutPage.SelectExerciseByName("Bench Press");
            Thread.Sleep(2000);
            
            // Complete a set
            workoutPage.EnterSet("10", "135");
            
            // Record time before saving set
            var startTime = DateTime.Now;
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            // Verify timer starts automatically
            Assert.That(timerPage.IsTimerRunning(), Is.True,
                "Timer should start automatically after saving set");
            
            TakeScreenshot("01-timer-started");
            
            // Get initial timer value
            var initialTime = timerPage.GetRemainingTime();
            TestContext.WriteLine($"Initial timer value: {initialTime}");
            
            // Verify correct rest time based on exercise type
            var expectedRestTime = GetExpectedRestTime("Bench Press");
            Assert.That(ParseTimeToSeconds(initialTime), Is.InRange(expectedRestTime - 5, expectedRestTime + 5),
                $"Timer should start near {expectedRestTime} seconds for compound exercise");
            
            // Wait for timer to count down
            Thread.Sleep(3000);
            
            var timeAfter3Seconds = timerPage.GetRemainingTime();
            TestContext.WriteLine($"Timer after 3 seconds: {timeAfter3Seconds}");
            
            // Verify timer is counting down
            Assert.That(ParseTimeToSeconds(timeAfter3Seconds), Is.LessThan(ParseTimeToSeconds(initialTime)),
                "Timer should be counting down");
            
            TakeScreenshot("02-timer-counting");
            
            // Wait for timer to reach near zero
            TestContext.WriteLine("Waiting for timer to complete...");
            var stopwatch = Stopwatch.StartNew();
            
            while (timerPage.IsTimerRunning() && stopwatch.Elapsed.TotalSeconds < expectedRestTime + 10)
            {
                Thread.Sleep(1000);
                var currentTime = timerPage.GetRemainingTime();
                TestContext.WriteLine($"Timer: {currentTime}");
                
                if (ParseTimeToSeconds(currentTime) <= 5)
                {
                    TakeScreenshot("03-timer-near-zero");
                }
            }
            
            // Verify timer completion
            Thread.Sleep(1000);
            Assert.That(timerPage.IsTimerRunning(), Is.False,
                "Timer should stop when reaching zero");
            
            // Verify notification/alert
            var notification = timerPage.GetTimerNotification();
            Assert.That(notification, Is.Not.Empty,
                "Should show notification when timer completes");
            
            TestContext.WriteLine($"Timer notification: {notification}");
            TakeScreenshot("04-timer-complete-notification");
            
            // Verify can start next set
            Assert.That(workoutPage.CanEnterNextSet(), Is.True,
                "Should be able to enter next set after timer completes");
        }
        
        [Test]
        [Order(2)]
        [Description("Tests skipping rest timer")]
        public void TEST_Rest_Timer_Skip()
        {
            TestContext.WriteLine("=== Testing Rest Timer Skip ===");
            
            workoutPage.SelectExerciseByName("Squat");
            Thread.Sleep(2000);
            
            // Complete a set
            workoutPage.EnterSet("8", "225");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            // Verify timer is running
            Assert.That(timerPage.IsTimerRunning(), Is.True);
            
            var timeBeforeSkip = timerPage.GetRemainingTime();
            TestContext.WriteLine($"Timer before skip: {timeBeforeSkip}");
            
            TakeScreenshot("05-timer-before-skip");
            
            // Skip rest timer
            timerPage.SkipTimer();
            Thread.Sleep(500);
            
            // Verify timer stopped
            Assert.That(timerPage.IsTimerRunning(), Is.False,
                "Timer should stop after skipping");
            
            // Verify can immediately start next set
            Assert.That(workoutPage.CanEnterNextSet(), Is.True,
                "Should be able to start next set immediately after skipping timer");
            
            TakeScreenshot("06-timer-skipped");
            
            // Verify skip is logged (optional)
            var skipMessage = timerPage.GetSkipConfirmation();
            if (!string.IsNullOrEmpty(skipMessage))
            {
                TestContext.WriteLine($"Skip confirmation: {skipMessage}");
            }
        }
        
        [Test]
        [Order(3)]
        [Description("Tests timer behavior when app is backgrounded")]
        public void TEST_Rest_Timer_Background()
        {
            TestContext.WriteLine("=== Testing Rest Timer in Background ===");
            
            workoutPage.SelectExerciseByName("Deadlift");
            Thread.Sleep(2000);
            
            // Complete a set
            workoutPage.EnterSet("5", "315");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            // Record timer value before backgrounding
            var timeBeforeBackground = timerPage.GetRemainingTime();
            var secondsBefore = ParseTimeToSeconds(timeBeforeBackground);
            TestContext.WriteLine($"Timer before background: {timeBeforeBackground}");
            
            TakeScreenshot("07-timer-before-background");
            
            // Simulate backgrounding app
            TestContext.WriteLine("Backgrounding app...");
            Driver?.BackgroundApp(TimeSpan.FromSeconds(5)); // Background for 5 seconds
            
            // App should be back in foreground
            Thread.Sleep(1000);
            
            // Check timer value after returning
            var timeAfterBackground = timerPage.GetRemainingTime();
            var secondsAfter = ParseTimeToSeconds(timeAfterBackground);
            TestContext.WriteLine($"Timer after background: {timeAfterBackground}");
            
            TakeScreenshot("08-timer-after-background");
            
            // Verify timer continued counting in background
            var expectedDifference = 5; // We backgrounded for 5 seconds
            var actualDifference = secondsBefore - secondsAfter;
            
            Assert.That(actualDifference, Is.InRange(expectedDifference - 2, expectedDifference + 2),
                "Timer should continue counting while app is backgrounded");
            
            // Test longer background period
            TestContext.WriteLine("Testing longer background period...");
            
            Driver?.BackgroundApp(TimeSpan.FromSeconds(10));
            Thread.Sleep(1000);
            
            // Timer might have completed while backgrounded
            if (timerPage.IsTimerRunning())
            {
                var remainingTime = timerPage.GetRemainingTime();
                TestContext.WriteLine($"Timer still running: {remainingTime}");
            }
            else
            {
                TestContext.WriteLine("Timer completed while backgrounded");
                
                // Should show notification that timer completed
                var notification = timerPage.GetTimerNotification();
                Assert.That(notification, Is.Not.Empty,
                    "Should show notification for timer that completed in background");
            }
            
            TakeScreenshot("09-timer-long-background");
        }
        
        [Test]
        [Order(4)]
        [Description("Tests custom rest time settings")]
        public void TEST_Custom_Rest_Times()
        {
            TestContext.WriteLine("=== Testing Custom Rest Times ===");
            
            // Navigate to settings
            workoutPage.NavigateToSettings();
            Thread.Sleep(2000);
            
            // Access timer settings
            settingsPage.OpenTimerSettings();
            Thread.Sleep(1000);
            
            TakeScreenshot("10-timer-settings");
            
            // Set custom rest time for specific exercise
            settingsPage.SelectExerciseForCustomRest("Bench Press");
            settingsPage.SetCustomRestTime("45"); // 45 seconds
            settingsPage.SaveTimerSettings();
            Thread.Sleep(1000);
            
            // Go back to workout
            settingsPage.NavigateBack();
            workoutPage.SelectExerciseByName("Bench Press");
            Thread.Sleep(2000);
            
            // Complete a set
            workoutPage.EnterSet("8", "135");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            // Verify custom timer is used
            var customTime = timerPage.GetRemainingTime();
            TestContext.WriteLine($"Custom timer value: {customTime}");
            
            Assert.That(ParseTimeToSeconds(customTime), Is.InRange(40, 50),
                "Should use custom rest time of 45 seconds");
            
            TakeScreenshot("11-custom-timer-active");
            
            // Test different custom times for different exercises
            timerPage.SkipTimer();
            workoutPage.FinishExercise();
            
            // Set different time for isolation exercise
            workoutPage.NavigateToSettings();
            settingsPage.OpenTimerSettings();
            settingsPage.SelectExerciseForCustomRest("Bicep Curl");
            settingsPage.SetCustomRestTime("30"); // 30 seconds for isolation
            settingsPage.SaveTimerSettings();
            
            settingsPage.NavigateBack();
            workoutPage.SelectExerciseByName("Bicep Curl");
            Thread.Sleep(2000);
            
            workoutPage.EnterSet("12", "30");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            var isolationTime = timerPage.GetRemainingTime();
            TestContext.WriteLine($"Isolation exercise timer: {isolationTime}");
            
            Assert.That(ParseTimeToSeconds(isolationTime), Is.InRange(25, 35),
                "Should use custom rest time of 30 seconds for isolation exercise");
            
            TakeScreenshot("12-isolation-custom-timer");
        }
        
        [Test]
        [Order(5)]
        [Description("Tests auto-start timer setting")]
        public void TEST_Auto_Start_Timer_Setting()
        {
            TestContext.WriteLine("=== Testing Auto-Start Timer Setting ===");
            
            // First, disable auto-start
            workoutPage.NavigateToSettings();
            Thread.Sleep(2000);
            
            settingsPage.OpenTimerSettings();
            settingsPage.SetAutoStartTimer(false);
            settingsPage.SaveTimerSettings();
            Thread.Sleep(1000);
            
            TakeScreenshot("13-auto-start-disabled");
            
            // Go back to workout
            settingsPage.NavigateBack();
            workoutPage.SelectExerciseByName("Overhead Press");
            Thread.Sleep(2000);
            
            // Complete a set
            workoutPage.EnterSet("10", "95");
            workoutPage.SaveSet();
            Thread.Sleep(2000);
            
            // Verify timer doesn't start automatically
            Assert.That(timerPage.IsTimerRunning(), Is.False,
                "Timer should NOT start automatically when auto-start is disabled");
            
            // Verify manual start button is available
            Assert.That(timerPage.IsStartTimerButtonVisible(), Is.True,
                "Manual start timer button should be visible");
            
            TakeScreenshot("14-manual-timer-start");
            
            // Manually start timer
            timerPage.StartTimer();
            Thread.Sleep(1000);
            
            Assert.That(timerPage.IsTimerRunning(), Is.True,
                "Timer should start when manually triggered");
            
            // Re-enable auto-start
            timerPage.SkipTimer();
            workoutPage.NavigateToSettings();
            settingsPage.OpenTimerSettings();
            settingsPage.SetAutoStartTimer(true);
            settingsPage.SaveTimerSettings();
            
            // Verify auto-start works again
            settingsPage.NavigateBack();
            workoutPage.EnterSet("10", "95");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            Assert.That(timerPage.IsTimerRunning(), Is.True,
                "Timer should start automatically when auto-start is re-enabled");
            
            TakeScreenshot("15-auto-start-re-enabled");
        }
        
        [Test]
        [Order(6)]
        [Description("Tests timer with different exercise types")]
        public void TEST_Timer_Different_Exercise_Types()
        {
            TestContext.WriteLine("=== Testing Timer for Different Exercise Types ===");
            
            var exerciseTimerTests = new[]
            {
                ("Squat", "Compound", 180, 240),           // 3-4 minutes
                ("Bicep Curl", "Isolation", 60, 90),      // 1-1.5 minutes
                ("Plank", "Isometric", 30, 60),           // 30-60 seconds
                ("Jump Squats", "Cardio", 30, 45),        // 30-45 seconds
                ("Bench Press", "Heavy Compound", 240, 300) // 4-5 minutes for heavy
            };
            
            foreach (var (exercise, type, minTime, maxTime) in exerciseTimerTests)
            {
                TestContext.WriteLine($"\nTesting {type} exercise: {exercise}");
                
                workoutPage.SelectExerciseByName(exercise);
                Thread.Sleep(2000);
                
                // Complete a set
                workoutPage.EnterSet("10", "100");
                workoutPage.SaveSet();
                Thread.Sleep(1000);
                
                var timerValue = timerPage.GetRemainingTime();
                var seconds = ParseTimeToSeconds(timerValue);
                
                TestContext.WriteLine($"{exercise} timer: {timerValue} ({seconds} seconds)");
                
                Assert.That(seconds, Is.InRange(minTime, maxTime),
                    $"{type} exercise should have rest time between {minTime}-{maxTime} seconds");
                
                TakeScreenshot($"16-timer-{type.ToLower().Replace(" ", "-")}");
                
                // Skip to next exercise
                timerPage.SkipTimer();
                workoutPage.FinishExercise();
            }
        }
        
        [Test]
        [Order(7)]
        [Description("Tests timer notifications and alerts")]
        public void TEST_Timer_Notifications()
        {
            TestContext.WriteLine("=== Testing Timer Notifications ===");
            
            // Configure notification settings
            workoutPage.NavigateToSettings();
            settingsPage.OpenTimerSettings();
            
            // Enable different notification types
            settingsPage.EnableTimerSound(true);
            settingsPage.EnableTimerVibration(true);
            settingsPage.EnableTimerNotification(true);
            settingsPage.SetNotificationTime("10"); // Alert 10 seconds before end
            
            settingsPage.SaveTimerSettings();
            settingsPage.NavigateBack();
            
            TakeScreenshot("17-notification-settings");
            
            // Start exercise with short timer
            workoutPage.SelectExerciseByName("Plank");
            Thread.Sleep(2000);
            
            workoutPage.EnterSet("30", "0"); // 30 second plank
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            // Wait for pre-notification (timer should be around 30 seconds)
            var initialTime = ParseTimeToSeconds(timerPage.GetRemainingTime());
            var waitTime = initialTime - 10; // Wait until 10 seconds remain
            
            if (waitTime > 0)
            {
                TestContext.WriteLine($"Waiting {waitTime} seconds for pre-notification...");
                Thread.Sleep((int)(waitTime * 1000));
            }
            
            // Check for 10-second warning
            var warningNotification = timerPage.GetWarningNotification();
            if (!string.IsNullOrEmpty(warningNotification))
            {
                TestContext.WriteLine($"Warning notification: {warningNotification}");
                TakeScreenshot("18-timer-warning-notification");
            }
            
            // Wait for timer completion
            Thread.Sleep(11000);
            
            // Verify completion notification
            var completionNotification = timerPage.GetTimerNotification();
            Assert.That(completionNotification, Is.Not.Empty,
                "Should show notification when timer completes");
            
            TestContext.WriteLine($"Completion notification: {completionNotification}");
            TakeScreenshot("19-timer-complete-alert");
        }
        
        private void LoginAndStartWorkout()
        {
            Thread.Sleep(3000);
            
            workoutPage.WaitForStartWorkout();
            workoutPage.StartWorkout();
            Thread.Sleep(2000);
            
            workoutPage.WaitForExerciseList();
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private int GetExpectedRestTime(string exerciseName)
        #pragma warning restore CA1822 // Mark members as static
        {
            // Default rest times by exercise type
            return exerciseName.ToLower() switch
            {
                var name when name.Contains("squat") || name.Contains("deadlift") || name.Contains("bench") => 180, // 3 minutes for compounds
                var name when name.Contains("curl") || name.Contains("extension") => 90, // 1.5 minutes for isolation
                var name when name.Contains("plank") || name.Contains("abs") => 60, // 1 minute for core
                _ => 120 // Default 2 minutes
            };
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private int ParseTimeToSeconds(string timeString)
        #pragma warning restore CA1822 // Mark members as static
        {
            // Parse time formats like "2:30", "1:00", "0:45"
            if (string.IsNullOrEmpty(timeString)) return 0;
            
            var parts = timeString.Split(':');
            if (parts.Length == 2)
            {
                if (int.TryParse(parts[0], out int minutes) && int.TryParse(parts[1], out int seconds))
                {
                    return minutes * 60 + seconds;
                }
            }
            
            // Try parsing as just seconds
            if (int.TryParse(timeString.Replace("s", ""), out int totalSeconds))
            {
                return totalSeconds;
            }
            
            return 0;
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private void TakeScreenshot(string name)
        {
            TestTimings.TakeScreenshot(Driver, name);
        }
        #pragma warning restore CA1822 // Mark members as static
    }
}