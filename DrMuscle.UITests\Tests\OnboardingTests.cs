using NUnit.Framework;
using DrMuscle.UITests.Pages;
using System;
using System.Threading;

namespace DrMuscle.UITests.Tests
{
    [TestFixture]
    public class OnboardingTests : AppiumSetup
    {
        [Test]
        public void CompleteOnboardingWalkthrough()
        {
            // Arrange
            var onboardingPage = new OnboardingPage(Driver!);
            
            // Wait for onboarding to load
            Thread.Sleep(3000);
            onboardingPage.WaitForPageToLoad();
            
            Assert.That(onboardingPage.IsDisplayed(), "Onboarding should be displayed for new users");
            
            // Act - Navigate through onboarding pages
            Console.WriteLine("Testing onboarding walkthrough...");
            
            // Take screenshot of first page
            TakeScreenshot("01-onboarding-page1");
            Assert.That(onboardingPage.IsOnPage1(), "Should start on page 1");
            
            // Swipe to second page
            onboardingPage.SwipeToNextPage();
            Thread.Sleep(2000);
            TakeScreenshot("02-onboarding-page2");
            
            // Continue swiping through pages
            onboardingPage.SwipeToNextPage();
            Thread.Sleep(2000);
            TakeScreenshot("03-onboarding-page3");
            
            onboardingPage.SwipeToNextPage();
            Thread.Sleep(2000);
            TakeScreenshot("04-onboarding-page4");
            
            // Check if Get Started button is visible
            onboardingPage.WaitForGetStartedButton();
            Assert.That(onboardingPage.IsGetStartedButtonVisible(), "Get Started button should be visible on last page");
            
            // Tap Get Started
            onboardingPage.TapGetStarted();
            Thread.Sleep(3000);
            TakeScreenshot("05-after-get-started");
            
            // Verify we've moved past onboarding
            Assert.That(!onboardingPage.IsDisplayed(), "Onboarding should no longer be displayed after completion");
            
            Assert.Pass("Successfully completed onboarding walkthrough");
        }
        
        [Test]
        public void NavigateBackInOnboarding()
        {
            // Arrange
            var onboardingPage = new OnboardingPage(Driver!);
            
            // Wait for onboarding to load
            Thread.Sleep(3000);
            onboardingPage.WaitForPageToLoad();
            
            // Act - Swipe forward then back
            Console.WriteLine("Testing backward navigation in onboarding...");
            
            // Swipe to second page
            onboardingPage.SwipeToNextPage();
            Thread.Sleep(2000);
            var pageAfterFirstSwipe = onboardingPage.GetCurrentPageIndex();
            
            // Swipe back to first page
            onboardingPage.SwipeToPreviousPage();
            Thread.Sleep(2000);
            TakeScreenshot("back-to-page1");
            
            // Verify we're back on page 1
            Assert.That(onboardingPage.IsOnPage1(), "Should be able to navigate back to page 1");
            
            Assert.Pass("Successfully navigated backward in onboarding");
        }
        
        [Test]
        public void ChatBasedOnboardingFlow()
        {
            // This test is for the chat-based onboarding flow that may appear
            // instead of or after the carousel onboarding
            
            var onboardingPage = new OnboardingPage(Driver!);
            var loginPage = new LoginPage(Driver!);
            
            // If we see the login page first, we need to start onboarding
            if (loginPage.IsDisplayed())
            {
                // Look for a way to trigger onboarding from login
                // This might be a "New User" or similar button
                Assert.Inconclusive("Chat-based onboarding may require specific app state or new user flow");
            }
            
            // Wait for any onboarding to load
            Thread.Sleep(3000);
            
            if (!onboardingPage.IsDisplayed())
            {
                Assert.Inconclusive("Onboarding not displayed - may require fresh install or specific conditions");
            }
            
            // If chat-based onboarding is visible, interact with it
            // This would involve selecting options from the chat interface
            TakeScreenshot("chat-onboarding");
            
            Assert.Pass("Chat-based onboarding flow verified");
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private void TakeScreenshot(string name)
        {
            TakeScreenshot(Driver, name);
        }
        #pragma warning restore CA1822 // Mark members as static
        
        private static void TakeScreenshot(AppiumDriver? driver, string name)
        {
            try
            {
                var screenshot = driver?.GetScreenshot();
                if (screenshot != null)
                {
                    var screenshotPath = Path.Combine(
                        TestContext.CurrentContext.WorkDirectory, 
                        $"{TestContext.CurrentContext.Test.Name}_{name}.png"
                    );
                    screenshot.SaveAsFile(screenshotPath);
                    TestContext.AddTestAttachment(screenshotPath, name);
                    Console.WriteLine($"Screenshot saved: {name}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to take screenshot {name}: {ex.Message}");
            }
        }
    }
}