using OpenQA.Selenium;
using OpenQA.Selenium.Appium;
using OpenQA.Selenium.Support.UI;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DrMuscle.UITests.Pages
{
    /// <summary>
    /// Page object for Superset functionality
    /// </summary>
    public class SupersetPage
    {
        private readonly AppiumDriver? _driver;
        private readonly WebDriverWait _wait;
        
        public SupersetPage(AppiumDriver driver)
        {
            _driver = driver ?? throw new ArgumentNullException(nameof(driver));
            _wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
        }
        
        // Superset controls
        private AppiumElement? SupersetButton => FindElement(MobileBy.AccessibilityId("SupersetButton"));
        private AppiumElement? SupersetOptionsModal => FindElement(MobileBy.AccessibilityId("SupersetOptions"));
        private AppiumElement? CreateSupersetButton => FindElement(MobileBy.AccessibilityId("CreateSuperset"));
        private AppiumElement? CancelSupersetButton => FindElement(MobileBy.AccessibilityId("CancelSuperset"));
        private AppiumElement? AddExerciseButton => FindElement(MobileBy.AccessibilityId("AddToSuperset"));
        
        // Tutorial elements
        private AppiumElement? SupersetTutorial => FindElement(MobileBy.AccessibilityId("SupersetTutorial"));
        private AppiumElement? TutorialText => FindElement(MobileBy.AccessibilityId("SupersetTutorialText"));
        private AppiumElement? DismissTutorialButton => FindElement(MobileBy.AccessibilityId("DismissTutorial"));
        
        // Exercise selection
        private AppiumElement? ExerciseSearchInput => FindElement(MobileBy.AccessibilityId("SupersetExerciseSearch"));
        private AppiumElement? ExerciseList => FindElement(MobileBy.AccessibilityId("SupersetExerciseList"));
        private AppiumElement? ConfirmExerciseButton => FindElement(MobileBy.AccessibilityId("ConfirmSupersetExercise"));
        
        // Superset indicators
        private AppiumElement? SupersetIndicator => FindElement(MobileBy.AccessibilityId("SupersetIndicator"));
        private AppiumElement? SupersetInfoLabel => FindElement(MobileBy.AccessibilityId("SupersetInfo"));
        private AppiumElement? ExerciseALabel => FindElement(MobileBy.AccessibilityId("SupersetExerciseA"));
        private AppiumElement? ExerciseBLabel => FindElement(MobileBy.AccessibilityId("SupersetExerciseB"));
        private AppiumElement? CurrentExerciseIndicator => FindElement(MobileBy.AccessibilityId("CurrentSupersetExercise"));
        
        // Actions
        public void OpenSupersetOptions()
        {
            SupersetButton?.Click();
            Thread.Sleep(500);
        }
        
        public void CreateSuperset()
        {
            CreateSupersetButton?.Click();
        }
        
        public void CancelSuperset()
        {
            CancelSupersetButton?.Click();
        }
        
        public void DismissTutorial()
        {
            DismissTutorialButton?.Click();
        }
        
        public void SearchExercise(string exerciseName)
        {
            ExerciseSearchInput?.Clear();
            ExerciseSearchInput?.SendKeys(exerciseName);
            Thread.Sleep(500); // Allow search to complete
        }
        
        public void SelectExerciseForSuperset(string exerciseName)
        {
            // Try multiple selectors
            var exerciseCell = FindElement(MobileBy.XPath($"//XCUIElementTypeCell[contains(@name, '{exerciseName}')]")) ??
                              FindElement(MobileBy.AccessibilityId(exerciseName));
            
            if (exerciseCell != null)
            {
                exerciseCell.Click();
                Thread.Sleep(500);
                
                // Confirm selection if needed
                ConfirmExerciseButton?.Click();
            }
            else
            {
                // Try scrolling to find exercise
                ScrollToExercise(exerciseName);
            }
        }
        
        public void AddAnotherExercise()
        {
            AddExerciseButton?.Click();
            Thread.Sleep(500);
        }
        
        // Verifications
        public bool IsSupersetActive()
        {
            return SupersetIndicator != null && SupersetIndicator.Displayed;
        }
        
        public bool IsSupersetTutorialVisible()
        {
            return SupersetTutorial != null && SupersetTutorial.Displayed;
        }
        
        public bool IsCancelSupersetVisible()
        {
            return CancelSupersetButton != null && CancelSupersetButton.Displayed;
        }
        
        public bool CanAddMoreExercises()
        {
            return AddExerciseButton != null && AddExerciseButton.Displayed && AddExerciseButton.Enabled;
        }
        
        // Data retrieval
        public string GetTutorialText()
        {
            return TutorialText?.Text ?? "";
        }
        
        public string GetSupersetInfo()
        {
            if (SupersetInfoLabel != null)
            {
                return SupersetInfoLabel.Text;
            }
            
            // Build info from individual exercise labels
            var exerciseA = ExerciseALabel?.Text ?? "";
            var exerciseB = ExerciseBLabel?.Text ?? "";
            
            if (!string.IsNullOrEmpty(exerciseA) && !string.IsNullOrEmpty(exerciseB))
            {
                return $"{exerciseA} + {exerciseB}";
            }
            
            return "";
        }
        
        public List<string> GetAvailableExercises()
        {
            var exercises = new List<string>();
            
            if (ExerciseList != null)
            {
                var exerciseCells = ExerciseList.FindElements(MobileBy.ClassName("XCUIElementTypeCell"));
                exercises = exerciseCells.Select(cell => cell.Text).ToList();
            }
            
            return exercises;
        }
        
        public string GetCurrentExerciseInSuperset()
        {
            return CurrentExerciseIndicator?.Text ?? "";
        }
        
        public List<string> GetSupersetExercises()
        {
            var exercises = new List<string>();
            
            // Try to get from info label
            var info = GetSupersetInfo();
            if (info.Contains('+'))
            {
                exercises = info.Split('+').Select(e => e.Trim()).ToList();
            }
            
            // Alternative: find exercise elements
            if (exercises.Count == 0)
            {
                var exerciseElements = _driver?.FindElements(MobileBy.XPath("//XCUIElementTypeStaticText[contains(@name, 'Exercise')]"));
                if (exerciseElements != null)
                {
                    exercises = exerciseElements.Select(e => e.Text).ToList();
                }
            }
            
            return exercises;
        }
        
        // Helper methods
        private AppiumElement? FindElement(By by)
        {
            try
            {
                return _driver?.FindElement(by) as AppiumElement;
            }
            catch (NoSuchElementException)
            {
                return null;
            }
        }
        
        private void ScrollToExercise(string exerciseName)
        {
            // Implement scrolling logic for iOS/Android
            if (_driver?.PlatformName == "iOS")
            {
                // iOS scroll
                var dict = new Dictionary<string, object>
                {
                    { "direction", "down" },
                    { "predicateString", $"name CONTAINS '{exerciseName}'" }
                };
                _driver.ExecuteScript("mobile: scroll", dict);
            }
            else
            {
                // Android scroll
                var scrollable = _driver?.FindElement(MobileBy.AndroidUIAutomator(
                    $"new UiScrollable(new UiSelector().scrollable(true)).scrollIntoView(new UiSelector().textContains(\"{exerciseName}\"))"));
                
                scrollable?.Click();
            }
        }
    }
}