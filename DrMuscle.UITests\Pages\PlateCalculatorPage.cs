using OpenQA.Selenium;
using OpenQA.Selenium.Appium;
using OpenQA.Selenium.Support.UI;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DrMuscle.UITests.Pages
{
    /// <summary>
    /// Page object for the Plate Calculator feature
    /// </summary>
    public class PlateCalculatorPage
    {
        private readonly AppiumDriver? _driver;
        private readonly WebDriverWait _wait;
        
        public PlateCalculatorPage(AppiumDriver driver)
        {
            _driver = driver ?? throw new ArgumentNullException(nameof(driver));
            _wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
        }
        
        // Calculator UI elements
        private AppiumElement? PlateCalculatorButton => FindElement(MobileBy.AccessibilityId("PlateCalculatorButton"));
        private AppiumElement? PlateCalculatorModal => FindElement(MobileBy.AccessibilityId("PlateCalculatorModal"));
        private AppiumElement? TargetWeightInput => FindElement(MobileBy.AccessibilityId("PlateCalcTargetWeight"));
        private AppiumElement? PlateBreakdownView => FindElement(MobileBy.AccessibilityId("PlateBreakdownView"));
        private AppiumElement? BarbellTypeSelector => FindElement(MobileBy.AccessibilityId("BarbellTypeSelector"));
        private AppiumElement? DumbbellModeSwitch => FindElement(MobileBy.AccessibilityId("DumbbellModeSwitch"));
        private AppiumElement? UnitToggle => FindElement(MobileBy.AccessibilityId("UnitToggle"));
        private AppiumElement? PlateSettingsButton => FindElement(MobileBy.AccessibilityId("PlateSettingsButton"));
        private AppiumElement? CloseCalculatorButton => FindElement(MobileBy.AccessibilityId("CloseCalculatorButton"));
        
        // Plate settings elements
        private AppiumElement? PlateSettingsModal => FindElement(MobileBy.AccessibilityId("PlateSettingsModal"));
        private AppiumElement? AddCustomPlateButton => FindElement(MobileBy.AccessibilityId("AddCustomPlateButton"));
        private AppiumElement? PlateWeightInput => FindElement(MobileBy.AccessibilityId("CustomPlateWeight"));
        private AppiumElement? PlateUnitSelector => FindElement(MobileBy.AccessibilityId("CustomPlateUnit"));
        private AppiumElement? SavePlateSettingsButton => FindElement(MobileBy.AccessibilityId("SavePlateSettings"));
        private AppiumElement? PlatePresetSelector => FindElement(MobileBy.AccessibilityId("PlatePresetSelector"));
        
        // Display elements
        private AppiumElement? BarWeightLabel => FindElement(MobileBy.AccessibilityId("BarWeightLabel"));
        private AppiumElement? SingleDumbbellWeightLabel => FindElement(MobileBy.AccessibilityId("SingleDumbbellWeight"));
        private AppiumElement? ClosestWeightLabel => FindElement(MobileBy.AccessibilityId("ClosestPossibleWeight"));
        private AppiumElement? WarningMessageLabel => FindElement(MobileBy.AccessibilityId("PlateCalcWarning"));
        private AppiumElement? DumbbellTypeSelector => FindElement(MobileBy.AccessibilityId("DumbbellTypeSelector"));
        
        // Actions
        public void OpenPlateCalculator()
        {
            PlateCalculatorButton?.Click();
            _wait.Until(d => IsPlateCalculatorVisible());
        }
        
        public void CloseCalculator()
        {
            CloseCalculatorButton?.Click();
        }
        
        public void EnterTargetWeight(string weight)
        {
            TargetWeightInput?.Clear();
            TargetWeightInput?.SendKeys(weight);
        }
        
        public void SelectBarbellType(string type)
        {
            BarbellTypeSelector?.Click();
            Thread.Sleep(500);
            
            var barbellOption = FindElement(MobileBy.XPath($"//XCUIElementTypeButton[@name='{type}']"));
            barbellOption?.Click();
        }
        
        public void SelectDumbbellType(string type)
        {
            DumbbellTypeSelector?.Click();
            Thread.Sleep(500);
            
            var dumbbellOption = FindElement(MobileBy.XPath($"//XCUIElementTypeButton[@name='{type}']"));
            dumbbellOption?.Click();
        }
        
        public void SwitchUnits(string unit)
        {
            var currentUnit = GetCurrentUnit();
            if (currentUnit != unit)
            {
                UnitToggle?.Click();
            }
        }
        
        public void OpenPlateSettings()
        {
            PlateSettingsButton?.Click();
            _wait.Until(d => PlateSettingsModal != null && PlateSettingsModal.Displayed);
        }
        
        public void AddCustomPlate(string weight, string unit)
        {
            AddCustomPlateButton?.Click();
            Thread.Sleep(500);
            
            PlateWeightInput?.Clear();
            PlateWeightInput?.SendKeys(weight);
            
            // Select unit if different from current
            if (PlateUnitSelector != null)
            {
                PlateUnitSelector.Click();
                Thread.Sleep(300);
                var unitOption = FindElement(MobileBy.XPath($"//XCUIElementTypeButton[@name='{unit}']"));
                unitOption?.Click();
            }
            
            // Confirm addition
            var confirmButton = FindElement(MobileBy.AccessibilityId("ConfirmAddPlate"));
            confirmButton?.Click();
        }
        
        public void RemovePlate(string weight, string unit)
        {
            var plateElement = FindElement(MobileBy.XPath($"//XCUIElementTypeCell[contains(@name, '{weight}') and contains(@name, '{unit}')]"));
            if (plateElement != null)
            {
                // Swipe to delete or tap delete button
                var deleteButton = plateElement.FindElement(MobileBy.AccessibilityId("DeletePlateButton"));
                deleteButton?.Click();
            }
        }
        
        public void SelectPlateSetPreset(string presetName)
        {
            PlatePresetSelector?.Click();
            Thread.Sleep(500);
            
            var presetOption = FindElement(MobileBy.XPath($"//XCUIElementTypeButton[@name='{presetName}']"));
            presetOption?.Click();
        }
        
        public void SavePlateSettings()
        {
            SavePlateSettingsButton?.Click();
            Thread.Sleep(500);
        }
        
        // Verifications
        public bool IsPlateCalculatorVisible()
        {
            return PlateCalculatorModal != null && PlateCalculatorModal.Displayed;
        }
        
        public bool IsDumbbellModeActive()
        {
            return DumbbellModeSwitch != null && DumbbellModeSwitch.GetAttribute("value") == "1";
        }
        
        public bool HasBarbellType(string type)
        {
            BarbellTypeSelector?.Click();
            Thread.Sleep(300);
            
            var barbellOption = FindElement(MobileBy.XPath($"//XCUIElementTypeButton[@name='{type}']"));
            
            // Close selector
            BarbellTypeSelector?.Click();
            
            return barbellOption != null;
        }
        
        // Data retrieval
        public string GetPlateBreakdown()
        {
            if (PlateBreakdownView == null) return "";
            
            // Get all plate elements within the breakdown view
            var plateElements = PlateBreakdownView.FindElements(MobileBy.ClassName("XCUIElementTypeStaticText"));
            var breakdown = string.Join(", ", plateElements.Select(e => e.Text));
            
            return breakdown;
        }
        
        public string GetSelectedBarbellType()
        {
            return BarbellTypeSelector?.Text ?? "";
        }
        
        public string GetBarWeight()
        {
            return BarWeightLabel?.Text?.Replace("Bar: ", "").Replace(" lbs", "").Replace(" kg", "") ?? "";
        }
        
        public string GetSingleDumbbellWeight()
        {
            return SingleDumbbellWeightLabel?.Text?.Replace(" lbs", "").Replace(" kg", "") ?? "";
        }
        
        public string GetTargetWeight()
        {
            return TargetWeightInput?.Text ?? "";
        }
        
        public string GetClosestPossibleWeight()
        {
            return ClosestWeightLabel?.Text ?? "";
        }
        
        public string GetWarningMessage()
        {
            return WarningMessageLabel?.Text ?? "";
        }
        
        public string GetCurrentUnit()
        {
            var unitText = UnitToggle?.Text ?? "";
            return unitText.Contains("kg") ? "kg" : "lbs";
        }
        
        public List<string> GetFixedDumbbellOptions()
        {
            var options = new List<string>();
            
            // This would need to be implemented based on actual UI structure
            var dumbbellList = FindElement(MobileBy.AccessibilityId("FixedDumbbellList"));
            if (dumbbellList != null)
            {
                var dumbbellOptions = dumbbellList.FindElements(MobileBy.ClassName("XCUIElementTypeCell"));
                options = dumbbellOptions.Select(e => e.Text).ToList();
            }
            
            return options;
        }
        
        public List<string> GetAvailablePlates()
        {
            var plates = new List<string>();
            
            var plateList = FindElement(MobileBy.AccessibilityId("AvailablePlatesList"));
            if (plateList != null)
            {
                var plateElements = plateList.FindElements(MobileBy.ClassName("XCUIElementTypeCell"));
                plates = plateElements.Select(e => e.Text).ToList();
            }
            
            return plates;
        }
        
        // Helper methods
        private AppiumElement? FindElement(By by)
        {
            try
            {
                return _driver?.FindElement(by) as AppiumElement;
            }
            catch (NoSuchElementException)
            {
                return null;
            }
        }
    }
}