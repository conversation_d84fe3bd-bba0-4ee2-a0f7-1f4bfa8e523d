using NUnit.Framework;
using DrMuscle.UITests.Helpers;
using DrMuscle.UITests.Pages;
using System;
using System.Threading;
using System.Collections.Generic;
using OpenQA.Selenium.Appium;

namespace DrMuscle.UITests.Tests
{
    /// <summary>
    /// Comprehensive tests for Advanced Set Types
    /// Tests drop sets, rest-pause, pyramid sets, AMRAP, timed sets, and mechanical drops
    /// </summary>
    [TestFixture]
    [Category(TestCategories.AdvancedFeatures)]
    public class AdvancedSetTypesTests : AppiumSetup
    {
        private WorkoutPage workoutPage = null!;
        private AdvancedSetsPage advancedSetsPage = null!;
        private TimerPage timerPage = null!;
        
        [SetUp]
        public void TestSetup()
        {
            workoutPage = new WorkoutPage(Driver!);
            advancedSetsPage = new AdvancedSetsPage(Driver!);
            timerPage = new TimerPage(Driver!);
            
            // Login and start workout
            LoginAndStartWorkout();
        }
        
        [Test]
        [Order(1)]
        [Description("Tests drop set functionality")]
        public void TEST_Drop_Sets()
        {
            TestContext.WriteLine("=== Testing Drop Sets ===");
            
            // Navigate to exercise
            workoutPage.SelectExerciseByName("Bench Press");
            Thread.Sleep(2000);
            
            // Enable drop sets
            advancedSetsPage.OpenAdvancedOptions();
            Thread.Sleep(1000);
            
            advancedSetsPage.EnableDropSets(true);
            advancedSetsPage.SetDropCount(3); // 3 drops
            advancedSetsPage.SetDropPercentage(20); // 20% reduction each drop
            advancedSetsPage.SaveAdvancedSettings();
            Thread.Sleep(1000);
            
            TakeScreenshot("01-drop-sets-enabled");
            
            // Complete first set at full weight
            var fullWeight = "185";
            workoutPage.EnterSet("8", fullWeight);
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            // Verify prompts for immediate drop set
            Assert.That(advancedSetsPage.IsDropSetPromptVisible(), Is.True,
                "Should prompt for drop set immediately after main set");
            
            var dropPrompt = advancedSetsPage.GetDropSetPrompt();
            TestContext.WriteLine($"Drop set prompt: {dropPrompt}");
            
            TakeScreenshot("02-drop-set-prompt");
            
            // First drop (20% reduction = 148 lbs)
            var drop1Weight = advancedSetsPage.GetSuggestedDropWeight();
            TestContext.WriteLine($"Drop 1 suggested weight: {drop1Weight}");
            
            Assert.That(ParseWeight(drop1Weight), Is.InRange(145, 150),
                "First drop should be ~20% lighter");
            
            // Verify no rest between drops
            Assert.That(timerPage.IsTimerRunning(), Is.False,
                "Should NOT have rest timer between drop sets");
            
            workoutPage.EnterSet("6", drop1Weight);
            workoutPage.SaveSet();
            Thread.Sleep(500);
            
            // Second drop
            var drop2Weight = advancedSetsPage.GetSuggestedDropWeight();
            TestContext.WriteLine($"Drop 2 suggested weight: {drop2Weight}");
            
            workoutPage.EnterSet("5", drop2Weight);
            workoutPage.SaveSet();
            Thread.Sleep(500);
            
            // Third drop
            var drop3Weight = advancedSetsPage.GetSuggestedDropWeight();
            TestContext.WriteLine($"Drop 3 suggested weight: {drop3Weight}");
            
            workoutPage.EnterSet("4", drop3Weight);
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            // Verify full rest after all drops complete
            Assert.That(timerPage.IsTimerRunning(), Is.True,
                "Should have full rest timer after completing all drop sets");
            
            TakeScreenshot("03-drop-sets-complete");
            
            // Verify all drops recorded as one extended set
            var setInfo = advancedSetsPage.GetSetSummary();
            TestContext.WriteLine($"Set summary: {setInfo}");
            
            Assert.That(setInfo, Does.Contain("Drop Set") | Does.Contain("4 total"),
                "Should show drop set with total mini-sets");
        }
        
        [Test]
        [Order(2)]
        [Description("Tests rest-pause set functionality")]
        public void TEST_Rest_Pause_Sets()
        {
            TestContext.WriteLine("=== Testing Rest-Pause Sets ===");
            
            workoutPage.SelectExerciseByName("Leg Extension");
            Thread.Sleep(2000);
            
            // Enable rest-pause
            advancedSetsPage.OpenAdvancedOptions();
            advancedSetsPage.EnableRestPause(true);
            advancedSetsPage.SetRestPauseDuration(15); // 15 second mini-rests
            advancedSetsPage.SetRestPauseRounds(3); // 3 mini-sets
            advancedSetsPage.SaveAdvancedSettings();
            Thread.Sleep(1000);
            
            TakeScreenshot("04-rest-pause-enabled");
            
            // Complete initial set to failure
            workoutPage.EnterSet("12", "150");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            // Verify mini-rest timer (15 seconds)
            Assert.That(timerPage.IsTimerRunning(), Is.True,
                "Should start mini-rest timer");
            
            var miniRestTime = ParseTimeToSeconds(timerPage.GetRemainingTime());
            Assert.That(miniRestTime, Is.InRange(13, 17),
                "Mini-rest should be ~15 seconds");
            
            TestContext.WriteLine($"Mini-rest timer: {miniRestTime} seconds");
            TakeScreenshot("05-rest-pause-mini-rest");
            
            // Wait for timer or skip
            timerPage.SkipTimer();
            
            // First mini-set
            Assert.That(advancedSetsPage.GetRestPauseRound(), Is.EqualTo("2/3"),
                "Should show round 2 of 3");
            
            workoutPage.EnterSet("4", "150");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            // Another mini-rest
            timerPage.SkipTimer();
            
            // Second mini-set
            Assert.That(advancedSetsPage.GetRestPauseRound(), Is.EqualTo("3/3"),
                "Should show round 3 of 3");
            
            workoutPage.EnterSet("3", "150");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            // Verify counted as one set
            var setCount = workoutPage.GetCompletedSetCount();
            Assert.That(setCount, Is.EqualTo("1"),
                "Rest-pause should count as single set");
            
            // Verify full rest after rest-pause complete
            var fullRestTime = ParseTimeToSeconds(timerPage.GetRemainingTime());
            Assert.That(fullRestTime, Is.GreaterThan(60),
                "Should have full rest after rest-pause set");
            
            TakeScreenshot("06-rest-pause-complete");
        }
        
        [Test]
        [Order(3)]
        [Description("Tests pyramid set functionality")]
        public void TEST_Pyramid_Sets()
        {
            TestContext.WriteLine("=== Testing Pyramid Sets ===");
            
            workoutPage.SelectExerciseByName("Dumbbell Press");
            Thread.Sleep(2000);
            
            // Configure pyramid
            advancedSetsPage.OpenAdvancedOptions();
            advancedSetsPage.SelectSetScheme("Pyramid");
            advancedSetsPage.SetPyramidSteps(5); // 5 steps up, 5 down
            advancedSetsPage.SetPyramidIncrement(10); // 10 lbs per step
            advancedSetsPage.SaveAdvancedSettings();
            Thread.Sleep(1000);
            
            TakeScreenshot("07-pyramid-configured");
            
            var weights = new List<string>();
            var baseWeight = 50;
            
            // Ascending phase
            TestContext.WriteLine("Ascending pyramid...");
            for (int i = 0; i < 5; i++)
            {
                var currentWeight = (baseWeight + (i * 10)).ToString();
                weights.Add(currentWeight);
                
                var suggestedWeight = advancedSetsPage.GetPyramidWeight();
                TestContext.WriteLine($"Step {i + 1} suggested: {suggestedWeight}, using: {currentWeight}");
                
                workoutPage.EnterSet((12 - i).ToString(), currentWeight);
                workoutPage.SaveSet();
                Thread.Sleep(1000);
                
                if (i < 4) timerPage.SkipTimer();
            }
            
            // Peak
            TakeScreenshot("08-pyramid-peak");
            
            // Descending phase
            TestContext.WriteLine("Descending pyramid...");
            for (int i = 3; i >= 0; i--)
            {
                timerPage.SkipTimer();
                
                var currentWeight = weights[i];
                var suggestedWeight = advancedSetsPage.GetPyramidWeight();
                TestContext.WriteLine($"Descending step suggested: {suggestedWeight}");
                
                workoutPage.EnterSet((12 - i).ToString(), currentWeight);
                workoutPage.SaveSet();
                Thread.Sleep(1000);
            }
            
            TakeScreenshot("09-pyramid-complete");
            
            // Verify pyramid summary
            var pyramidSummary = advancedSetsPage.GetSetSummary();
            TestContext.WriteLine($"Pyramid summary: {pyramidSummary}");
            
            Assert.That(pyramidSummary, Does.Contain("Pyramid") | Does.Contain("9 sets"),
                "Should show complete pyramid with 9 total sets");
        }
        
        [Test]
        [Order(4)]
        [Description("Tests AMRAP (As Many Reps As Possible) sets")]
        public void TEST_AMRAP_Sets()
        {
            TestContext.WriteLine("=== Testing AMRAP Sets ===");
            
            workoutPage.SelectExerciseByName("Push-ups");
            Thread.Sleep(2000);
            
            // Enable AMRAP mode
            advancedSetsPage.OpenAdvancedOptions();
            advancedSetsPage.EnableAMRAP(true);
            advancedSetsPage.SaveAdvancedSettings();
            Thread.Sleep(1000);
            
            TakeScreenshot("10-amrap-enabled");
            
            // Verify no target reps shown
            var targetReps = workoutPage.GetTargetReps();
            Assert.That(targetReps, Is.EqualTo("AMRAP") | Is.EqualTo("Max") | Is.Empty,
                "Should not show specific target reps for AMRAP");
            
            // Complete AMRAP set
            TestContext.WriteLine("Performing AMRAP set...");
            
            // Enter actual reps achieved
            var achievedReps = "25";
            workoutPage.EnterReps(achievedReps);
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            TakeScreenshot("11-amrap-completed");
            
            // Verify affects future recommendations
            workoutPage.FinishExercise();
            workoutPage.StartNextWorkout(); // Simulate next session
            workoutPage.SelectExerciseByName("Push-ups");
            Thread.Sleep(2000);
            
            var newTarget = workoutPage.GetTargetReps();
            TestContext.WriteLine($"New target based on AMRAP: {newTarget}");
            
            if (!string.IsNullOrEmpty(newTarget) && newTarget != "AMRAP")
            {
                Assert.That(int.Parse(newTarget), Is.GreaterThanOrEqualTo(20),
                    "Future targets should be based on AMRAP performance");
            }
        }
        
        [Test]
        [Order(5)]
        [Description("Tests timed sets functionality")]
        public void TEST_Timed_Sets()
        {
            TestContext.WriteLine("=== Testing Timed Sets ===");
            
            workoutPage.SelectExerciseByName("Plank");
            Thread.Sleep(2000);
            
            // Verify shows timer instead of reps
            Assert.That(advancedSetsPage.IsTimedExercise(), Is.True,
                "Plank should be recognized as timed exercise");
            
            TakeScreenshot("12-timed-exercise");
            
            // Start timer-based set
            advancedSetsPage.StartTimedSet();
            Thread.Sleep(1000);
            
            // Verify timer is counting up
            var time1 = advancedSetsPage.GetElapsedTime();
            Thread.Sleep(3000);
            var time2 = advancedSetsPage.GetElapsedTime();
            
            TestContext.WriteLine($"Time 1: {time1}, Time 2: {time2}");
            
            Assert.That(ParseTimeToSeconds(time2), Is.GreaterThan(ParseTimeToSeconds(time1)),
                "Timer should be counting up during timed set");
            
            TakeScreenshot("13-timed-set-in-progress");
            
            // Test pause/resume
            advancedSetsPage.PauseTimedSet();
            Thread.Sleep(2000);
            
            var pausedTime = advancedSetsPage.GetElapsedTime();
            Thread.Sleep(2000);
            var stillPausedTime = advancedSetsPage.GetElapsedTime();
            
            Assert.That(pausedTime, Is.EqualTo(stillPausedTime),
                "Timer should not advance while paused");
            
            TakeScreenshot("14-timed-set-paused");
            
            // Resume and complete
            advancedSetsPage.ResumeTimedSet();
            Thread.Sleep(2000);
            
            advancedSetsPage.CompleteTimedSet();
            Thread.Sleep(1000);
            
            // Verify duration saved
            var savedDuration = workoutPage.GetLastSetDetails();
            TestContext.WriteLine($"Saved duration: {savedDuration}");
            
            Assert.That(savedDuration, Does.Contain("sec") | Does.Contain(":"),
                "Should save duration instead of reps");
            
            TakeScreenshot("15-timed-set-complete");
        }
        
        [Test]
        [Order(6)]
        [Description("Tests mechanical drop sets")]
        public void TEST_Mechanical_Drop_Sets()
        {
            TestContext.WriteLine("=== Testing Mechanical Drop Sets ===");
            
            // Set up exercise variations
            workoutPage.SelectExerciseByName("Push-ups");
            Thread.Sleep(2000);
            
            // Configure mechanical drops
            advancedSetsPage.OpenAdvancedOptions();
            advancedSetsPage.SelectSetScheme("Mechanical Drop");
            
            // Add variations (hardest to easiest)
            advancedSetsPage.AddMechanicalVariation("Decline Push-ups");
            advancedSetsPage.AddMechanicalVariation("Regular Push-ups");
            advancedSetsPage.AddMechanicalVariation("Incline Push-ups");
            advancedSetsPage.AddMechanicalVariation("Knee Push-ups");
            
            advancedSetsPage.SaveAdvancedSettings();
            Thread.Sleep(1000);
            
            TakeScreenshot("16-mechanical-drops-configured");
            
            // Complete each variation
            var variations = new[] {
                ("Decline Push-ups", "8"),
                ("Regular Push-ups", "10"),
                ("Incline Push-ups", "12"),
                ("Knee Push-ups", "15")
            };
            
            foreach (var (variation, reps) in variations)
            {
                var currentExercise = advancedSetsPage.GetCurrentVariation();
                Assert.That(currentExercise, Does.Contain(variation),
                    $"Should be on {variation} variation");
                
                TestContext.WriteLine($"Completing {variation}: {reps} reps");
                
                workoutPage.EnterReps(reps);
                workoutPage.SaveSet();
                Thread.Sleep(1000);
                
                // No rest between mechanical drops
                if (variation != "Knee Push-ups")
                {
                    Assert.That(timerPage.IsTimerRunning(), Is.False,
                        "Should not rest between mechanical drop variations");
                }
            }
            
            TakeScreenshot("17-mechanical-drops-complete");
            
            // Verify treated as one extended set
            var mechanicalSummary = advancedSetsPage.GetSetSummary();
            TestContext.WriteLine($"Mechanical drop summary: {mechanicalSummary}");
            
            Assert.That(mechanicalSummary, Does.Contain("Mechanical") | Does.Contain("4 variations"),
                "Should show as mechanical drop set with variations");
        }
        
        private void LoginAndStartWorkout()
        {
            Thread.Sleep(3000);
            
            workoutPage.WaitForStartWorkout();
            workoutPage.StartWorkout();
            Thread.Sleep(2000);
            
            workoutPage.WaitForExerciseList();
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private double ParseWeight(string weightText)
        #pragma warning restore CA1822 // Mark members as static
        {
            var cleanWeight = weightText.Replace("lbs", "").Replace("kg", "").Trim();
            return double.TryParse(cleanWeight, out double weight) ? weight : 0;
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private int ParseTimeToSeconds(string timeString)
        #pragma warning restore CA1822 // Mark members as static
        {
            if (string.IsNullOrEmpty(timeString)) return 0;
            
            var parts = timeString.Split(':');
            if (parts.Length == 2)
            {
                if (int.TryParse(parts[0], out int minutes) && int.TryParse(parts[1], out int seconds))
                {
                    return minutes * 60 + seconds;
                }
            }
            
            if (int.TryParse(timeString.Replace("s", ""), out int totalSeconds))
            {
                return totalSeconds;
            }
            
            return 0;
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private void TakeScreenshot(string name)
        #pragma warning restore CA1822 // Mark members as static
        {
            TestTimings.TakeScreenshot(Driver, name);
        }
    }
}