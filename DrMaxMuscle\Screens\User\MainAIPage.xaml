﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.Screens.User.MainAIPage"
              xmlns:t="clr-namespace:DrMaxMuscle.Layout"
            xmlns:app="clr-namespace:DrMaxMuscle.Constants"
                xmlns:microcharts="clr-namespace:Microcharts.Maui;assembly=Microcharts.Maui"
            xmlns:local="clr-namespace:DrMaxMuscle.Cells"
            xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
             xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
            BackgroundColor="#f4f4f4"
             Title="MainAIPage1">
    <ContentPage.Resources>
        <ResourceDictionary>
            <local:BotDataTemplateSelector
        x:Key="BotTemplateSelector">
            </local:BotDataTemplateSelector>
        </ResourceDictionary>
    </ContentPage.Resources>
    <ContentPage.Content>
        <Grid
            x:Name="mainGrid"
            BackgroundColor="#f4f4f4"
            AbsoluteLayout.LayoutFlags="All"
            AbsoluteLayout.LayoutBounds="0, 0, 1, 1"
            HorizontalOptions="FillAndExpand"
            VerticalOptions="FillAndExpand"
            RowSpacing="0"
            Margin="0"
            Padding="0">
            <Grid.RowDefinitions>
                <RowDefinition
                    x:Name="StatusBarHeight"
                    Height="0" />
                <RowDefinition
                    Height="Auto" />
                <RowDefinition
                    Height="*" />
                <RowDefinition
                    Height="0" />
                <RowDefinition
                    Height="Auto" />
            </Grid.RowDefinitions>
            <!--<Image
                Source="nav.png"
                Grid.Row="0"
                Grid.Column="0"
                VerticalOptions="Start"
                Aspect="AspectFill"
                Grid.RowSpan="2" />-->

            <ScrollView Grid.Row="2" x:Name="TempListScroll">
                <CollectionView
                x:Name="TempList"
                
                BackgroundColor="White"
                ItemTemplate="{StaticResource BotTemplateSelector}"
                VerticalOptions="Start"
                IsVisible="false"/> 
                <!--<ListView
                x:Name="TempList"
                
                BackgroundColor="White"
                ItemTemplate="{StaticResource BotTemplateSelector}"
                HasUnevenRows="True"
                VerticalOptions="Start"
                IsVisible="false"
                SeparatorColor="Transparent"/>-->
            </ScrollView>
            <CollectionView
                Grid.Row="2"
                BackgroundColor="#f4f4f4"
                ItemTemplate="{StaticResource BotTemplateSelector}"
                x:Name="lstChats"
                VerticalOptions="FillAndExpand"
                IsVisible="false">
                <CollectionView.Footer>
                    <StackLayout
                        x:Name="bottomBtns"
                        Padding="30,0"
                        Spacing="10">
                        <Frame
    HeightRequest="60"
    BackgroundColor="Transparent"
    Margin="0"
    Padding="2"
    BorderColor="#195377"    
    CornerRadius="0"
    HasShadow="False">
                            <Frame.GestureRecognizers>
                                <TapGestureRecognizer
                                    NumberOfTapsRequired="1"
                                    Tapped="ButtonShare_Clicked"/>
                            </Frame.GestureRecognizers>
                            <Grid
        BackgroundColor="White"
        HorizontalOptions="FillAndExpand"
        VerticalOptions="FillAndExpand">

                                <StackLayout
            Grid.Row="0"
            Grid.Column="0"
            HorizontalOptions="Center"
            VerticalOptions="FillAndExpand"
            Orientation="Horizontal"
            Spacing="8"
            Margin="-26,0,0,0"
            BackgroundColor="Transparent">

                                    <Image               
                x:Name="Img"
                Source="ic_share_exercise"
                VerticalOptions="FillAndExpand"
                HorizontalOptions="Center"
                Margin="0,20"/>

                                    <Label                
                x:Name="LblText"
                Text="Share"
                FontSize="15"
                HorizontalOptions="Center"
                VerticalOptions="FillAndExpand"
                HorizontalTextAlignment="Center"
                VerticalTextAlignment="Center"
                TextColor="#195377"
                FontAttributes="Bold"
                BackgroundColor="Transparent"/>
                                </StackLayout>
                            </Grid>
                        </Frame>
                        <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 2,2,2,2"
                            Padding="0"
                            Style="{StaticResource GradientBorderStyleBlue}"

                            Margin="0"
                            HeightRequest="66"
                            VerticalOptions="Center"
                            x:Name="btnMoveToHome"
                            HorizontalOptions="FillAndExpand">

                            <t:DrMuscleButton
                                x:Name="HomeButton"
                                VerticalOptions="Center"
                                HeightRequest="66"
                                FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                CornerRadius="2"
                                HorizontalOptions="FillAndExpand"
                                Text="Continue"
                                Clicked="MoveToHome"
                                IsVisible="true"
                                Style="{StaticResource highEmphasisButtonStyle}"
                                BackgroundColor="Transparent"
                                BorderColor="Transparent"
                                TextColor="White" />
                        </Border>
                    </StackLayout>
                </CollectionView.Footer>
            </CollectionView>
            <!--<ListView
                Grid.Row="2"
                BackgroundColor="Transparent"
                ItemTemplate="{StaticResource BotTemplateSelector}"
                HasUnevenRows="True"
                x:Name="lstChats"
                VerticalOptions="FillAndExpand"
                IsVisible="false"
                SeparatorColor="Transparent">
                <ListView.Footer>
                    <StackLayout
                        x:Name="bottomBtns"
                        IsVisible="false"
                        Margin="30,0"
                        Spacing="10">
                        <Frame
    HeightRequest="60"
    BackgroundColor="Transparent"
    Margin="0"
    Padding="2"
    BorderColor="#195377"    
    CornerRadius="0"
    HasShadow="False">
                            <Frame.GestureRecognizers>
                                <TapGestureRecognizer
            NumberOfTapsRequired="1"/>
                            </Frame.GestureRecognizers>
                            <Grid
        BackgroundColor="White"
        HorizontalOptions="FillAndExpand"
        VerticalOptions="FillAndExpand">

                                --><!--Horizontal Stacklayout (row 0; col 0)--><!--
                                <StackLayout
            Grid.Row="0"
            Grid.Column="0"
            HorizontalOptions="Center"
            VerticalOptions="FillAndExpand"
            Orientation="Horizontal"
            Spacing="8"
            Margin="-26,0,0,0"
            BackgroundColor="Transparent">

                                    <Image               
                x:Name="Img"
                Source="ic_share_exercise"
                VerticalOptions="FillAndExpand"
                HorizontalOptions="Center"
                Margin="0,20"/>

                                    <Label                
                x:Name="LblText"
                Text="Share"
                FontSize="15"
                HorizontalOptions="Center"
                VerticalOptions="FillAndExpand"
                HorizontalTextAlignment="Center"
                VerticalTextAlignment="Center"
                TextColor="#195377"
                FontAttributes="Bold"
                BackgroundColor="Transparent"/>
                                </StackLayout>
                            </Grid>
                        </Frame>
                        <Frame
                            Padding="0"
                            Margin="0"
                            IsClippedToBounds="true"
                            CornerRadius="2"
                            HeightRequest="66"
                            VerticalOptions="Center"
                            x:Name="btnMoveToHome"
                            HorizontalOptions="FillAndExpand"
                            Style="{StaticResource GradientFrameStyleBlue}">

                            <t:DrMuscleButton
                                VerticalOptions="Center"
                                HeightRequest="66"
                                FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                CornerRadius="2"
                                HorizontalOptions="FillAndExpand"
                                Text="Workout loaded (Home)"
                                IsVisible="true"
                                Style="{StaticResource highEmphasisButtonStyle}"
                                BackgroundColor="Transparent"
                                BorderColor="Transparent"
                                TextColor="White" />
                        </Frame>
                    </StackLayout>
                </ListView.Footer>
            </ListView>-->

            
            
            <ScrollView
                Grid.Row="2"
                x:Name="mainScroll"
                Grid.RowSpan="5"
                Padding="0,0,0,90">
                <StackLayout>
                    <!--Step2-->
                    <StackLayout
                        x:Name="StackSteps2"
                        Margin="0,0,0,20"
                        IsVisible="false">

                        <!--1-->
                        <!--Welcome back card-->
                        <controls:CustomFrame
                            x:Name="SecondWelcomeBox"
                            Margin="10,13,10,0"
                            Padding="10,10,10,10"
                            CornerRadius="12"
                            HasShadow="True">
                            <StackLayout>
                                <Grid
                                    Margin="0,10,0,0">
                                    <Grid.RowDefinitions>
                                        <RowDefinition
                                            Height="*" />
                                        <RowDefinition
                                            Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition
                                            Width="40" />
                                        <ColumnDefinition
                                            Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <Image
                                        Source="logo2.png"
                                        Margin="-5,-9,0,0"
                                        Grid.Row="0"
                                        WidthRequest="40"
                                        VerticalOptions="Start"
                                        HeightRequest="40" />
                                    <StackLayout
                                        Grid.Column="1"
                                        Grid.Row="0"
                                        Grid.RowSpan="2">
                                        <Label
                                            x:Name="LblWelcomeback"
                                            Text="WELCOME BACK!"
                                            Margin="0,0,0,9"
                                            TextColor="Black"
                                            FontAttributes="Bold"
                                            FontSize="16" />
                                        <Label
                                            x:Name="LblWelcomebackText"
                                            Text="-"
                                            FontSize="17"
                                            LineHeight="{OnPlatform Android='1.3',iOS='1.2'}" 
                                            TextColor="#AA000000"/>
                                    </StackLayout>
                                </Grid>
                                <Grid
                                    HorizontalOptions="FillAndExpand"
                                    Margin="1,20,1,15">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width=".5*"/>
                                        <ColumnDefinition Width=".5*"/>
                                    </Grid.ColumnDefinitions>
                                    <t:DrMuscleButton
                                        Text="HISTORY"
                                        FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                        FontAttributes="Bold"
                                        Grid.Column="0"
                                        HorizontalOptions="Center"
                                        x:Name="BtnHistory"
                                        Clicked="btnHistory_Clicked"
                                        Style="{StaticResource buttonLinkStyle}"
                                        VerticalOptions="Center"
                                        TextColor="{x:Static app:AppThemeConstants.BlueColor}" />
                                    <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 6,6,6,6"
                            Padding="0"
                                        Margin="0"
                                        BackgroundColor="Transparent"
                                        VerticalOptions="Center"
                                        x:Name="btnWelcomeGotit"
                                        Grid.Column="1"
                                        IsVisible="false"
                                        HorizontalOptions="FillAndExpand"
                                        HeightRequest="45">
                                        <StackLayout
                                            HeightRequest="45"
                                            Padding="0"
                                            Margin="0"
                                            VerticalOptions="FillAndExpand"
                                            HorizontalOptions="FillAndExpand"
                                            Style="{StaticResource GradientStackStyleBlue}"
                                            >
                                            
                                            <t:DrMuscleButton
                                            VerticalOptions="Center"
                                            HeightRequest="45"
                                            FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                            CornerRadius="6"
                                            HorizontalOptions="FillAndExpand"
                                            Text="GOT IT"
                                            Clicked="btnWelcomeGotit_Clicked"
                                            IsVisible="true"
                                            Style="{StaticResource highEmphasisButtonStyle}"
                                            BackgroundColor="Transparent"
                                            BorderColor="Transparent"
                                            TextColor="White" />
                                        </StackLayout>
                                    </Border>
                                    <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 6,6,6,6"
                            Padding="0"
                                        Margin="0"
                                        BackgroundColor="Transparent"
                                        VerticalOptions="Center"
                                        x:Name="btnWelcomeStartWorkout"
                                        Grid.Column="1"
                                        IsVisible="false"
                                        HorizontalOptions="FillAndExpand"
                                        HeightRequest="45">
                                        <StackLayout
                                            HeightRequest="45"
                                            Padding="0"
                                            Margin="0"
                                            VerticalOptions="FillAndExpand"
                                            HorizontalOptions="FillAndExpand"
                                            Style="{StaticResource GradientStackStyleBlue}"
                                            >
                                            <t:DrMuscleButton
                                            VerticalOptions="Center"
                                            HeightRequest="45"
                                            FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                            CornerRadius="6"
                                            x:Name="btnstsrtWorkoutTitle"
                                            AutomationId="StartWorkoutButton"
                                            HorizontalOptions="FillAndExpand"
                                            Text="START WORKOUT"
                                            Clicked="btnWelcomeStartWorkout_Clicked"
                                            IsVisible="true"
                                            Style="{StaticResource highEmphasisButtonStyle}"
                                            BackgroundColor="Transparent"
                                            BorderColor="Transparent"
                                            TextColor="White" />
                                        </StackLayout>
                                    </Border>
                                </Grid>
                            </StackLayout>
                        </controls:CustomFrame>

                        <!-- AI Chat Card -->
                        <controls:CustomFrame
                            x:Name="AIChatBox"
                            IsVisible="false"
                            Margin="10,10,10,0"
                            Padding="10,10,10,10"
                            CornerRadius="12"
HasShadow="True">
                            <StackLayout>
                                <Grid
                                    Margin="0,10,0,0">
                                    <Grid.RowDefinitions>
                                        <RowDefinition
                                            Height="*" />
                                        <RowDefinition
                                            Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition
                                            Width="40" />
                                        <ColumnDefinition
                                            Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <!--<Image
                                        Source="WorkoutDone.png"
                                        Margin="{OnPlatform iOS='0,-8,0,0', Android='0,-4,0,0'}"
                                        Grid.Row="0"
                                        WidthRequest="40"
                                        VerticalOptions="Start"
                                        HeightRequest="35" />
                                    <StackLayout
                                        Grid.Column="1"
                                        Grid.Row="0"
                                        Grid.RowSpan="2">
                                        <Label
                                            x:Name="LblXXWorkout"
                                            Text="-"
                                            Margin="0,0,0,9"
                                            TextColor="Black"
                                            FontAttributes="Bold"
                                            FontSize="19" />
                                        <Label
                                            x:Name="LblXXWeightLeft"
                                            Text="-"
                                            TextColor="#AA000000"
                                            FontSize="{x:Static app:AppThemeConstants.DescriptionFontSize}" />
                                    </StackLayout>-->
                                </Grid>
                                <Grid
                                    HorizontalOptions="FillAndExpand"
                                    Margin="1,20,1,15">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width=".5*"/>
                                        <ColumnDefinition Width=".5*"/>
                                    </Grid.ColumnDefinitions>
                                    <t:DrMuscleButton
                                        Text="HELP WITH GOAL"
                                        FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                        FontAttributes="Bold"
                                        Grid.Column="0"
                                        HorizontalOptions="Center"
                                        x:Name="BtnHelpWithGoal"
                                        Clicked="HelpWithGoal_Clicked"
                                        Style="{StaticResource buttonLinkStyle}"
                                        VerticalOptions="Center"
                                        TextColor="{x:Static app:AppThemeConstants.BlueColor}" />
                                    <t:DrMuscleButton
                                        Text="UPDATE GOAL"
                                        FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                        FontAttributes="Bold"
                                        Grid.Column="0"
                                        HorizontalOptions="Center"
                                        x:Name="btnUpdateGoal3"
                                        Clicked="btnUpdateGoal_Clicked"
                                        Style="{StaticResource buttonLinkStyle}"
                                        VerticalOptions="Center"
                                        TextColor="{x:Static app:AppThemeConstants.BlueColor}" />

                                    <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 6,6,6,6"
                            Padding="0"
                            BackgroundColor="Transparent"
                                        Margin="0"
                                        
                                        VerticalOptions="Center"
                                        x:Name="btnAIChat"
                                        Grid.Column="1"
                                        HorizontalOptions="FillAndExpand"
                                        HeightRequest="45">
                                        <StackLayout
                                            HeightRequest="45"
                                            Padding="0"
                                            Margin="0"
                                            VerticalOptions="FillAndExpand"
                                            HorizontalOptions="FillAndExpand"
                                            Style="{StaticResource GradientStackStyleBlue}"
                                            >
                                            <t:DrMuscleButton
                                            VerticalOptions="Center"
                                            HeightRequest="45"
                                            FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                            CornerRadius="6"
                                            HorizontalOptions="FillAndExpand"
                                            Text="AI CHAT"
                                            Clicked="OpenChat_Clicked"
                                            IsVisible="true"
                                            Style="{StaticResource highEmphasisButtonStyle}"
                                            BackgroundColor="Transparent"
                                            BorderColor="Transparent"
                                            TextColor="White" />

                                        </StackLayout>
                                    </Border>
                                </Grid>
                            </StackLayout>
                        </controls:CustomFrame>

                        <!--Second goal box-->
                        <controls:CustomFrame
                            Padding="0,0,0,10"
                            x:Name="GoalBox2"
                            Margin="10,10,10,0"
                            CornerRadius="12"
HasShadow="True"
                            IsClippedToBounds="True">
                            <StackLayout>
                                <Frame
                                    Margin="{OnPlatform Android='-3', iOS='-2'}"
Padding="0"
CornerRadius="{OnPlatform Android='18', iOS='12'}"
                                    IsClippedToBounds="True"
                                    HasShadow="False">
                                    <ffimageloading:CachedImage
                                        ErrorPlaceholder="backgroundblack.png"
                                        CacheDuration="30"
                                        CacheType="All" 
                                        FadeAnimationEnabled="True"
                                        HeightRequest="{OnPlatform iOS='270'}"
                                        Source="settingsbackground.png"
                                        Aspect="AspectFill" />
                                </Frame>
                                <Label
                                    x:Name="LblLearnGoalTitle2"
                                    Margin="20.5,11,20.5,0"
                                    FontSize="19"
                                    Text="Muscle guide"
                                    TextColor="Black"
                                    FontAttributes="Bold" />
                                <Label
                                    x:Name="LblLearnGoal2"
                                    Margin="20.5,9,20.5,0"
                                    FontSize="17"
                                    LineHeight="{OnPlatform Android='1.3',iOS='1.2'}" 
                                    TextColor="#AA000000" />
                                <Grid
                                    ColumnSpacing="10"
                                    HorizontalOptions="FillAndExpand"
                                    Margin="11,20,11,13">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width=".5*"/>
                                        <ColumnDefinition Width=".5*"/>
                                    </Grid.ColumnDefinitions>
                                    <t:DrMuscleButton
                                        Grid.Column="0"
                                        Padding="0"
                                        Text="LATER"
                                        HorizontalOptions="FillAndExpand"
                                        FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                        FontAttributes="Bold"
                                        CornerRadius="6"
                                        VerticalOptions="End"
                                        HeightRequest="45"
                                        Style="{StaticResource buttonLinkStyle}"
                                        Margin="0,0,0,0"
                                        BackgroundColor="Transparent"
                                        TextColor="{x:Static app:AppThemeConstants.BlueColor}" 
                                        Clicked="Later_Clicked2"/>
                                    <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 6,6,6,6"
                            Padding="0"
                            BackgroundColor="Transparent"
                                        Margin="0"
                                        
                                        Grid.Column="1"
                                        HorizontalOptions="FillAndExpand"
                                        HeightRequest="45">
                                        <StackLayout
                                            HeightRequest="45"
                                            Padding="0"
                                            Margin="0"
                                            VerticalOptions="FillAndExpand"
                                            HorizontalOptions="FillAndExpand"
                                            Style="{StaticResource GradientStackStyleBlue}"
                                            >
                                            <t:DrMuscleButton
                                            VerticalOptions="EndAndExpand"
                                            HeightRequest="45"
                                            FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                            CornerRadius="6"
                                            HorizontalOptions="FillAndExpand"
                                            Text="READ NOW"
                                            Clicked="Learn_Clicked2"
                                            IsVisible="true"
                                            Style="{StaticResource highEmphasisButtonStyle}"
                                            BackgroundColor="Transparent"
                                            BorderColor="Transparent"
                                            TextColor="White" />

                                        </StackLayout>
                                    </Border>
                                </Grid>
                            </StackLayout>
                        </controls:CustomFrame>

                        <!--Progress box-->
                        <controls:CustomFrame
                            x:Name="SecondProgressBox"
                            Margin="10,10,10,0"
                            Padding="10,10,10,10"
                            CornerRadius="12"
                            IsVisible="true"
HasShadow="True">
                            <!--<SwipeView>
                                <SwipeView.RightItems>
                                    <SwipeItems
                                        Mode="Execute">
                                        <SwipeItem
                                            Text=""
                                            IconImageSource="delete.png"
                                            Invoked="btnWelcomeProgress_Clicked" />
                                    </SwipeItems>
                                </SwipeView.RightItems>
                                <SwipeView.LeftItems>
                                    <SwipeItems
                                        Mode="Execute">
                                        <SwipeItem
                                            Text=""
                                            IconImageSource="delete.png"
                                            Invoked="btnWelcomeProgress_Clicked" />
                                    </SwipeItems>
                                </SwipeView.LeftItems>
                            </SwipeView>-->
                            <StackLayout>
                                <Grid
                                    Margin="0,10,0,0">
                                    <Grid.RowDefinitions>
                                        <RowDefinition
                                            Height="*" />
                                        <RowDefinition
                                            Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition
                                            Width="40" />
                                        <ColumnDefinition
                                            Width="*" />
                                    </Grid.ColumnDefinitions>

                                    <Image
                                        Source="lamp.png"
                                        Margin="0,-8,0,0"
                                        Grid.Row="0"
                                        WidthRequest="40"
                                        VerticalOptions="Start"
                                        HeightRequest="40" />
                                    <StackLayout
                                        Grid.Column="1"
                                        Grid.Row="0"
                                        Grid.RowSpan="2">
                                        <Label
                                            x:Name="LblStrengthUp"
                                            Text="Strength up!"
                                            Margin="0,0,0,9"
                                            TextColor="Black"
                                            FontAttributes="Bold"
                                            FontSize="19" />
                                        <Label
                                            x:Name="LblStrengthUpText"
                                            Text="-"
                                            FontSize="17"
                                            LineHeight="{OnPlatform Android='1.3',iOS='1.2'}" 
                                            TextColor="#AA000000" />
                                    </StackLayout>
                                </Grid>
                                <Grid
                                    HorizontalOptions="FillAndExpand"
                                    Margin="1,20,1,15">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width=".5*"/>
                                        <ColumnDefinition Width=".5*"/>
                                    </Grid.ColumnDefinitions>
                                    <t:DrMuscleButton
                                        Text="LEARN MORE"
                                        FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                        FontAttributes="Bold"
                                        Grid.Column="0"
                                        HorizontalOptions="Center"
                                        x:Name="BtnLearnMore2"
                                        Clicked="BtnLearnMore_Clicked"
                                        Style="{StaticResource buttonLinkStyle}"
                                        VerticalOptions="Center"
                                        TextColor="{x:Static app:AppThemeConstants.BlueColor}" />
                                    <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 6,6,6,6"
                            Padding="0"
                            BackgroundColor="Transparent"
                                        Margin="0"
                                        
                                        VerticalOptions="Center"
                                        x:Name="btnWorkoutNow"
                                        Grid.Column="1"
                                        IsVisible="false"
                                        HorizontalOptions="FillAndExpand"
                                        HeightRequest="45">
                                        <StackLayout
                                            HeightRequest="45"
                                            Padding="0"
                                            Margin="0"
                                            VerticalOptions="FillAndExpand"
                                            HorizontalOptions="FillAndExpand"
                                            Style="{StaticResource GradientStackStyleBlue}"
                                            >

                                        <t:DrMuscleButton
                                            VerticalOptions="Center"
                                            HeightRequest="45"
                                            FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                            CornerRadius="6"
                                            HorizontalOptions="FillAndExpand"
                                            Text="ADD WORKOUT"
                                            Clicked="btnChangeWokoutNow_Clicked"
                                            IsVisible="true"
                                            Style="{StaticResource highEmphasisButtonStyle}"
                                            BackgroundColor="Transparent"
                                            BorderColor="Transparent"
                                            TextColor="White" />
                                        </StackLayout>
                                    </Border>
                                    <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 6,6,6,6"
                            Padding="0"
                                        Margin="0"
                                        
                                        VerticalOptions="Center"
                                        x:Name="btnRestNow"
                                        Grid.Column="1"
                                        IsVisible="false"
                                        HorizontalOptions="FillAndExpand"
                                        BackgroundColor="Transparent"
                                        HeightRequest="45">
                                        <StackLayout
                                            HeightRequest="45"
                                            Padding="0"
                                            Margin="0"
                                            VerticalOptions="FillAndExpand"
                                            HorizontalOptions="FillAndExpand"
                                            Style="{StaticResource GradientStackStyleBlue}"
                                            >
                                            <t:DrMuscleButton
                                            VerticalOptions="Center"
                                            HeightRequest="45"
                                            FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                            CornerRadius="6"
                                            HorizontalOptions="FillAndExpand"
                                            Text="RECOVERY WORKOUT"
                                            Clicked="btnRecoveryWorkout_Clicked"
                                            IsVisible="true"
                                            Style="{StaticResource highEmphasisButtonStyle}"
                                            BackgroundColor="Transparent"
                                            BorderColor="Transparent"
                                            TextColor="White" />

                                        </StackLayout>
                                    </Border>
                                </Grid>
                            </StackLayout>
                        </controls:CustomFrame>

                        <!--WeightProgress2 card-->
                        <controls:CustomFrame
                            IsVisible="false"
                            x:Name="WeightProgress2"
                            Margin="10,10,10,0"
                            Padding="10,10,10,10"
                            CornerRadius="12"
HasShadow="True">
                            <StackLayout>
                                <Grid
                                    RowSpacing="0"
                                    Margin="0,17,0,0">
                                    <Grid.RowDefinitions>
                                        <RowDefinition
                                            Height="Auto" />
                                        <RowDefinition
                                            Height="Auto" />
                                        <RowDefinition
                                            Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition
                                            Width="40" />
                                        <ColumnDefinition
                                            Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <Image
                                        Grid.Column="0"
                                        VerticalOptions="Start"
                                        HorizontalOptions="Center"
                                        Margin="0,-8,0,0"
                                        Source="bodyweight.png"
                                        WidthRequest="27"
                                        HeightRequest="27" />
                                    <StackLayout
                                        Grid.Column="1"
                                        Grid.Row="0">
                                        <Label
                                            x:Name="LblWeightToGo2"
                                            Text="Weight progress"
                                            TextColor="Black"
                                            FontAttributes="Bold"
                                            FontSize="19"
                                            Margin="0,-8,0,9" />
                                        <!--<Label
                                            x:Name="LblWeightTipText2"
                                            Text="-"
                                            TextColor="#AA000000"
                                            FontSize="{x:Static app:AppThemeConstants.DescriptionFontSize}" />-->
                                    </StackLayout>

                                    <Grid
                                        Grid.Row="1"
                                        Grid.Column="0"
                                        Grid.ColumnSpan="2"
                                        Margin="0,0"
                                        RowSpacing="0"
                                        BackgroundColor="White"
                                        ColumnSpacing="5">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="60" />
                                            <RowDefinition
                                                Height="*" />
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition
                                                Width="*" />
                                            <ColumnDefinition
                                                Width="*" />
                                            <ColumnDefinition
                                                Width="*" />
                                        </Grid.ColumnDefinitions>

                                        <!--Start weight-->
                                        <Frame
                                            CornerRadius="6"
                                            Grid.Row="0"
                                            Grid.Column="0"
                                            HeightRequest="45"
                                            BackgroundColor="White"
                                            HasShadow="False"
                                            Padding="0">
                                            <StackLayout
                                                VerticalOptions="Center"
                                                HorizontalOptions="Center">
                                                <Label
                                                    x:Name="LblStartText2"
                                                    FontSize="17"
                                                    FontAttributes="Bold"
                                                    Style="{StaticResource LabelStyle}"
                                                    HorizontalOptions="Center"
                                                    HorizontalTextAlignment="Center"
                                                    TextColor="Black" />
                                                <Label
                                                    x:Name="LblStartWeight2"
                                                    Text="Start weight"
                                                    FontSize="15"
                                                    LineHeight="{OnPlatform Android='1.1',iOS='1'}" 
                                                    TextColor="#AA000000"
                                                    HorizontalOptions="CenterAndExpand"
                                                    HorizontalTextAlignment="Center" />
                                            </StackLayout>
                                        </Frame>

                                        <!--Current weight-->
                                        <Frame
                                            CornerRadius="6"
                                            Grid.Row="0"
                                            Grid.Column="1"
                                            HeightRequest="45"
                                            BackgroundColor="White"
                                            HasShadow="False"
                                            Padding="0">
                                            <StackLayout
                                                VerticalOptions="Center"
                                                HorizontalOptions="Center">
                                                <Label
                                                    x:Name="LblCurrentText2"
                                                    FontSize="17"
FontAttributes="Bold"
                                                    Style="{StaticResource LabelStyle}"
                                                    HorizontalOptions="Center"
                                                    HorizontalTextAlignment="Center"
                                                    TextColor="Black" />
                                                <Label
                                                    x:Name="LblCurrentWeight2"
                                                    Text="Current weight"
                                                    FontSize="15"
                                                    LineHeight="{OnPlatform Android='1.1',iOS='1.1'}" 
                                                    TextColor="#AA000000"
                                                    HorizontalOptions="CenterAndExpand"
                                                    HorizontalTextAlignment="Center" />
                                            </StackLayout>
                                        </Frame>


                                        <!--Goal weight-->
                                        <Frame
                                            CornerRadius="6"
                                            Grid.Row="0"
                                            Grid.Column="2"
                                            HeightRequest="75"
                                            HasShadow="False"
                                            BackgroundColor="White"
                                            Padding="0">
                                            <StackLayout
                                                VerticalOptions="Center"
                                                HorizontalOptions="Center">
                                                <Label
                                                    x:Name="LblGoalText2"
                                                    FontSize="17"
FontAttributes="Bold"
                                                    Style="{StaticResource LabelStyle}"
                                                    HorizontalOptions="Center"
                                                    HorizontalTextAlignment="Center"
                                                    TextColor="Black" />
                                                <Label
                                                    x:Name="LblGoalWeight2"
                                                    Text="Goal weight"
                                                    FontSize="15"
                                                    LineHeight="{OnPlatform Android='1.1',iOS='1'}" 
                                                    TextColor="#AA000000"
                                                    HorizontalOptions="CenterAndExpand"
                                                    HorizontalTextAlignment="Center" />
                                            </StackLayout>
                                        </Frame>
                                        <!--Tracker-->
                                        <StackLayout
                                            Grid.Row="0"
                                            IsVisible="false"
                                            Grid.Column="0"
                                            Grid.ColumnSpan="3">
                                            <Label
                                                HorizontalOptions="CenterAndExpand"
                                                x:Name="LbltrackerText2"
                                                FontSize="17" />
                                            <Frame
                                                x:Name="FrmTracker2"
                                                HasShadow="False"
                                                Margin="20,0"
                                                Padding="0"
                                                HeightRequest="10"
                                                CornerRadius="5" />
                                        </StackLayout>
                                    </Grid>
                                </Grid>

                                <Grid
                                    HorizontalOptions="FillAndExpand"
                                    Margin="1,10,1,15">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width=".5*"/>
                                        <ColumnDefinition Width=".5*"/>
                                    </Grid.ColumnDefinitions>
                                    <t:DrMuscleButton
                                        Text="UPDATE GOAL"
                                        FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                        FontAttributes="Bold"
                                        Grid.Column="0"
                                        HorizontalOptions="Center"
                                        Clicked="btnUpdateGoal_Clicked"
                                        VerticalOptions="Center"
                                        Style="{StaticResource buttonLinkStyle}"
                                        TextColor="{x:Static app:AppThemeConstants.BlueColor}" />
                                    <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 6,6,6,6"
                            Padding="0"
                            BackgroundColor="Transparent"
                                        Margin="0"
                                        
                                        Grid.Column="1"
                                        HorizontalOptions="FillAndExpand"
                                        VerticalOptions="Center"
                                        HeightRequest="45">
                                        <StackLayout
                                            HeightRequest="45"
                                            Padding="0"
                                            Margin="0"
                                            VerticalOptions="FillAndExpand"
                                            HorizontalOptions="FillAndExpand"
                                            Style="{StaticResource GradientStackStyleBlue}"
                                            >
                                            <t:DrMuscleButton
                                            CornerRadius="6"
                                            VerticalOptions="Fill"
                                            HeightRequest="45"
                                            FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                            HorizontalOptions="FillAndExpand"
                                            Text="LOG WEIGHT"
                                            Clicked="EnterWeight_Clicked"
                                            IsVisible="true"
                                            Style="{StaticResource highEmphasisButtonStyle}"
                                            BackgroundColor="Transparent"
                                            BorderColor="Transparent"
                                            TextColor="White" />

                                        </StackLayout>
                                    </Border>
                                </Grid>
                            </StackLayout>
                        </controls:CustomFrame>

                        <!--Weight Coaching card-->
                        <controls:CustomFrame
                            x:Name="WeightCoachingCard2"
                            Margin="10,10,10,0"
                            Padding="10,10,10,10"
                            CornerRadius="12"
                            IsVisible="False"
HasShadow="True">
                            <StackLayout>
                                <Grid
                                    Margin="0,10,0,15">
                                    <Grid.RowDefinitions>
                                        <RowDefinition
                                            Height="*" />
                                        <RowDefinition
                                            Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition
                                            Width="40" />
                                        <ColumnDefinition
                                            Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <Image
                                        Grid.Column="0"
                                        VerticalOptions="Start"
                                        HorizontalOptions="Center"
                                        Margin="0,-8,0,0"
                                        Source="lamp.png"
                                        WidthRequest="40"
                                        HeightRequest="40" />
                                    <StackLayout
                                        Grid.Column="1"
                                        Grid.Row="0"
                                        Grid.RowSpan="2">
                                        <Label
                                            x:Name="LblWeightTip2"
                                            Text=""
                                            TextColor="Black"
                                            FontAttributes="Bold"
                                            FontSize="19"
                                            Margin="0,0,0,9" />
                                        <Label
                                            x:Name="LblWeightTipText2"
                                            Text="-"
                                            FontSize="17"
                                            LineHeight="{OnPlatform Android='1.3',iOS='1.2'}" 
                                            TextColor="#AA000000" />
                                    </StackLayout>
                                </Grid>
                                <Grid
                                    HorizontalOptions="FillAndExpand"
                                    Margin="1,10,1,15">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width=".5*"/>
                                        <ColumnDefinition Width=".5*"/>
                                    </Grid.ColumnDefinitions>
                                    <t:DrMuscleButton
    Text="WEIGHT PROGRESS"
    FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
    FontAttributes="Bold"
    Grid.Column="0"
    HorizontalOptions="Center"
    Clicked="BtnWeightHistory_Clicked"
    VerticalOptions="Center"
    Style="{StaticResource buttonLinkStyle}"
    TextColor="{x:Static app:AppThemeConstants.BlueColor}" />
                                    <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 6,6,6,6"
                            Padding="0"
                            BackgroundColor="Transparent"
    Margin="0"
    
    Grid.Column="1"
    HorizontalOptions="FillAndExpand"
    VerticalOptions="Center"
    HeightRequest="45">

                                        <StackLayout
                                            HeightRequest="45"
                                            Padding="0"
                                            Margin="0"
                                            VerticalOptions="FillAndExpand"
                                            HorizontalOptions="FillAndExpand"
                                            Style="{StaticResource GradientStackStyleBlue}"
                                            >

                                            <t:DrMuscleButton
            CornerRadius="6"
            VerticalOptions="Fill"
            HeightRequest="45"
            FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
            x:Name="btnUpdateGoal2"
            HorizontalOptions="FillAndExpand"
            Text="UPDATE GOAL"
            Clicked="btnUpdateGoal_Clicked"
            IsVisible="false"
            Style="{StaticResource highEmphasisButtonStyle}"
            BackgroundColor="Transparent"
            BorderColor="Transparent"
            TextColor="White" />
                                                <t:DrMuscleButton
            CornerRadius="6"
            VerticalOptions="Fill"
            HeightRequest="45"
            FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
            x:Name="btnLogWeight2"
            HorizontalOptions="FillAndExpand"
            Text="LOG WEIGHT"
            Clicked="EnterWeight_Clicked"
            IsVisible="false"
            Style="{StaticResource highEmphasisButtonStyle}"
            BackgroundColor="Transparent"
            BorderColor="Transparent"
            TextColor="White" />
                                        </StackLayout>
                                    </Border>
                                </Grid>
                            </StackLayout>
                        </controls:CustomFrame>
                        
                        <!--2-->
                        <!--CaloriesAdjustment card-->
                        <controls:CustomFrame
x:Name="CaloriesAdjustmentCard2"
Margin="10,10,10,0"
Padding="10,10,10,10"
CornerRadius="12"
IsVisible="true"
HasShadow="True">
                            <StackLayout>
                                <Grid
        Margin="0,10,0,15">
                                    <Grid.RowDefinitions>
                                        <RowDefinition
                Height="*" />
                                        <RowDefinition
                Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition
                Width="40" />
                                        <ColumnDefinition
                Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <Image
            Grid.Column="0"
            VerticalOptions="Start"
            Margin="0,-2,0,0"
                                    Source="bodyweight.png"
                                    WidthRequest="27"
                                    HorizontalOptions="Center"
                                    HeightRequest="27" />
                                    <StackLayout
            Grid.Column="1"
            Grid.Row="0"
            Grid.RowSpan="2">
                                        <Label
                x:Name="LblCaloriesAdjustmentTip2"
                Text="x-day weight trend: x.xx lbs"
                TextColor="Black"
                FontAttributes="Bold"
                FontSize="19"
                Margin="0,0,0,9" />
                                        <Label
                x:Name="LblCaloriesAdjustmentTipText2"
                Text="That's more weight loss than expected (x.xxx lbs) for someone with 8 weeks on record like you. Possible muscle loss. Calories adjusted: xxxx ⮕ xxxx."
                FontSize="17"
                LineHeight="{OnPlatform Android='1.3',iOS='1.2'}" 
                TextColor="#AA000000" />
                                    </StackLayout>
                                </Grid>
                                <!--<Grid
        HorizontalOptions="FillAndExpand"
        Margin="1,10,1,15">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width=".5*"/>
                                        <ColumnDefinition Width=".5*"/>
                                    </Grid.ColumnDefinitions>
                                    <t:DrMuscleButton
            Text="LOG WEIGHT"
            FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
            FontAttributes="Bold"
            Grid.Column="0"
            HorizontalOptions="Center"
            Clicked="EnterWeight_Clicked"
            VerticalOptions="Center"
            Style="{StaticResource buttonLinkStyle}"
            TextColor="{x:Static app:AppThemeConstants.BlueColor}" />
                                    <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 6,6,6,6"
                            Padding="0"
                            BackgroundColor="Transparent"
            Margin="0"
            
            Grid.Column="1"
            HorizontalOptions="FillAndExpand"
            VerticalOptions="Center"
            
            HeightRequest="45">

                                        <StackLayout
    HeightRequest="45"
    Padding="0"
    Margin="0"
    VerticalOptions="FillAndExpand"
    HorizontalOptions="FillAndExpand"
    Style="{StaticResource GradientStackStyleBlue}"
    >


                                        <t:DrMuscleButton
                                            CornerRadius="6"
                                            VerticalOptions="Fill"
                                            HeightRequest="45"
                                            FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                            HorizontalOptions="FillAndExpand"
                                            Text=""
                                            x:Name="btnUpdateMealPlan"
                                            Clicked="GetMealPlan_Clicked"
                                            IsVisible="true"
                                            Style="{StaticResource highEmphasisButtonStyle}"
                                            BackgroundColor="Transparent"
                                            BorderColor="Transparent"
                                            TextColor="White" />
                                        </StackLayout>
                                    </Border>
                                </Grid>-->
                                <Grid
                                    HorizontalOptions="FillAndExpand"
                                    Margin="1,10,1,15">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width=".5*"/>
                                        <ColumnDefinition Width=".5*"/>
                                    </Grid.ColumnDefinitions>
                                    <t:DrMuscleButton
                                        Text="EDIT CALORIES"
                                        Clicked="EditCalories_Clicked"
                                        FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                        FontAttributes="Bold"
                                        Grid.Column="0"
                                        HorizontalOptions="Center"
                                        VerticalOptions="Center"
                                        Style="{StaticResource buttonLinkStyle}"
                                        TextColor="{x:Static app:AppThemeConstants.BlueColor}" />
                                    <Border
                                        Stroke="Transparent"
                                        StrokeShape="RoundRectangle 6,6,6,6"
                                        Padding="0"
                                        BackgroundColor="Transparent"
                                        Margin="0"
                                        
                                        Grid.Column="1"
                                        HorizontalOptions="FillAndExpand"
                                        VerticalOptions="Center"
                                        
                                        HeightRequest="45">

                                        <StackLayout
                                            HeightRequest="45"
                                            Padding="0"
                                            Margin="0"
                                            VerticalOptions="FillAndExpand"
                                            HorizontalOptions="FillAndExpand"
                                            Style="{StaticResource GradientStackStyleBlue}"
                                            >
                                            <t:DrMuscleButton
                                            CornerRadius="6"
                                            VerticalOptions="Fill"
                                            HeightRequest="45"
                                            FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                            HorizontalOptions="FillAndExpand"
                                            Text="LOG WEIGHT"
                                            Clicked="EnterWeight_Clicked"
                                            IsVisible="true"
                                            Style="{StaticResource highEmphasisButtonStyle}"
                                            BackgroundColor="Transparent"
                                            BorderColor="Transparent"
                                            TextColor="White" />
                                        </StackLayout>
                                    </Border>
                                </Grid>
                            </StackLayout>
                        </controls:CustomFrame>
                        
                        <!--3-->
                        <!--Target Intake card-->
                        <controls:CustomFrame
                            x:Name="TargetIntake2"
                            Margin="10,10,10,0"
                            Padding="10,10,10,10"
                            CornerRadius="12"
                            HasShadow="True">
                            <StackLayout>
                                <Grid
                                    RowSpacing="0"
                                    Margin="0,17,0,0">
                                    <Grid.RowDefinitions>
                                        <RowDefinition
                                            Height="Auto" />
                                        <RowDefinition
                                            Height="Auto" />
                                        <RowDefinition
                                            Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition
                                            Width="40" />
                                        <ColumnDefinition
                                            Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <Image
                                        Grid.Column="0"
                                        VerticalOptions="Start"
                                        HorizontalOptions="Center"
                                        Margin="{OnPlatform Android='0,-8,0,0', iOS='0,-10,0,0'}"
                                        Source="applefruite.png"
                                        WidthRequest="27"
                                        HeightRequest="27" />
                                    <StackLayout
                                        Grid.Column="1"
                                        Grid.Row="0">
                                        <Label
                                            x:Name="LblTargetIntake2"
                                            Text="xxxx cal/day to build muscle"
                                            TextColor="Black"
                                            FontAttributes="Bold"
                                            FontSize="19"
                                            Margin="0,-8,0,9" >
                                            <Label.GestureRecognizers>
                                                <TapGestureRecognizer Tapped="GetMealPlan_Clicked"/>
                                            </Label.GestureRecognizers>
                                        </Label>
                                    </StackLayout>

                                    <Grid
                                        Grid.Row="1"
                                        Grid.Column="0"
                                        Grid.ColumnSpan="2"
                                        Margin="0,0"
                                        RowSpacing="0"
                                        BackgroundColor="White"
                                        ColumnSpacing="10">
                                        <Grid.RowDefinitions>
                                            <RowDefinition
                                                Height="*" />
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition
                                                Width="*" />
                                            <ColumnDefinition
                                                Width="*" />
                                            <ColumnDefinition
                                                Width="*" />
                                        </Grid.ColumnDefinitions>

                                        <!--Protein-->
                                        <Frame
                                            CornerRadius="6"
                                            Grid.Row="0"
                                            Grid.Column="0"
                                            HeightRequest="75"
                                            BorderColor="Transparent"
                                            BackgroundColor="White"
                                            HasShadow="False"
                                            Padding="0">
                                            <StackLayout
                                                VerticalOptions="Center"
                                                HorizontalOptions="Center">
                                                <Label
                                                    x:Name="LblProteinText2"
                                                    FontSize="17"
FontAttributes="Bold"
                                                    Text="xxx - xxx g"
                                                    Style="{StaticResource LabelStyle}"
                                                    HorizontalOptions="Center"
                                                    HorizontalTextAlignment="Center"
                                                    TextColor="Black" />
                                                <Label
                                                    x:Name="LblProtein2"
                                                    Text="Protein"
                                                    FontSize="17"
                                                    LineHeight="{OnPlatform Android='1.3',iOS='1.2'}" 
                                                    TextColor="#AA000000"
                                                    HorizontalOptions="CenterAndExpand"
                                                    HorizontalTextAlignment="Center" />
                                            </StackLayout>
                                        </Frame>

                                        <!--Carbs-->
                                        <Frame
                                            CornerRadius="6"
                                            Grid.Row="0"
                                            Grid.Column="1"
                                            HeightRequest="75"
                                            BackgroundColor="White"
                                            BorderColor="Transparent"
                                            HasShadow="False"
                                            Padding="0">
                                            <StackLayout
                                                VerticalOptions="Center"
                                                HorizontalOptions="Center">
                                                <Label
                                                    x:Name="LblCarbText2"
                                                    FontSize="17"
FontAttributes="Bold"
                                                    Text="xxx - xxx g"
                                                    Style="{StaticResource LabelStyle}"
                                                    HorizontalOptions="Center"
                                                    HorizontalTextAlignment="Center"
                                                    TextColor="Black" />
                                                <Label
                                                    x:Name="LblCarb2"
                                                    Text="Carbs"
                                                    FontSize="17"
                                                    LineHeight="{OnPlatform Android='1.3',iOS='1.2'}"
                                                    TextColor="#AA000000"
                                                    HorizontalOptions="CenterAndExpand"
                                                    HorizontalTextAlignment="Center" />
                                            </StackLayout>
                                        </Frame>

                                        <!--Fat-->
                                        <Frame
                                            CornerRadius="6"
                                            Grid.Row="0"
                                            Grid.Column="2"
                                            HeightRequest="75"
                                            HasShadow="False"
                                            BackgroundColor="White"
                                            BorderColor="Transparent"
                                            Padding="0">
                                            <StackLayout
                                                VerticalOptions="Center"
                                                HorizontalOptions="Center">
                                                <Label
                                                    x:Name="LblFatText2"
                                                    FontSize="17"
FontAttributes="Bold"
                                                    Text="xxx - xxx g"
                                                    Style="{StaticResource LabelStyle}"
                                                    HorizontalOptions="Center"
                                                    HorizontalTextAlignment="Center"
                                                    TextColor="Black" />
                                                <Label
                                                    x:Name="LblFat2"
                                                    Text="Fat"
                                                    FontSize="17"
                                                    LineHeight="{OnPlatform Android='1.3',iOS='1.2'}" 
                                                    TextColor="#AA000000"
                                                    HorizontalOptions="CenterAndExpand"
                                                    HorizontalTextAlignment="Center" />
                                            </StackLayout>
                                        </Frame>
                                    </Grid>
                                </Grid>

                                <Grid
                                    HorizontalOptions="FillAndExpand"
                                    Margin="1,10,1,15">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width=".5*"/>
                                        <ColumnDefinition Width=".5*"/>
                                    </Grid.ColumnDefinitions>
                                    <t:DrMuscleButton
                                        Text="LEARN MORE"
                                        FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                        FontAttributes="Bold"
                                        Grid.Column="0"
                                        HorizontalOptions="Center"
                                        Clicked="BtnLearnMore_Clicked"
                                        Style="{StaticResource buttonLinkStyle}"
                                        VerticalOptions="Center"
                                        TextColor="{x:Static app:AppThemeConstants.BlueColor}" />

                                    <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 6,6,6,6"
                            Padding="0"
                            BackgroundColor="Transparent"
                                        Margin="0"
                                        
                                        Grid.Column="1"
                                        HorizontalOptions="FillAndExpand"
                                        VerticalOptions="Center"
                                        
                                        HeightRequest="45">

                                        <StackLayout
    HeightRequest="45"
    Padding="0"
    Margin="0"
    VerticalOptions="FillAndExpand"
    HorizontalOptions="FillAndExpand"
    Style="{StaticResource GradientStackStyleBlue}"
    >

                                        <t:DrMuscleButton
                                                CornerRadius="6"
                                                VerticalOptions="Fill"
                                                HeightRequest="45"
                                                FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                                x:Name="btnMealPlan"
                                                HorizontalOptions="FillAndExpand"
                                                Text=""
                                                Clicked="GetMealPlan_Clicked"
                                                IsVisible="true"
                                                Style="{StaticResource highEmphasisButtonStyle}"
                                                BackgroundColor="Transparent"
                                                BorderColor="Transparent"
                                                TextColor="White" />
                                                <t:DrMuscleButton
                                                CornerRadius="6"
                                                VerticalOptions="Fill"
                                                HeightRequest="45"
                                                FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                                x:Name="btnUpdateGoal"
                                                HorizontalOptions="FillAndExpand"
                                                Text="UPDATE GOAL"
                                                Clicked="btnUpdateGoal_Clicked"
                                                IsVisible="false"
                                                Style="{StaticResource highEmphasisButtonStyle}"
                                                BackgroundColor="Transparent"
                                                BorderColor="Transparent"
                                                TextColor="White" />
                                        </StackLayout>
                                    </Border>
                                </Grid>
                            </StackLayout>
                        </controls:CustomFrame>

                        <!--Second weight box-->
                        <controls:CustomFrame
                            IsVisible="false"
                            x:Name="WeightBox2"
                            Margin="10,10,10,0"
                            Padding="0,0,0,10"
                            CornerRadius="12"
HasShadow="True"
                            IsClippedToBounds="True">
                            <StackLayout>
                                <Frame
                                    Padding="0"
                                    IsClippedToBounds="True"
                                    x:Name="ImgWeight"
                                    CornerRadius="12"
                                    HasShadow="False">
                                    <!--<ffimageloading:CachedImage
                                        
                                        IsVisible="false"
                                        HeightRequest="150"
                                        Aspect="Fill" />-->
                                </Frame>
                                <microcharts:ChartView
                                    x:Name="chartViewWeight"
                                    IsVisible="false"
                                    HorizontalOptions="FillAndExpand"
                                    HeightRequest="200" />
                                <Label
                                    Text=""
                                    x:Name="LblTrackin2"
                                    VerticalOptions="Center"
                                    Margin="20,11,20,0"
                                    FontAttributes="Bold"
                                    FontSize="19"
                                    TextColor="Black" />
                                <StackLayout
                                    Orientation="Horizontal"
                                    Margin="0.5,0">
                                    <Label
                                        x:Name="LblWeightGoal2"
                                        Margin="20,11,20,0"
                                        HorizontalOptions="Start"
                                        FontSize="17"
                                        LineHeight="{OnPlatform Android='1.3',iOS='1.2'}" 
                                        TextColor="#AA000000"
                                        Text="Track your weight to get custom tip to building muscle" />
                                    <ffimageloading:CachedImage
                                        ErrorPlaceholder="backgroundblack.png"
CacheDuration="30"
CacheType="All" 
FadeAnimationEnabled="True"
                                        HorizontalOptions="Center"
                                        x:Name="WeightArrowImage"
                                        IsVisible="false"
                                        Aspect="AspectFit"
                                        Source="{Binding StrengthImage}" />
                                </StackLayout>
                                <Label
                                    Margin="20,9,20,0"
                                    x:Name="WeightArrowText"
                                    FontSize="Medium"
                                    FontAttributes="Bold"
                                    HorizontalOptions="Start"
                                    HorizontalTextAlignment="Start"
                                    TextColor="Black" />
                                <Grid
                                    IsVisible="True"
                                    HorizontalOptions="FillAndExpand"
                                    Margin="11,10,11,15">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width=".5*"/>
                                        <ColumnDefinition Width=".5*"/>
                                    </Grid.ColumnDefinitions>
                                    <t:DrMuscleButton
                                        Text="WEIGHT PROGRESS"
                                        FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                        FontAttributes="Bold"
                                        Clicked="BtnWeightHistory_Clicked"
                                        Grid.Column="0"
                                        HorizontalOptions="Center"
                                        VerticalOptions="Center"
                                        Style="{StaticResource buttonLinkStyle}"
                                        TextColor="{x:Static app:AppThemeConstants.BlueColor}" />
                                    <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 6,6,6,6"
                            Padding="0"
                            BackgroundColor="Transparent"
                                        Margin="0"
                                        
                                        Grid.Column="1"
                                        HorizontalOptions="FillAndExpand"
                                        VerticalOptions="Center"
                                        
                                        HeightRequest="45">
                                        <StackLayout
    HeightRequest="45"
    Padding="0"
    Margin="0"
    VerticalOptions="FillAndExpand"
    HorizontalOptions="FillAndExpand"
    Style="{StaticResource GradientStackStyleBlue}"
    >

                                        <t:DrMuscleButton
                                            CornerRadius="6"
                                            VerticalOptions="Fill"
                                            HeightRequest="45"
                                            FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                            HorizontalOptions="FillAndExpand"
                                            Text="AI ANALYSIS"
                                            Clicked="AIAnalyseBodyWeight_Clicked"
                                            IsVisible="true"
                                            Style="{StaticResource highEmphasisButtonStyle}"
                                            BackgroundColor="Transparent"
                                            BorderColor="Transparent"
                                            TextColor="White" />
                                        </StackLayout>
                                    </Border>
                                </Grid>
                            </StackLayout>
                        </controls:CustomFrame>
                        
                        <!--4-->
                        <!--Volume box-->
                        <controls:CustomFrame
                            x:Name="volumeBox"
                            Margin="10,10,10,0"
                            Padding="0,0,0,10"
                            CornerRadius="12"
                            HasShadow="True"
                            IsClippedToBounds="True">
                            <StackLayout>
                                <Grid
                                    HeightRequest="200">
                                    <microcharts:ChartView
                                        x:Name="chartViewVolume"
                                        Margin="-80,0"
                                        HorizontalOptions="FillAndExpand"
                                        HeightRequest="200" />
                                    <BoxView
                                        IsVisible="true"
                                        Color="Transparent"
                                        BackgroundColor="Transparent">
                                        <BoxView.GestureRecognizers>
                                            <TapGestureRecognizer
                                                Tapped="SetsChart_Tapped" />
                                        </BoxView.GestureRecognizers>
                                    </BoxView>
                                </Grid>
                                <StackLayout
                                    Orientation="Horizontal"
                                    Margin="20,11,20,0">
                                    <Label
                                        x:Name="LblVolumeProgress"
                                        HorizontalOptions="Start"
                                        TextColor="Black"
                                        VerticalOptions="Center"
                                        FontAttributes="Bold"
                                        FontSize="19" />
                                    <ffimageloading:CachedImage
                                        ErrorPlaceholder="backgroundblack.png"
CacheDuration="30"
CacheType="All" 
FadeAnimationEnabled="True"
                                        HorizontalOptions="Center"
                                        x:Name="VolumeArrowImage"
                                        IsVisible="false"
                                        Aspect="AspectFit" />
                                </StackLayout>

                                <!--<StackLayout Orientation="Horizontal">
                                        <t:DrMuscleButton Padding="0" Text="Got it" HorizontalOptions="End" FontSize="17" VerticalOptions="Center"  Margin="10,0,5,0" BackgroundColor="Transparent" TextColor="#007aff"  Clicked="BtnVolumeGotit_Clicked" />
                                </StackLayout>-->
                                <Label
                                    Text="Work sets in the last 7 days."
                                    Margin="20,9,10,20"
                                    HorizontalOptions="Start"
                                    FontSize="17"
                                    LineHeight="{OnPlatform Android='1.3',iOS='1.2'}" 
                                    TextColor="#AA000000"
                                    HorizontalTextAlignment="Start" />
                                <!--<Grid  HorizontalOptions="FillAndExpand" Margin="3,0,3,3">
                                        <StackLayout Grid.Column="0" Margin="0" VerticalOptions="Start" Orientation="Horizontal">
                                            <Label
                                                x:Name="VolumeArrowText"
                                                FontSize="Medium"
                                                FontAttributes="Bold"
                                                HorizontalOptions="Start"
                                                HorizontalTextAlignment="Start" />
                                    </StackLayout>
                                        <t:DrMuscleButton Grid.Column="1" Padding="0" Text="GOT IT" HorizontalOptions="FillAndExpand" FontSize="13" HeightRequest="45" CornerRadius="6" FontAttributes="Bold" VerticalOptions="End" Style="{StaticResource buttonStyle}" Margin="0,0,0,0" BackgroundColor="Transparent" TextColor="{x:Static app:AppThemeConstants.BlueColor}"  Clicked="BtnVolumeGotit_Clicked" />   
                                    </Grid>-->
                                <Grid
                                    IsVisible="True"
                                    HorizontalOptions="FillAndExpand"
                                    Margin="11,10,11,15">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width=".5*"/>
                                        <ColumnDefinition Width=".5*"/>
                                    </Grid.ColumnDefinitions>
                                    <t:DrMuscleButton
                                        Text="WEB DASHBOARD"
                                        FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                        FontAttributes="Bold"
                                        Grid.Column="0"
                                        HorizontalOptions="Center"
                                        Clicked="BtnWebDashboard_Clicked"
                                        VerticalOptions="Center"
                                        Style="{StaticResource buttonLinkStyle}"
                                        TextColor="{x:Static app:AppThemeConstants.BlueColor}" />
                                    <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 6,6,6,6"
                            Padding="0"
                            BackgroundColor="Transparent"
                                        Margin="0"
                                        
                                        Grid.Column="1"
                                        HorizontalOptions="FillAndExpand"
                                        VerticalOptions="Center"
                                        HeightRequest="45">
                                        <StackLayout
    HeightRequest="45"
    Padding="0"
    Margin="0"
    VerticalOptions="FillAndExpand"
    HorizontalOptions="FillAndExpand"
    Style="{StaticResource GradientStackStyleBlue}"
    >

                                        <t:DrMuscleButton
                                            CornerRadius="6"
                                            VerticalOptions="Fill"
                                            HeightRequest="45"
                                            FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                            HorizontalOptions="FillAndExpand"
                                            Text="AI ANALYSIS"
                                            Clicked="AIAnalyseVolume_Clicked"
                                            IsVisible="true"
                                            Style="{StaticResource highEmphasisButtonStyle}"
                                            BackgroundColor="Transparent"
                                            BorderColor="Transparent"
                                            TextColor="White" />
                                        </StackLayout>
                                    </Border>

                                </Grid>
                            </StackLayout>
                        </controls:CustomFrame>

                        <!--5-->
                        <!--Strenth box-->
                        <controls:CustomFrame
                            x:Name="strengthBox"
                            IsClippedToBounds="True"
                            Margin="10,10,10,0"
                            Padding="0,0,0,10"
                            CornerRadius="12"
HasShadow="True">
                            <StackLayout>
                                <Grid
                                    HeightRequest="200">

                                    <microcharts:ChartView
                                        x:Name="chartViewStrength"
                                        Margin="-80,0"
                                        HorizontalOptions="FillAndExpand"
                                        HeightRequest="200" />
                                    <BoxView
                                        IsVisible="true"
                                        Color="Transparent"
                                        BackgroundColor="Transparent">
                                        <BoxView.GestureRecognizers>
                                            <TapGestureRecognizer
                                              Tapped="StrenthChart_Tapped"  />
                                        </BoxView.GestureRecognizers>
                                    </BoxView>
                                </Grid>
                                <StackLayout
                                    Orientation="Horizontal"
                                    Margin="20,11,20,0">

                                    <Label
                                        x:Name="LblStrengthProgress"
                                        HorizontalOptions="Start"
                                        VerticalOptions="Center"
                                        FontAttributes="Bold"
                                        FontSize="19"
                                        TextColor="Black" />
                                    <ffimageloading:CachedImage
                                        ErrorPlaceholder="backgroundblack.png"
CacheDuration="30"
CacheType="All" 
FadeAnimationEnabled="True"
                                        HorizontalOptions="Center"
                                        x:Name="StrengthArrowImage"
                                        Aspect="AspectFit"
                                        Source="{Binding StrengthImage}"
                                        IsVisible="false" />
                                </StackLayout>

                                <!--<StackLayout Orientation="Horizontal">
                                       
                                        <t:DrMuscleButton Text="Got it" HorizontalOptions="End" FontSize="17" VerticalOptions="Center" Padding="0" Margin="10,0,5,0" BackgroundColor="Transparent" TextColor="#007aff"  Clicked="BtnStrengthGotit_Clicked" />
                                    </StackLayout>-->
                                <Label
                                    Margin="20,9,15,20"
                                    Text="Average of all exercises done 3 times recently."
                                    FontSize="17"
                                    LineHeight="{OnPlatform Android='1.3',iOS='1.2'}" 
                                    TextColor="#AA000000" />

                                <Grid
                                    IsVisible="True"
                                    HorizontalOptions="FillAndExpand"
                                    Margin="11,10,11,15">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width=".5*"/>
                                        <ColumnDefinition Width=".5*"/>
                                    </Grid.ColumnDefinitions>
                                    <t:DrMuscleButton
                                        Text="WEB DASHBOARD"
                                        FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                        FontAttributes="Bold"
                                        Grid.Column="0"
                                        HorizontalOptions="Center"
                                        Clicked="BtnWebMyDashboard_Clicked"
                                        VerticalOptions="Center"
                                        Style="{StaticResource buttonLinkStyle}"
                                        TextColor="{x:Static app:AppThemeConstants.BlueColor}" />
                                    <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 6,6,6,6"
                            Padding="0"
                            BackgroundColor="Transparent"
                                        Margin="0"
                                        
                                        Grid.Column="1"
                                        HorizontalOptions="FillAndExpand"
                                        VerticalOptions="Center"
                                        
                                        HeightRequest="45">
                                        <StackLayout
    HeightRequest="45"
    Padding="0"
    Margin="0"
    VerticalOptions="FillAndExpand"
    HorizontalOptions="FillAndExpand"
    Style="{StaticResource GradientStackStyleBlue}"
    >

                                        <t:DrMuscleButton
                                            CornerRadius="6"
                                            VerticalOptions="Fill"
                                            HeightRequest="45"
                                            FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                            HorizontalOptions="FillAndExpand"
                                            Text="AI ANALYSIS"
                                            Clicked="AIAnalyseStrength_Clicked"
                                            IsVisible="true"
                                            Style="{StaticResource highEmphasisButtonStyle}"
                                            BackgroundColor="Transparent"
                                            BorderColor="Transparent"
                                            TextColor="White" />
                                        </StackLayout>
                                    </Border>

                                </Grid>
                            </StackLayout>
                        </controls:CustomFrame>

                        <!--6-->
                        <!--State box-->
                        <controls:CustomFrame
                            x:Name="StateBox"
                            Margin="10,10,10,0"
                            Padding="10,15"
                            CornerRadius="12"
HasShadow="True">

                            <StackLayout>
                                <!--Stack of stats to share on click of Share Stats button-->
                                <StackLayout
                                    x:Name="StackOfStats"
                                    BackgroundColor="White">
                                    <Grid
                                        VerticalOptions="FillAndExpand"
                                        HorizontalOptions="FillAndExpand"
                                        Margin="0"
                                        Padding="0,20,0,0">
                                        <Grid.RowDefinitions>
                                            <RowDefinition
                                                Height="*" />
                                            <RowDefinition
                                                Height="Auto" />
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition
                                                Width="*" />
                                            <ColumnDefinition
                                                Width="*" />
                                            <ColumnDefinition
                                                Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <!--row 0; col 0-->
                                        <StackLayout
                                            Grid.Column="0"
                                            HorizontalOptions="FillAndExpand">
                                            <Image
                                                x:Name="IconResultImage"
                                                Source="chain.png"
                                                Aspect="AspectFit"
                                                HeightRequest="32"
                                                HorizontalOptions="CenterAndExpand" />

                                            <Label
                                                x:Name="lblChainCount"
                                                Text="{Binding ChainCount}"
                                                IsVisible="true"
                                                HorizontalOptions="Center"
                                                FontSize="17"
FontAttributes="Bold"
                                                Style="{StaticResource LabelStyle}"
                                                TextColor="Black" />
                                            <Label
                                                x:Name="lblResult44"
                                                Text="Weeks streak"
                                                IsVisible="true"
                                                HorizontalOptions="Center"
                                                HorizontalTextAlignment="Center"
                                                FontSize="15"
                                                LineHeight="{OnPlatform Android='1.1',iOS='1'}" 
                                                TextColor="#AA000000">
                                                <Label.Triggers>
                                                    <DataTrigger
                                                        TargetType="Label"
                                                        Binding="{Binding ChainCount}"
                                                        Value="1">
                                                        <Setter
                                                            Property="Text"
                                                            Value="Week streak" />
                                                    </DataTrigger>
                                                    <DataTrigger
                                                        TargetType="Label"
                                                        Binding="{Binding ChainCount}"
                                                        Value="0">
                                                        <Setter
                                                            Property="Text"
                                                            Value="Week streak" />
                                                    </DataTrigger>
                                                </Label.Triggers>
                                            </Label>
                                        </StackLayout>
                                        <!--row 0; col 1-->
                                        <StackLayout
                                            Grid.Row="0"
                                            IsVisible="{Binding IsLastVisible}"
                                            x:Name="StackIsLastVisible"
                                            HorizontalOptions="FillAndExpand"
                                            Grid.Column="1">
                                            <Image
                                                Source="workoutdone.png"
                                                Aspect="AspectFit"
                                                HeightRequest="32"
                                                HorizontalOptions="CenterAndExpand" />
                                            <Label
                                                Text="{Binding LevelUpMessage}"
                                                x:Name="LblLevelUpMessage"
                                                FontSize="17"
FontAttributes="Bold"
                                                Style="{StaticResource LabelStyle}"
                                                HorizontalOptions="Center"
                                                HorizontalTextAlignment="Center"
                                                TextColor="Black" />
                                            <Label
                                                Text="{Binding LevelUpText}"
                                                x:Name="LblLevelUpText"
                                                FontSize="15"
                                                LineHeight="{OnPlatform Android='1.1',iOS='1'}" 
                                                TextColor="#AA000000"
                                                HorizontalOptions="CenterAndExpand"
                                                HorizontalTextAlignment="Center" />
                                        </StackLayout>
                                        <!--row 0; col 2-->
                                        <StackLayout
                                            Grid.Row="0"
                                            HorizontalOptions="FillAndExpand"
                                            Grid.Column="2">
                                            <Image
                                                Source="flexed_biceps.png"
                                                Aspect="AspectFit"
                                                HeightRequest="32"
                                                HorizontalOptions="CenterAndExpand" />
                                            <Label
                                                Text="{Binding LbsLifted}"
                                                x:Name="LblLifted"
                                                FontSize="17"
FontAttributes="Bold"
                                                Style="{StaticResource LabelStyle}"
                                                HorizontalOptions="Center"
                                                HorizontalTextAlignment="Center"
                                                TextColor="Black" />
                                            <Label
                                                Text="{Binding LbsLiftedText}"
                                                x:Name="LblLiftedText"
                                                FontSize="15"
                                                LineHeight="{OnPlatform Android='1.1',iOS='1'}" 
                                                TextColor="#AA000000"
                                                HorizontalOptions="CenterAndExpand"
                                                HorizontalTextAlignment="Center" />
                                        </StackLayout>
                                    </Grid>

                                    <Grid
                                        VerticalOptions="FillAndExpand"
                                        HorizontalOptions="FillAndExpand"
                                        Margin="0"
                                        Padding="0,20,0,20">
                                        <Grid.RowDefinitions>
                                            <RowDefinition
                                                Height="*" />
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition
                                                Width="*" />
                                            <ColumnDefinition
                                                Width="*" />
                                            <ColumnDefinition
                                                Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <StackLayout
                                            Grid.Row="0"
                                            HorizontalOptions="FillAndExpand"
                                            Grid.Column="0">
                                            <Image
                                                Source="bodyweight.png"
                                                Aspect="AspectFit"
                                                HeightRequest="32"
                                                HorizontalOptions="CenterAndExpand" />
                                            <Label
                                                x:Name="LblBodyweight"
                                                FontSize="17"
FontAttributes="Bold"
                                                Style="{StaticResource LabelStyle}"
                                                HorizontalOptions="Center"
                                                HorizontalTextAlignment="Center"
                                                TextColor="Black" />
                                            <Label
                                                x:Name="LblBodyweightText"
                                                FontSize="15"
                                                LineHeight="{OnPlatform Android='1.1',iOS='1'}" 
                                                TextColor="#AA000000"
                                                HorizontalOptions="CenterAndExpand"
                                                HorizontalTextAlignment="Center" />
                                            <StackLayout.GestureRecognizers>
                                                <TapGestureRecognizer Tapped="EnterWeight_Clicked"/>
                                            </StackLayout.GestureRecognizers>
                                        </StackLayout>
                                        <StackLayout
                                            Grid.Row="0"
                                            HorizontalOptions="FillAndExpand"
                                            Grid.Column="1">
                                            <Image
                                                Source="restrecovery.png"
                                                Aspect="AspectFit"
                                                HeightRequest="32"
                                                HorizontalOptions="CenterAndExpand" />
                                            <Label
                                                Text="{Binding SinceTime}"
                                                FontSize="17"
FontAttributes="Bold"
                                                x:Name="LblSinceTIme"
                                                Style="{StaticResource LabelStyle}"
                                                HorizontalOptions="Center"
                                                HorizontalTextAlignment="Center"
                                                TextColor="Black" />
                                            <Label
                                                Text="{Binding LastWorkoutText}"
                                                x:Name="LblLastworkoutText"
                                                FontSize="15"
                                                LineHeight="{OnPlatform Android='1.1',iOS='1'}" 
                                                TextColor="#AA000000"
                                                HorizontalOptions="Center"
                                                HorizontalTextAlignment="Center" />
                                        </StackLayout>
                                        <StackLayout
                                            Grid.Row="0"
                                            HorizontalOptions="FillAndExpand"
                                            Grid.Column="2">

                                            <ffimageloading:CachedImage
                                                ErrorPlaceholder="backgroundblack.png"
                                                HorizontalOptions="Center"
CacheDuration="30"
CacheType="All" 
FadeAnimationEnabled="True"
                                                WidthRequest="20"
                                                HeightRequest="32"
                                                x:Name="TrainRestImage"
                                                Source="orange2.png"
                                                Aspect="AspectFit">
                                                <ffimageloading:CachedImage.Triggers>
                                                    <DataTrigger
                                                        TargetType="ffimageloading:CachedImage"
                                                        Binding="{Binding TrainRest}"
                                                        Value="Train">
                                                        <Setter
                                                            Property="Source"
                                                            Value="green.png" />
                                                    </DataTrigger>
                                                    <DataTrigger
                                                        TargetType="ffimageloading:CachedImage"
                                                        Binding="{Binding TrainRest}"
                                                        Value="Rest">
                                                        <Setter
                                                            Property="Source"
                                                            Value="orange2.png" />
                                                    </DataTrigger>
                                                </ffimageloading:CachedImage.Triggers>
                                            </ffimageloading:CachedImage>
                                            <Label
                                                Text="{Binding TrainRest}"
                                                x:Name="LblTrainRest"
                                                TextColor="{Binding StrengthTextColor}"
                                                FontSize="17"
FontAttributes="Bold"
                                                Style="{StaticResource LabelStyle}"
                                                HorizontalOptions="Center"
                                                HorizontalTextAlignment="Center" />

                                            <Label
                                                Text="{Binding TrainRestText}"
                                                x:Name="LblTrainRestText"
                                                FontSize="15"
                                                LineHeight="{OnPlatform Android='1.1',iOS='1'}" 
                                                TextColor="#AA000000"
                                                HorizontalOptions="CenterAndExpand"
                                                HorizontalTextAlignment="Center" />
                                        </StackLayout>
                                    </Grid>
                                </StackLayout>

                                <!--Buttons-->
                                <Grid
                                    HorizontalOptions="FillAndExpand"
                                    VerticalOptions="FillAndExpand"
                                    IsVisible="True"
                                    Margin="1,10,1,15">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width=".5*"/>
                                        <ColumnDefinition Width=".5*"/>
                                    </Grid.ColumnDefinitions>
                                    <!--AI Analysis Button (col 0)-->
                                    <t:DrMuscleButton
                                        x:Name="BtnAIAnalysis"
                                        Grid.Column="0"
                                        Text="AI ANALYSIS"
                                        FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                        FontAttributes="Bold"
                                        VerticalOptions="Center"
                                        HorizontalOptions="Center"
                                        Style="{StaticResource buttonLinkStyle}"
                                        TextColor="{x:Static app:AppThemeConstants.BlueColor}"
                                        Clicked="BtnAIAnalysis_Clicked"
                                        />

                                    <!--Share Stats Button (col 1)-->
                                    <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 6,6,6,6"
                            Padding="0"
                            BackgroundColor="Transparent"
                                        Grid.Column="1"
                                        
                                        Margin="0"
                                        
                                        VerticalOptions="Center"
                                        HorizontalOptions="FillAndExpand"
                                        HeightRequest="45"
                                        >
                                        <StackLayout
    HeightRequest="45"
    Padding="0"
    Margin="0"
    VerticalOptions="FillAndExpand"
    HorizontalOptions="FillAndExpand"
    Style="{StaticResource GradientStackStyleBlue}"
    >

                                        <t:DrMuscleButton
                                            x:Name="BtnStats"
                                            CornerRadius="6"
                                            VerticalOptions="Fill"
                                            HeightRequest="45"
                                            FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                            HorizontalOptions="FillAndExpand"
                                            Text="SHARE"
                                            Style="{StaticResource highEmphasisButtonStyle}"
                                            BackgroundColor="Transparent"
                                            BorderColor="Transparent"
                                            TextColor="White"
                                            Clicked="BtnStats_Clicked"/>
                                        </StackLayout>
                                    </Border>
                                </Grid>
                                <!--<t:DrMuscleButton Padding="0" Text="Got it" HorizontalOptions="End" FontSize="17" VerticalOptions="End" Margin="10,0,5,0" BackgroundColor="Transparent" TextColor="#007aff"  Clicked="BtnStatsGotit_Clicked" />-->
                                <!--<Grid  HorizontalOptions="FillAndExpand" Margin="1,10,1,1">
                                        <t:DrMuscleButton Grid.Column="0" Padding="0" HeightRequest="45" Text="GOT IT" HorizontalOptions="FillAndExpand" FontSize="13" FontAttributes="Bold" CornerRadius="6" VerticalOptions="End" Style="{StaticResource buttonStyle}" Margin="0,0,0,0" BackgroundColor="Transparent" TextColor="{x:Static app:AppThemeConstants.BlueColor}"  Clicked="BtnStatsGotit_Clicked" />
                                        </Grid>-->
                            </StackLayout>
                        </controls:CustomFrame>

                        <!--7-->
                        <!--Calendar step 2-->
                        <controls:CustomFrame
                            Margin="10,10,10,0"
                            Padding="10,10,10,10"
                            CornerRadius="12"
HasShadow="True">
                            <StackLayout>
                                <StackLayout
                                    x:Name="calendarBox2"
                                    Padding="0"
                                    BackgroundColor="Transparent">
                                </StackLayout>
                                <Grid
                                    HorizontalOptions="FillAndExpand"
                                    Margin="1,0,1,15">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width=".5*"/>
                                        <ColumnDefinition Width=".5*"/>
                                    </Grid.ColumnDefinitions>
                                    <t:DrMuscleButton
                                        CornerRadius="6"
                                        VerticalOptions="Fill"
                                        HeightRequest="45"
                                        FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                        FontAttributes="Bold"
                                        HorizontalOptions="FillAndExpand"
                                        Text="FULL HISTORY"
                                        Clicked="btnHistory_Clicked"
                                        IsVisible="true"
                                        Style="{StaticResource buttonLinkStyle}"
                                        TextColor="{x:Static app:AppThemeConstants.BlueColor}"
                                        BackgroundColor="Transparent"
                                        BorderColor="Transparent" />

                                    <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 6,6,6,6"
                            Padding="0"
                            BackgroundColor="Transparent"
                                        Margin="0"
                                        
                                        Grid.Column="1"
                                        HorizontalOptions="FillAndExpand"
                                        VerticalOptions="Center"
                                        HeightRequest="45"
                                        >
                                        <StackLayout
    HeightRequest="45"
    Padding="0"
    Margin="0"
    VerticalOptions="FillAndExpand"
    HorizontalOptions="FillAndExpand"
    Style="{StaticResource GradientStackStyleBlue}"
    >

                                        <t:DrMuscleButton
                                            Text="PROGRESS CHARTS"
                                            Clicked="btnChart_Clicked"
                                            FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                            FontAttributes="Bold"
                                            Grid.Column="0"
                                            HorizontalOptions="Center"
                                            VerticalOptions="Center"
                                            BackgroundColor="Transparent"
                                            BorderColor="Transparent"
                                            TextColor="White"
                                            Style="{StaticResource buttonLinkStyle}"
                                            Margin="0,-3,0,0"
                                            Padding="0"/>
                                        </StackLayout>
                                    </Border>
                                </Grid>
                            </StackLayout>
                        </controls:CustomFrame>

                        <!-- Upgrade plan box if user not purchase any plan (monthly or yearly) -->
                        <StackLayout
                            x:Name="UpgradePlanBox"
                            Margin="0"
                            Padding="0"
                            IsVisible="False">
                            <controls:CustomFrame
                                Margin="10,10,10,0"
                                Padding="10"
                                CornerRadius="12"
HasShadow="True">
                                <StackLayout>
                                    <!--Logo and text-->
                                    <Grid
                                        Margin="0,10,0,0">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="Auto" />
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="40" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <Image
                                            Source="ic_heart.png"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Center"
                                            WidthRequest="27"
                                            HeightRequest="27" />
                                        <Label
                                            Grid.Column="1"
                                            Grid.Row="0"
                                            VerticalOptions="Center"
                                            VerticalTextAlignment="Center"
                                            Margin="0,0,0,9"
                                            Text="Get 4 months free"
                                            TextColor="Black"
                                            FontAttributes="Bold"
                                            FontSize="19" />
                                        <Label
                                            x:Name="LabelUpgradePlanOffer"
                                            Grid.Column="1"
                                            Grid.Row="1"
                                            TextColor="#AA000000"
                                            FontSize="17"
                                            LineHeight="{OnPlatform Android='1.3',iOS='1.2'}"/>
                                    </Grid>

                                    <!--Buttons-->
                                    <Grid
                                        HorizontalOptions="FillAndExpand"
                                        Margin="1,20,1,15">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!--Monthly Plan Purchase Button-->
                                        <t:DrMuscleButton
                                            x:Name="BtnMonthlyPlanPurchase"
                                            Grid.Column="0"
                                            Text="MONTHLY PLAN"
                                            FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                            FontAttributes="Bold"
                                            HorizontalOptions="Center"
                                            VerticalOptions="Center"
                                            Style="{StaticResource buttonLinkStyle}"
                                            TextColor="{x:Static app:AppThemeConstants.BlueColor}"
                                            Clicked="BtnMonthlyPlanPurchase_Clicked"/>

                                        <!--Yearly Plan Purchase Button-->
                                        <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 6,6,6,6"
                            Padding="0"
                            BackgroundColor="Transparent"
                                            x:Name="PancakeAnnualPlanPurchase"
                                            Grid.Column="1"
                                            
                                            Margin="0"
                                            
                                            VerticalOptions="Center"
                                            IsVisible="true"
                                            HorizontalOptions="FillAndExpand"
                                            HeightRequest="45">
                                            <StackLayout
    HeightRequest="45"
    Padding="0"
    Margin="0"
    VerticalOptions="FillAndExpand"
    HorizontalOptions="FillAndExpand"
    Style="{StaticResource GradientStackStyleBlue}"
    >

                                            <t:DrMuscleButton
                                                x:Name="BtnAnnualPlanPurchase"
                                                VerticalOptions="Center"
                                                HeightRequest="45"
                                                FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                                CornerRadius="6"
                                                HorizontalOptions="FillAndExpand"
                                                IsVisible="true"
                                                Style="{StaticResource highEmphasisButtonStyle}"
                                                BackgroundColor="Transparent"
                                                BorderColor="Transparent"
                                                TextColor="White"
                                                TextTransform="Uppercase"
                                                Clicked="BtnAnnualPlanPurchase_Clicked"/>
                                            </StackLayout>
                                        </Border>
                                    </Grid>
                                </StackLayout>
                            </controls:CustomFrame>
                        </StackLayout>

                        <!--8-->
                        <!-- Training parter card to show for monthly and yearly user -->
                        <StackLayout
                            x:Name="TrainingCard"
                            Margin="0"
                            Padding="0"
                            IsVisible="False">
                            <controls:CustomFrame
                                Grid.Row="0"
                                Grid.Column="0"
                                Margin="10,10,10,0"
                                Padding="10"
                                CornerRadius="12"
HasShadow="True">
                                <StackLayout>
                                    <!--Logo and text-->
                                    <Grid
                                        Margin="0,10,0,0">
                                        <Grid.RowDefinitions>
                                            <RowDefinition
                                            Height="Auto" />
                                            <RowDefinition
                                            Height="Auto" />
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition
                                            Width="40" />
                                            <ColumnDefinition
                                            Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <Image
                                            Source="ic_handshake.png"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Center"
                                            WidthRequest="27"
                                            HeightRequest="27" />
                                        <Label
                                            Grid.Column="1"
                                            Grid.Row="0"
                                            Text="Training partner?"
                                            VerticalOptions="Center"
                                            VerticalTextAlignment="Center"
                                            Margin="0,0,0,9"
                                            TextColor="Black"
                                            FontAttributes="Bold"
                                            FontSize="19" />
                                        <Label
                                            Grid.Column="1"
                                            Grid.Row="1"
                                            TextColor="#AA000000"
                                            FontSize="17"
                                            LineHeight="{OnPlatform Android='1.3',iOS='1.2'}"
                                            Text="Add a partner account—50% off."/>
                                    </Grid>

                                    <!--Buttons-->
                                    <Grid
                                        HorizontalOptions="FillAndExpand"
                                        Margin="1,20,1,15">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>


                                        <!--Add Account Button-->
                                        <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 6,6,6,6"
                            Padding="0"
                            BackgroundColor="Transparent"
                                            x:Name="PancakeAddAccount"
                                            Grid.Column="1"
                                            
                                            Margin="0"
                                            
                                            VerticalOptions="Center"
                                            HorizontalOptions="FillAndExpand"
                                            HeightRequest="45"
                                            >
                                            <StackLayout
    HeightRequest="45"
    Padding="0"
    Margin="0"
    VerticalOptions="FillAndExpand"
    HorizontalOptions="FillAndExpand"
    Style="{StaticResource GradientStackStyleBlue}"
    >

                                            <t:DrMuscleButton
                                                x:Name="BtnAddAccount"
                                                VerticalOptions="Center"
                                                HeightRequest="45"
                                                FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                                CornerRadius="6"
                                                HorizontalOptions="FillAndExpand"
                                                Style="{StaticResource highEmphasisButtonStyle}"
                                                BackgroundColor="Transparent"
                                                BorderColor="Transparent"
                                                TextColor="White"
                                                TextTransform="Uppercase"
                                                Text="ADD ACCOUNT"
                                                Clicked="BtnAddAccount_Clicked"/>
                                            </StackLayout>
                                        </Border>
                                    </Grid>
                                </StackLayout>
                            </controls:CustomFrame>
                        </StackLayout>

                        <!-- Meal Plan Card while user is not purchased any meal plan -->
                        <StackLayout
                            x:Name="MealPlanCard"
                            Padding="0"
                            Margin="0"
                            IsVisible="False">
                            <controls:CustomFrame
                                Grid.Row="0"
                                Grid.Column="0"
                                Margin="10,10,10,0"
                                Padding="10"
                                CornerRadius="12"
HasShadow="True">
                                <StackLayout>
                                    <!--Logo and text-->
                                    <Grid
                                        Margin="0,10,0,0">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="Auto" />
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition
                                                Width="40" />
                                            <ColumnDefinition
                                                Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <Image
                                            Source="ic_meal_plan.png"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Center"
                                            WidthRequest="27"
                                            HeightRequest="27" />
                                        <Label
                                            Grid.Column="1"
                                            Grid.Row="0"
                                            Text="Get in shape faster"
                                            VerticalOptions="Center"
                                            VerticalTextAlignment="Center"
                                            Margin="0,0,0,9"
                                            TextColor="Black"
                                            FontAttributes="Bold"
                                            FontSize="19" />
                                        <Label
                                            Grid.Column="1"
                                            Grid.Row="1"
                                            TextColor="#AA000000"
                                            FontSize="17"
                                            LineHeight="{OnPlatform Android='1.3',iOS='1.2'}"
                                            Text="Try a custom meal plan."/>
                                    </Grid>

                                    <!--Buttons-->
                                    <Grid
                                        HorizontalOptions="FillAndExpand"
                                        Margin="1,20,1,15">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>


                                        <!--Add Account Button-->
                                        <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 6,6,6,6"
                            Padding="0"
                            BackgroundColor="Transparent"
                                            Grid.Column="1"
                                            
                                            Margin="0"
                                            
                                            VerticalOptions="Center"
                                            HorizontalOptions="FillAndExpand"
                                            HeightRequest="45"
                                            >
                                            <StackLayout
    HeightRequest="45"
    Padding="0"
    Margin="0"
    VerticalOptions="FillAndExpand"
    HorizontalOptions="FillAndExpand"
    Style="{StaticResource GradientStackStyleBlue}"
    >

                                            <t:DrMuscleButton
                                                VerticalOptions="Center"
                                                HeightRequest="45"
                                                FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                                CornerRadius="6"
                                                HorizontalOptions="FillAndExpand"
                                                Style="{StaticResource highEmphasisButtonStyle}"
                                                BackgroundColor="Transparent"
                                                BorderColor="Transparent"
                                                TextColor="White"
                                                TextTransform="Uppercase"
                                                Text="GET MEAL PLAN"
                                                Clicked="GetMealPlan_Clicked"/>
                                            </StackLayout>
                                        </Border>
                                    </Grid>
                                </StackLayout>
                            </controls:CustomFrame>
                        </StackLayout>

                        <!--9-->
                        <!--Start workout box-->
                        <controls:CustomFrame
                            x:Name="StartworkoutBox"
                            Margin="10,10,10,0"
                            Padding="0,0,0,20"
                            CornerRadius="12"
                            HasShadow="True"
                            IsClippedToBounds="True">
                            <StackLayout>
                                <Frame
                                    BorderColor="Transparent"
                                    Margin="{OnPlatform Android='-3', iOS='-2'}"
                                    Padding="0"
                                    CornerRadius="{OnPlatform Android='18', iOS='12'}"
                                    HasShadow="False"
                                    IsClippedToBounds="True">
                                    <ffimageloading:CachedImage
                                        ErrorPlaceholder="backgroundblack.png"
                                        HeightRequest="{OnPlatform iOS='270'}"
CacheDuration="30"
CacheType="All" 
FadeAnimationEnabled="True"
                                        Source="workoutbackground.png"
                                        x:Name="ImgWorkout2"
                                        Aspect="AspectFill" />
                                </Frame>
                                <Label
                                    x:Name="workoutNameLabel2"
                                    Margin="20.5,11,20.5,0"
                                    Text="Up next"
                                    TextColor="Black"
                                    FontAttributes="Bold"
                                    FontSize="19" />


                                <Grid
                                    HorizontalOptions="FillAndExpand"
                                    Margin="1,5,1,1"
                                    IsVisible="false">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width=".5*"/>
                                        <ColumnDefinition Width=".5*"/>
                                    </Grid.ColumnDefinitions>
                                    <t:DrMuscleButton
                                        Text="LEARN MORE"
                                        FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                        FontAttributes="Bold"
                                        Grid.Column="0"
                                        Clicked="BtnLearnMore_Clicked"
                                        HorizontalOptions="Center"
                                        VerticalOptions="Center"
                                        Style="{StaticResource buttonLinkStyle}"
                                        TextColor="{x:Static app:AppThemeConstants.BlueColor}" />
                                    <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 6,6,6,6"
                            Padding="0"
                            BackgroundColor="Transparent"
                                        Margin="0"
                                        
                                        VerticalOptions="Center"
                                        Grid.Column="1"
                                        HorizontalOptions="FillAndExpand"
                                        
                                        HeightRequest="45">
                                        <StackLayout
    HeightRequest="45"
    Padding="0"
    Margin="0"
    VerticalOptions="FillAndExpand"
    HorizontalOptions="FillAndExpand"
    Style="{StaticResource GradientStackStyleBlue}"
    >

                                        <t:DrMuscleButton
                                            VerticalOptions="EndAndExpand"
                                            HeightRequest="45"
                                            FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                            CornerRadius="6"
                                            x:Name="BtnCardStartWorkout"
                                            HorizontalOptions="FillAndExpand"
                                            Text=""
                                            Clicked="BtnStartTodayWorkout_Clicked"
                                            IsVisible="true"
                                            Style="{StaticResource highEmphasisButtonStyle}"
                                            BackgroundColor="Transparent"
                                            BorderColor="Transparent"
                                            TextColor="White">
                                        </t:DrMuscleButton>
                                        </StackLayout>
                                    </Border>
                                </Grid>

                                <Label
                                    x:Name="LblWorkoutDuration2"
                                    Text="x exercises · xx minutes"
                                    IsVisible="false"
                                    Margin="20.5,9,20.5,10"
                                    FontSize="17"
                                    LineHeight="{OnPlatform Android='1.3',iOS='1.2'}" 
                                    TextColor="#AA000000" />
                                <Grid
                                    HorizontalOptions="FillAndExpand"
                                    Margin="11,10,11,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width=".5*"/>
                                        <ColumnDefinition Width=".5*"/>
                                    </Grid.ColumnDefinitions>
                                    <t:DrMuscleButton
                                        Text="EXERCISES"
                                        FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                        FontAttributes="Bold"
                                        Grid.Column="0"
                                        HorizontalOptions="Center"
                                        Clicked="BtnExercises_Clicked"
                                        VerticalOptions="Center"
                                        Style="{StaticResource buttonLinkStyle}"
                                        TextColor="{x:Static app:AppThemeConstants.BlueColor}" />
                                    <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 6,6,6,6"
                            Padding="0"
                            BackgroundColor="Transparent"
                                        Margin="0"
                                        
                                        Grid.Column="1"
                                        HorizontalOptions="FillAndExpand"
                                        VerticalOptions="Center"
                                        
                                        HeightRequest="45">
                                        <StackLayout
    HeightRequest="45"
    Padding="0"
    Margin="0"
    VerticalOptions="FillAndExpand"
    HorizontalOptions="FillAndExpand"
    Style="{StaticResource GradientStackStyleBlue}"
    >

                                        <t:DrMuscleButton
                                                CornerRadius="6"
                                                VerticalOptions="Fill"
                                                HeightRequest="45"
                                                FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                                HorizontalOptions="FillAndExpand"
                                                Text="WORKOUTS"
                                                x:Name="btnWorkouts"
                                                Clicked="BtnWorkouts_Clicked"
                                                IsVisible="true"
                                            
                                                Style="{StaticResource highEmphasisButtonStyle}"
                                                BackgroundColor="Transparent"
                                                BorderColor="Transparent"
                                                TextColor="White" />
                                        </StackLayout>
                                    </Border>
                                </Grid>
                            </StackLayout>
                        </controls:CustomFrame>
                    </StackLayout>
                    <StackLayout
                        x:Name="StackSteps1"
                        IsVisible="false">
                        <!--Welcome card-->
                        <controls:CustomFrame
                            x:Name="WelcomeBox"
                            Margin="10,13,10,0"
                            Padding="10,10,10,10"
                            CornerRadius="12"
HasShadow="True">

                            <StackLayout>
                                <Grid
                                    Margin="0,10,0,0">
                                    <Grid.RowDefinitions>
                                        <RowDefinition
                                            Height="*" />
                                        <RowDefinition
                                            Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition
                                            Width="40" />
                                        <ColumnDefinition
                                            Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <Image
                                        Grid.Column="0"
                                        VerticalOptions="Start"
                                        Margin="-5,-9,0,0"
                                        Source="logo2.png"
                                        WidthRequest="40"
                                        HeightRequest="40" />
                                    <StackLayout
                                        Grid.Column="1"
                                        Grid.Row="0"
                                        Grid.RowSpan="2">
                                        <Label
                                            Text="WELCOME TO YOUR HOME"
                                            TextColor="Black"
                                            FontAttributes="Bold"
                                            FontSize="16"
                                            Margin="0,0,0,9" />
                                        <Label
                                            x:Name="LblGoal"
                                            FontSize="17"
                                            LineHeight="{OnPlatform Android='1.3',iOS='1.2'}" 
                                            TextColor="#AA000000"
                                            Text="This is where you'll find your workouts, key stats, and everything you need to build muscle" />
                                    </StackLayout>
                                </Grid>

                                <Grid
                                    HorizontalOptions="FillAndExpand"
                                    Margin="1,20,1,15">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width=".5*"/>
                                        <ColumnDefinition Width=".5*"/>
                                    </Grid.ColumnDefinitions>
                                    <t:DrMuscleButton
                                        Text="LEARN MORE"
                                        FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                        FontAttributes="Bold"
                                        Grid.Column="0"
                                        Clicked="BtnLearnMore_Clicked"
                                        HorizontalOptions="Center"
                                        VerticalOptions="Center"
                                        Style="{StaticResource buttonLinkStyle}"
                                        TextColor="{x:Static app:AppThemeConstants.BlueColor}" />

                                    <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 6,6,6,6"
                            Padding="0"
                            BackgroundColor="Transparent"
                                        Margin="0"
                                        
                                        Grid.Column="1"
                                        HorizontalOptions="FillAndExpand"
                                        VerticalOptions="Center"
                                        HeightRequest="45">
                                        <StackLayout
    HeightRequest="45"
    Padding="0"
    Margin="0"
    VerticalOptions="FillAndExpand"
    HorizontalOptions="FillAndExpand"
    Style="{StaticResource GradientStackStyleBlue}"
    >

                                        <t:DrMuscleButton
                                            CornerRadius="6"
                                            VerticalOptions="Fill"
                                            HeightRequest="45"
                                            FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                            HorizontalOptions="FillAndExpand"
                                            Text="GOT IT"
                                            Clicked="DrMuscleButton_Clicked"
                                            IsVisible="true"
                                            Style="{StaticResource highEmphasisButtonStyle}"
                                            BackgroundColor="Transparent"
                                            BorderColor="Transparent"
                                            TextColor="White" />
                                        </StackLayout>
                                    </Border>

                                </Grid>
                            </StackLayout>
                        </controls:CustomFrame>
                        <!--First goal box-->
                        <controls:CustomFrame
                            Padding="0,0,0,10"
                            x:Name="GoalBox"
                            Margin="10,10,10,0"
                            CornerRadius="12"
HasShadow="True"
                            IsClippedToBounds="True">

                            <StackLayout>
                                <Frame
                                    Margin="{OnPlatform Android='-3', iOS='-2'}"
Padding="0"
CornerRadius="{OnPlatform Android='18', iOS='12'}"
                                    IsClippedToBounds="True"
                                    HasShadow="False">
                                    <ffimageloading:CachedImage
                                        ErrorPlaceholder="backgroundblack.png"
CacheDuration="30"
CacheType="All" 
FadeAnimationEnabled="True"
                                        HeightRequest="{OnPlatform iOS='270'}"
                                        Source="settingsbackground.png"
                                        Aspect="AspectFill" />
                                </Frame>
                                <Label
                                    x:Name="LblLearnGoalTitle"
                                    Margin="20.5,11,20.5,0"
                                    Text="Muscle guide"
                                    TextColor="Black"
                                    FontAttributes="Bold"
                                    FontSize="19" />
                                <Label
                                    x:Name="LblLearnGoal"
                                    Margin="20.5,9,20.5,0"
                                    FontSize="17"
                                    LineHeight="{OnPlatform Android='1.3',iOS='1.2'}" 
                                    TextColor="#AA000000" />

                                <Grid
                                    HorizontalOptions="FillAndExpand"
                                    Margin="11,20,11,17"
                                    ColumnSpacing="10">

                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width=".5*"/>
                                        <ColumnDefinition Width=".5*"/>
                                    </Grid.ColumnDefinitions>
                                    <t:DrMuscleButton
                                        Grid.Column="0"
                                        Padding="0"
                                        Text="LATER"
                                        HeightRequest="45"
                                        HorizontalOptions="FillAndExpand"
                                        FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                        CornerRadius="6"
                                        FontAttributes="Bold"
                                        VerticalOptions="End"
                                        Style="{StaticResource buttonLinkStyle}"
                                        Margin="0,0,5,0"
                                        BackgroundColor="Transparent"
                                        TextColor="{x:Static app:AppThemeConstants.BlueColor}"
                                        Clicked="Later_Clicked"/>
                                    <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 6,6,6,6"
                            Padding="0"
                            BackgroundColor="Transparent"
                                        Margin="0"
                                        
                                        Grid.Column="1"
                                        HorizontalOptions="FillAndExpand"
                                        
                                        HeightRequest="45">
                                        <StackLayout
    HeightRequest="45"
    Padding="0"
    Margin="0"
    VerticalOptions="FillAndExpand"
    HorizontalOptions="FillAndExpand"
    Style="{StaticResource GradientStackStyleBlue}"
    >

                                        <t:DrMuscleButton
                                            CornerRadius="6"
                                            VerticalOptions="Fill"
                                            HeightRequest="45"
                                            FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                            HorizontalOptions="FillAndExpand"
                                            Text="READ NOW"
                                            Clicked="Learn_Clicked"
                                            IsVisible="true"
                                            Style="{StaticResource highEmphasisButtonStyle}"
                                            BackgroundColor="Transparent"
                                            BorderColor="Transparent"
                                            TextColor="White" />
                                        </StackLayout>
                                    </Border>

                                </Grid>
                            </StackLayout>
                        </controls:CustomFrame>

                        <!--WeightProgress1 card-->
                        <controls:CustomFrame
                            IsVisible="false"
                            x:Name="WeightProgress1"
                            Margin="10,10,10,0"
                            Padding="10,10,10,10"
                            CornerRadius="12"
HasShadow="True">

                            <StackLayout>
                                <Grid
                                    Margin="0,17,0,0"
                                    RowSpacing="0">
                                    <Grid.RowDefinitions>
                                        <RowDefinition
                                            Height="*" />
                                        <RowDefinition
                                            Height="Auto" />
                                        <RowDefinition
                                            Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition
                                            Width="40" />
                                        <ColumnDefinition
                                            Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <Image
                                        Grid.Column="0"
                                        VerticalOptions="Start"
                                        Margin="0,-4,0,0"
                                        Source="bodyweight.png"
                                        WidthRequest="27"
                                        HorizontalOptions="Center"
                                        HeightRequest="27" />
                                    <StackLayout
                                        Grid.Column="1"
                                        Grid.Row="0">
                                        <Label
                                            x:Name="LblWeightToGo1"
                                            Text="Weight progress"
                                            TextColor="Black"
                                            FontAttributes="Bold"
                                            FontSize="19"
                                            Margin="0,-4,0,9" />
                                        <!--<Label
                                                x:Name="LblWeightTipText1"
                                                Text="-"
                                                TextColor="#AA000000"
                                                FontSize="{x:Static app:AppThemeConstants.DescriptionFontSize}" />-->

                                    </StackLayout>
                                    <t:DrMuscleButton
                                            Grid.Row="1"
                                            Text="LEARN MORE"
                                            FontSize="13"
                                            Grid.ColumnSpan="2"
                                            FontAttributes="Bold"
                                            Grid.Column="0"
                                            HorizontalOptions="Center"
                                        Clicked="BtnLearnMore_Clicked"
                                            VerticalOptions="Center"
                                            Style="{StaticResource buttonLinkStyle}"
                                            TextColor="{x:Static app:AppThemeConstants.BlueColor}" />
                                    <Grid
                                        Grid.Row="1"
                                        Grid.Column="0"
                                        Grid.ColumnSpan="2"
                                        Margin="0,0"
                                        RowSpacing="0"
                                        BackgroundColor="White"
                                        ColumnSpacing="10">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="60" />
                                            <RowDefinition
                                                Height="*" />
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition
                                                Width="*" />
                                            <ColumnDefinition
                                                Width="*" />
                                            <ColumnDefinition
                                                Width="*" />
                                        </Grid.ColumnDefinitions>

                                        <!--Start weight-->
                                        <Frame
                                            CornerRadius="6"
                                            Grid.Row="0"
                                            Grid.Column="0"
                                            HeightRequest="45"
                                            BackgroundColor="White"
                                            HasShadow="False"
                                            Padding="0">
                                            <StackLayout
                                                VerticalOptions="Center"
                                                HorizontalOptions="Center">


                                                <Label
                                                    x:Name="LblStartText1"
                                                    FontSize="17"
FontAttributes="Bold"
                                                    Style="{StaticResource LabelStyle}"
                                                    HorizontalOptions="Center"
                                                    HorizontalTextAlignment="Center"
                                                    TextColor="Black" />
                                                <Label
                                                    x:Name="LblStartWeight1"
                                                    Text="Start weight"
                                                    LineHeight="{OnPlatform Android='1.1',iOS='1'}" 
                                                    TextColor="#AA000000"
                                                    FontSize="15"
                                                    HorizontalOptions="CenterAndExpand"
                                                    HorizontalTextAlignment="Center" />
                                            </StackLayout>

                                        </Frame>


                                        <!--Current weight-->
                                        <Frame
                                            CornerRadius="6"
                                            Grid.Row="0"
                                            Grid.Column="1"
                                            HeightRequest="45"
                                            BackgroundColor="White"
                                            HasShadow="False"
                                            Padding="0">
                                            <StackLayout
                                                VerticalOptions="Center"
                                                HorizontalOptions="Center">


                                                <Label
                                                    x:Name="LblCurrentText1"
                                                    FontSize="17"
FontAttributes="Bold"
                                                    Style="{StaticResource LabelStyle}"
                                                    HorizontalOptions="Center"
                                                    HorizontalTextAlignment="Center"
                                                    TextColor="Black" />
                                                <Label
                                                    x:Name="LblCurrentWeight1"
                                                    Text="Current weight"
                                                    LineHeight="{OnPlatform Android='1.1',iOS='1.1'}" 
                                                    TextColor="#AA000000"
                                                    FontSize="15"
                                                    HorizontalOptions="CenterAndExpand"
                                                    HorizontalTextAlignment="Center" />
                                            </StackLayout>
                                        </Frame>


                                        <!--Goal weight-->
                                        <Frame
                                            CornerRadius="6"
                                            Grid.Row="0"
                                            Grid.Column="2"
                                            HeightRequest="75"
                                            HasShadow="False"
                                            BackgroundColor="White"
                                            Padding="0">
                                            <StackLayout
                                                VerticalOptions="Center"
                                                HorizontalOptions="Center">
                                                <Label
                                                    x:Name="LblGoalText1"
                                                    FontSize="17"
FontAttributes="Bold"
                                                    Style="{StaticResource LabelStyle}"
                                                    HorizontalOptions="Center"
                                                    HorizontalTextAlignment="Center"
                                                    TextColor="Black" />
                                                <Label
                                                    x:Name="LblGoalWeight1"
                                                    Text="Goal weight"
                                                    LineHeight="{OnPlatform Android='1.1',iOS='1.1'}" 
                                                    TextColor="#AA000000"
                                                    FontSize="15"
                                                    HorizontalOptions="CenterAndExpand"
                                                    HorizontalTextAlignment="Center" />
                                            </StackLayout>
                                        </Frame>
                                        <!--Tracker-->
                                        <StackLayout
                                            Grid.Row="0"
                                            IsVisible="false"
                                            Grid.Column="0"
                                            Grid.ColumnSpan="3">
                                            <Label
                                                HorizontalOptions="CenterAndExpand"
                                                x:Name="LbltrackerText1"
                                                FontSize="17" />
                                            <Frame
                                                x:Name="FrmTracker1"
                                                HasShadow="False"
                                                Margin="20,0"
                                                Padding="0"
                                                HeightRequest="10"
                                                CornerRadius="5" />
                                        </StackLayout>
                                    </Grid>
                                </Grid>

                                <Grid
                                    HorizontalOptions="FillAndExpand"
                                    Margin="1,10,1,15">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width=".5*"/>
                                        <ColumnDefinition Width=".5*"/>
                                    </Grid.ColumnDefinitions>
                                    <t:DrMuscleButton
                                        Text="UPDATE GOAL"
                                        FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                        FontAttributes="Bold"
                                        Grid.Column="0"
                                        HorizontalOptions="Center"
                                        Clicked="btnUpdateGoal_Clicked"
                                        VerticalOptions="Center"
                                        Style="{StaticResource buttonLinkStyle}"
                                        TextColor="{x:Static app:AppThemeConstants.BlueColor}" />

                                    <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 6,6,6,6"
                            Padding="0"
                            BackgroundColor="Transparent"
                                        Margin="0"
                                        
                                        Grid.Column="1"
                                        HorizontalOptions="FillAndExpand"
                                        VerticalOptions="Center"
                                        
                                        HeightRequest="45">
                                        <StackLayout
    HeightRequest="45"
    Padding="0"
    Margin="0"
    VerticalOptions="FillAndExpand"
    HorizontalOptions="FillAndExpand"
    Style="{StaticResource GradientStackStyleBlue}"
    >

                                        <t:DrMuscleButton
                                            CornerRadius="6"
                                            VerticalOptions="Fill"
                                            HeightRequest="45"
                                            FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                            HorizontalOptions="FillAndExpand"
                                            Text="LOG WEIGHT"
                                            Clicked="EnterWeight_Clicked"
                                            IsVisible="true"
                                            Style="{StaticResource highEmphasisButtonStyle}"
                                            BackgroundColor="Transparent"
                                            BorderColor="Transparent"
                                            TextColor="White" />
                                        </StackLayout>
                                    </Border>

                                </Grid>
                            </StackLayout>
                        </controls:CustomFrame>

                        <!--Weight Coaching card-->
                        <controls:CustomFrame
                            x:Name="WeightCoachingCard1"
                            Margin="10,10,10,0"
                            Padding="10,10,10,10"
                            CornerRadius="12"
                            IsVisible="true"
HasShadow="True">

                            <StackLayout>
                                <Grid
                                    Margin="0,10,0,15">
                                    <Grid.RowDefinitions>
                                        <RowDefinition
                                            Height="*" />
                                        <RowDefinition
                                            Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition
                                            Width="40" />
                                        <ColumnDefinition
                                            Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <Image
                                        Grid.Column="0"
                                        VerticalOptions="Start"
                                        HorizontalOptions="Center"
                                        Margin="0,-8,0,0"
                                        Source="lamp.png"
                                        WidthRequest="40"
                                        HeightRequest="40" />
                                    <StackLayout
                                        Grid.Column="1"
                                        Grid.Row="0"
                                        Grid.RowSpan="2">
                                        <Label
                                            x:Name="LblWeightTip1"
                                            Text=""
                                            TextColor="Black"
                                            FontAttributes="Bold"
                                            FontSize="19"
                                            Margin="0,0,0,9" />
                                        <Label
                                            x:Name="LblWeightTipText1"
                                            Text="-"
                                            FontSize="17"
                                            LineHeight="{OnPlatform Android='1.3',iOS='1.2'}" 
                                            TextColor="#AA000000" />
                                    </StackLayout>

                                </Grid>

                                <Grid
                                    HorizontalOptions="FillAndExpand"
                                    Margin="1,10,1,15">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width=".5*"/>
                                        <ColumnDefinition Width=".5*"/>
                                    </Grid.ColumnDefinitions>
                                    <t:DrMuscleButton
                                        Text="HISTORY"
                                        FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                        FontAttributes="Bold"
                                        Grid.Column="0"
                                        Clicked="BtnWeightHistory_Clicked"
                                        HorizontalOptions="Center"
                                        VerticalOptions="Center"
                                        Style="{StaticResource buttonLinkStyle}"
                                        TextColor="{x:Static app:AppThemeConstants.BlueColor}" />

                                    <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 6,6,6,6"
                            Padding="0"
                            BackgroundColor="Transparent"
                                        Margin="0"
                                        
                                        Grid.Column="1"
                                        HorizontalOptions="FillAndExpand"
                                        VerticalOptions="Center"
                                        
                                        HeightRequest="45">
                                        <StackLayout
    HeightRequest="45"
    Padding="0"
    Margin="0"
    VerticalOptions="FillAndExpand"
    HorizontalOptions="FillAndExpand"
    Style="{StaticResource GradientStackStyleBlue}"
    >

                                        <Grid>
                                            <t:DrMuscleButton
                                                CornerRadius="6"
                                                VerticalOptions="Fill"
                                                HeightRequest="45"
                                                FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                                HorizontalOptions="FillAndExpand"
                                                Text="UPDATE MEAL PLAN"
                                                IsVisible="true"
                                                x:Name="BtnUpdateMealPlan3"
                                                Style="{StaticResource highEmphasisButtonStyle}"
                                                BackgroundColor="Transparent"
                                                BorderColor="Transparent"
                                                TextColor="White" />
                                                <t:DrMuscleButton
                                                CornerRadius="6"
                                                VerticalOptions="Fill"
                                                HeightRequest="45"
                                                FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                                x:Name="btnLogWeight3"
                                                HorizontalOptions="FillAndExpand"
                                                Text="LOG WEIGHT"
                                                IsVisible="false"
                                                Style="{StaticResource highEmphasisButtonStyle}"
                                                BackgroundColor="Transparent"
                                                BorderColor="Transparent"
                                                TextColor="White" />
                                        </Grid>
                                        </StackLayout>
                                    </Border>

                                </Grid>
                            </StackLayout>
                        </controls:CustomFrame>
                        <!--CaloriesAdjustment card-->
                        <controls:CustomFrame
x:Name="CaloriesAdjustmentCard1"
Margin="10,10,10,0"
Padding="10,10,10,10"
CornerRadius="12"
IsVisible="true"
HasShadow="True">
                            <StackLayout>
                                <Grid
        Margin="0,10,0,15">
                                    <Grid.RowDefinitions>
                                        <RowDefinition
                Height="*" />
                                        <RowDefinition
                Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition
                Width="40" />
                                        <ColumnDefinition
                Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <Image
            Grid.Column="0"
            VerticalOptions="Start"
            Margin="0,-2,0,0"
                                        Source="bodyweight.png"
                                        WidthRequest="27"
                                        HorizontalOptions="Center"
                                        HeightRequest="27" />
                                    <StackLayout
            Grid.Column="1"
            Grid.Row="0"
            Grid.RowSpan="2">
                                        <Label
                x:Name="LblCaloriesAdjustmentTip1"
                Text="x-day weight trend: x.xx lbs"
                TextColor="Black"
                FontAttributes="Bold"
                FontSize="19"
                Margin="0,0,0,9" />
                                        <Label
                x:Name="LblCaloriesAdjustmentTipText1"
                Text="That's more weight loss than expected (x.xxx lbs) for someone with 8 weeks on record like you. Possible muscle loss. Calories adjusted: xxxx ⮕ xxxx."
                FontSize="17"
                LineHeight="{OnPlatform Android='1.3',iOS='1.2'}" 
                TextColor="#AA000000" />
                                    </StackLayout>
                                </Grid>
                                <Grid
        HorizontalOptions="FillAndExpand"
        Margin="1,10,1,15">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width=".5*"/>
                                        <ColumnDefinition Width=".5*"/>
                                    </Grid.ColumnDefinitions>
                                    <t:DrMuscleButton
            Text="LOG WEIGHT"
            FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
            FontAttributes="Bold"
            Grid.Column="0"
            HorizontalOptions="Center"
            Clicked="EnterWeight_Clicked"
            VerticalOptions="Center"
            Style="{StaticResource buttonLinkStyle}"
            TextColor="{x:Static app:AppThemeConstants.BlueColor}" />
                                    <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 6,6,6,6"
                            Padding="0"
                            BackgroundColor="Transparent"
            Margin="0"
            
            Grid.Column="1"
            HorizontalOptions="FillAndExpand"
            VerticalOptions="Center"
            
            HeightRequest="45">

                                        <StackLayout
    HeightRequest="45"
    Padding="0"
    Margin="0"
    VerticalOptions="FillAndExpand"
    HorizontalOptions="FillAndExpand"
    Style="{StaticResource GradientStackStyleBlue}"
    >

                                        <t:DrMuscleButton
                                                CornerRadius="6"
                                                VerticalOptions="Fill"
                                                HeightRequest="45"
                                                FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                                HorizontalOptions="FillAndExpand"
                                                Text=""
                                                x:Name="MealPlanInsideCaloriesAdjustment"
                                                Clicked="GetMealPlan_Clicked"
                                                IsVisible="true"
                                                Style="{StaticResource highEmphasisButtonStyle}"
                                                BackgroundColor="Transparent"
                                                BorderColor="Transparent"
                                                TextColor="White" />
                                        </StackLayout>
                                    </Border>
                                </Grid>
                            </StackLayout>
                        </controls:CustomFrame>

                        <!--Target Intake card-->
                        <controls:CustomFrame
                            x:Name="TargetIntakeBox"
                            Margin="10,10,10,0"
                            Padding="10,10,10,10"
                            CornerRadius="12"
HasShadow="True">
                            <StackLayout>
                                <Grid
                                    RowSpacing="0"
                                    Margin="0,17,0,0">
                                    <Grid.RowDefinitions>
                                        <RowDefinition
                                            Height="Auto" />
                                        <RowDefinition
                                            Height="Auto" />

                                        <RowDefinition
                                            Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition
                                            Width="40" />
                                        <ColumnDefinition
                                            Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <Image
                                        Grid.Column="0"
                                        VerticalOptions="Start"
                                        HorizontalOptions="Center"
                                        Margin="{OnPlatform Android='0,-8,0,0', iOS='0,-10,0,0'}"
                                        Source="applefruite.png"
                                        WidthRequest="27"
                                        HeightRequest="27" />

                                    <StackLayout
                                        Grid.Column="1"
                                        Grid.Row="0">
                                        <Label
                                            x:Name="LblTargetIntake"
                                            Text="-"
                                            TextColor="Black"
                                            FontAttributes="Bold"
                                            FontSize="19"
                                            Margin="0,-8,0,9" />

                                    </StackLayout>


                                    <Grid
                                        Grid.Row="1"
                                        Grid.Column="0"
                                        Grid.ColumnSpan="2"
                                        Margin="0,0"
                                        RowSpacing="0"
                                        BackgroundColor="White"
                                        ColumnSpacing="10">
                                        <Grid.RowDefinitions>
                                            <RowDefinition
                                                Height="*" />
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition
                                                Width="*" />
                                            <ColumnDefinition
                                                Width="*" />
                                            <ColumnDefinition
                                                Width="*" />
                                        </Grid.ColumnDefinitions>

                                        <!--Protein-->
                                        <Frame
                                            CornerRadius="6"
                                            Grid.Row="0"
                                            Grid.Column="0"
                                            HeightRequest="75"
                                            BorderColor="Transparent"
                                            BackgroundColor="White"
                                            HasShadow="False"
                                            Padding="0">
                                            <StackLayout
                                                VerticalOptions="Center"
                                                HorizontalOptions="Center">
                                                <Label
                                                    x:Name="LblProteinText"
                                                    FontSize="17"
FontAttributes="Bold"
                                                    Style="{StaticResource LabelStyle}"
                                                    HorizontalOptions="Center"
                                                    HorizontalTextAlignment="Center"
                                                    TextColor="Black" />
                                                <Label
                                                    x:Name="LblProtein"
                                                    Text="Protein"
                                                    FontSize="17"
                                                    LineHeight="{OnPlatform Android='1.3',iOS='1.2'}" 
                                                    TextColor="#AA000000"
                                                    HorizontalOptions="CenterAndExpand"
                                                    HorizontalTextAlignment="Center" />
                                            </StackLayout>
                                        </Frame>

                                        <!--Carbs-->
                                        <Frame
                                            CornerRadius="6"
                                            Grid.Row="0"
                                            Grid.Column="1"
                                            HeightRequest="75"
                                            BorderColor="Transparent"
                                            BackgroundColor="White"
                                            HasShadow="False"
                                            Padding="0">
                                            <StackLayout
                                                VerticalOptions="Center"
                                                HorizontalOptions="Center">

                                                <Label
                                                    x:Name="LblCarbText"
                                                    FontSize="17"
FontAttributes="Bold"
                                                    Style="{StaticResource LabelStyle}"
                                                    HorizontalOptions="Center"
                                                    HorizontalTextAlignment="Center"
                                                    TextColor="Black" />
                                                <Label
                                                    x:Name="LblCarb"
                                                    Text="Carbs"
                                                    FontSize="17"
                                                    LineHeight="{OnPlatform Android='1.3',iOS='1.2'}" 
                                                    TextColor="#AA000000"
                                                    HorizontalOptions="CenterAndExpand"
                                                    HorizontalTextAlignment="Center" />
                                            </StackLayout>

                                        </Frame>




                                        <!--Fat-->
                                        <Frame
                                            CornerRadius="6"
                                            Grid.Row="0"
                                            Grid.Column="2"
                                            HeightRequest="75"
                                            HasShadow="False"
                                            BorderColor="Transparent"
                                            BackgroundColor="White"
                                            Padding="0">
                                            <StackLayout
                                                VerticalOptions="Center"
                                                HorizontalOptions="Center">
                                                <Label
                                                    x:Name="LblFatText"
                                                    FontSize="17"
FontAttributes="Bold"
                                                    Style="{StaticResource LabelStyle}"
                                                    HorizontalOptions="Center"
                                                    HorizontalTextAlignment="Center"
                                                    TextColor="Black" />
                                                <Label
                                                    x:Name="LblFat"
                                                    Text="Fat"
                                                    FontSize="17"
                                                    LineHeight="{OnPlatform Android='1.3',iOS='1.2'}" 
                                                    TextColor="#AA000000"
                                                    HorizontalOptions="CenterAndExpand"
                                                    HorizontalTextAlignment="Center" />
                                            </StackLayout>
                                        </Frame>
                                    </Grid>
                                </Grid>

                                <Grid
                                    HorizontalOptions="FillAndExpand"
                                    Margin="1,10,1,15">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width=".5*"/>
                                        <ColumnDefinition Width=".5*"/>
                                    </Grid.ColumnDefinitions>
                                    <t:DrMuscleButton
                                        Text="LEARN MORE"
                                        FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                        FontAttributes="Bold"
                                        Grid.Column="0"
                                        HorizontalOptions="Center"
                                        Clicked="BtnLearnMore_Clicked"
                                        Style="{StaticResource buttonLinkStyle}"
                                        VerticalOptions="Center"
                                        TextColor="{x:Static app:AppThemeConstants.BlueColor}" />

                                    <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 6,6,6,6"
                            Padding="0"
                            BackgroundColor="Transparent"
                                        Margin="0"
                                        
                                        
                                        Grid.Column="1"
                                        HorizontalOptions="FillAndExpand"
                                        VerticalOptions="Center"
                                        
                                        HeightRequest="45">

                                        <StackLayout
    HeightRequest="45"
    Padding="0"
    Margin="0"
    VerticalOptions="FillAndExpand"
    HorizontalOptions="FillAndExpand"
    Style="{StaticResource GradientStackStyleBlue}"
    >

                                        <t:DrMuscleButton
                                                CornerRadius="6"
                                                VerticalOptions="Fill"
                                                HeightRequest="45"
                                                FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                                x:Name="btnMealPlan2"
                                                HorizontalOptions="FillAndExpand"
                                                Text=""
                                                IsVisible="true"
                                                Style="{StaticResource highEmphasisButtonStyle}"
                                                BackgroundColor="Transparent"
                                                BorderColor="Transparent"
                                                TextColor="White" />

                                        </StackLayout>
                                    </Border>

                                </Grid>
                            </StackLayout>
                        </controls:CustomFrame>


                        <!--Calendar-->
                        <controls:CustomFrame
                            Margin="10,10,10,0"
                            Padding="10,10,10,10"
                            CornerRadius="12"
HasShadow="True">
                            <StackLayout>
                                <StackLayout
                                    x:Name="calendarBox1"
                                    Padding="0"
                                    BackgroundColor="Transparent">
                                </StackLayout>
                                <Grid
                                    HorizontalOptions="FillAndExpand"
                                    Margin="1,0,1,15">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width=".5*"/>
                                        <ColumnDefinition Width=".5*"/>
                                    </Grid.ColumnDefinitions>
                                    <t:DrMuscleButton
                                        Text="PROGRESS CHARTS"
                                        Clicked="btnChart_Clicked"
                                        FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                        FontAttributes="Bold"
                                        Grid.Column="0"
                                        HorizontalOptions="Center"
                                        VerticalOptions="Center"
                                        Style="{StaticResource buttonLinkStyle}"
                                        TextColor="{x:Static app:AppThemeConstants.BlueColor}"
                                        BorderColor="Transparent"/>

                                    <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 6,6,6,6"
                            Padding="0"
                            BackgroundColor="Transparent"
                                        Margin="0"
                                        
                                        Grid.Column="1"
                                        HorizontalOptions="FillAndExpand"
                                        VerticalOptions="Center"
                                        HeightRequest="45"
                                        >
                                        <StackLayout
    HeightRequest="45"
    Padding="0"
    Margin="0"
    VerticalOptions="FillAndExpand"
    HorizontalOptions="FillAndExpand"
    Style="{StaticResource GradientStackStyleBlue}"
    >

                                        <t:DrMuscleButton
                                            CornerRadius="6"
                                            VerticalOptions="Fill"
                                            HeightRequest="45"
                                            FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                            HorizontalOptions="FillAndExpand"
                                            Text="FULL HISTORY"
                                            Clicked="btnHistory_Clicked"
                                            FontAttributes="Bold"
                                            IsVisible="true"
                                            Style="{StaticResource buttonLinkStyle}"
                                            TextColor="White"
                                            BackgroundColor="Transparent"
                                            BorderColor="Transparent" />
                                        </StackLayout>
                                    </Border>

                                </Grid>
                            </StackLayout>
                        </controls:CustomFrame>
                        <!--First start workout-->
                        <controls:CustomFrame
                            Padding="0,0,0,20"
                            x:Name="WorkoutBox"
                            Margin="10,10,10,5"
                            CornerRadius="12"
HasShadow="True"
                            IsClippedToBounds="True">
                            <StackLayout>
                                <Frame
                                    Padding="0"
                                    Margin="{OnPlatform Android='-3', iOS='-2'}"
                                    BorderColor="Transparent"
                                    CornerRadius="{OnPlatform Android='18', iOS='12'}"
                                    HasShadow="False"
                                    IsClippedToBounds="True">
                                    <ffimageloading:CachedImage
                                        ErrorPlaceholder="backgroundblack.png"
CacheDuration="30"
CacheType="All" 
FadeAnimationEnabled="True"
                                        HeightRequest="{OnPlatform iOS='270'}"
                                        x:Name="ImgWorkout"
                                        Source="workoutbackground.png"
                                        Aspect="AspectFill" />
                                </Frame>
                                <Label
                                    x:Name="workoutNameLabel"
                                    TextColor="Black"
                                    FontAttributes="Bold"
                                    FontSize="19"
                                    Margin="20.5,15,20.5,0" />


                                <Grid
                                    HorizontalOptions="FillAndExpand"
                                    Margin="1,5,1,1"
                                    IsVisible="false">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width=".5*"/>
                                        <ColumnDefinition Width=".5*"/>
                                    </Grid.ColumnDefinitions>
                                    <t:DrMuscleButton
                                        Text="LEARN MORE"
                                        FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                        FontAttributes="Bold"
                                        Grid.Column="0"
                                        HorizontalOptions="Center"
                                        Clicked="BtnLearnMore_Clicked"
                                        VerticalOptions="Center"
                                        Style="{StaticResource buttonLinkStyle}"
                                        TextColor="{x:Static app:AppThemeConstants.BlueColor}" />
                                    <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 6,6,6,6"
                            Padding="0"
                            BackgroundColor="Transparent"
                                        Margin="0,0,0,5"
                                        
                                        VerticalOptions="Center"
                                        Grid.Column="1"
                                        HorizontalOptions="FillAndExpand"
                                        
                                        HeightRequest="45">
                                        <StackLayout
    HeightRequest="45"
    Padding="0"
    Margin="0"
    VerticalOptions="FillAndExpand"
    HorizontalOptions="FillAndExpand"
    Style="{StaticResource GradientStackStyleBlue}"
    >

                                        <t:DrMuscleButton
                                            VerticalOptions="FillAndExpand"
                                            HeightRequest="45"
                                            FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                            HorizontalOptions="FillAndExpand"
                                            Text="START WORKOUT"
                                            x:Name="BtnWelcomeStartWorkout"
                                            Clicked="BtnStartTodayWorkout_Clicked"
                                            IsVisible="true"
                                            Style="{StaticResource highEmphasisButtonStyle}"
                                            BackgroundColor="Transparent"
                                            BorderColor="Transparent"
                                            TextColor="White" />
                                        </StackLayout>
                                    </Border>
                                </Grid>
                                <Label
                                    x:Name="LblWorkoutDuration"
                                    Text="x exercises  · xx minutes"
                                    IsVisible="false"
                                    Margin="20.5,11,20.5,10"
                                    FontSize="17"
                                    LineHeight="{OnPlatform Android='1.3',iOS='1.2'}" 
                                    TextColor="#AA000000"/>
                                <Grid
                                    HorizontalOptions="FillAndExpand"
                                    Margin="11,10,11,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width=".5*"/>
                                        <ColumnDefinition Width=".5*"/>
                                    </Grid.ColumnDefinitions>
                                    <t:DrMuscleButton
                                        Text="EXERCISES"
                                        FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                        FontAttributes="Bold"
                                        Grid.Column="0"
                                        HorizontalOptions="Center"
                                        Clicked="BtnExercises_Clicked"
                                        VerticalOptions="Center"
                                        Style="{StaticResource buttonLinkStyle}"
                                        TextColor="{x:Static app:AppThemeConstants.BlueColor}" />
                                    <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 6,6,6,6"
                            Padding="0"
                            BackgroundColor="Transparent"
                                        Margin="0"
                                        
                                        Grid.Column="1"
                                        HorizontalOptions="FillAndExpand"
                                        VerticalOptions="Center"
                                        
                                        HeightRequest="45">
                                        <StackLayout
    HeightRequest="45"
    Padding="0"
    Margin="0"
    VerticalOptions="FillAndExpand"
    HorizontalOptions="FillAndExpand"
    Style="{StaticResource GradientStackStyleBlue}"
    >

                                        <t:DrMuscleButton
                                            CornerRadius="6"
                                            VerticalOptions="Fill"
                                            HeightRequest="45"
                                            FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                            HorizontalOptions="FillAndExpand"
                                            Text="WORKOUTS"
                                            Clicked="BtnWorkouts_Clicked"
                                            IsVisible="true"
                                            Style="{StaticResource highEmphasisButtonStyle}"
                                            BackgroundColor="Transparent"
                                            BorderColor="Transparent"
                                            TextColor="White" />
                                        </StackLayout>
                                    </Border>
                                </Grid>
                            </StackLayout>
                        </controls:CustomFrame>
                    </StackLayout>
                    <VerticalStackLayout 
                        x:Name="StackSteps3"
                        VerticalOptions="FillAndExpand"
                        Spacing="0">

                        <local:LoadingPlaceholder Margin="10,13,10,0"></local:LoadingPlaceholder>
                        <local:LoadingPlaceholder Margin="10,10,10,0"></local:LoadingPlaceholder>
                        <local:LoadingPlaceholder Margin="10,10,10,0"></local:LoadingPlaceholder>
                        <local:LoadingPlaceholder Margin="10,10,10,0"></local:LoadingPlaceholder>

                    </VerticalStackLayout>
                </StackLayout>
            </ScrollView>

            <StackLayout
                x:Name="EmptyStateStack"
                VerticalOptions="CenterAndExpand"
                IsVisible="false"
                Grid.Column="0"
                Grid.Row="2"
                Spacing="10"
                Padding="0,0,0,2">

                <Image
                    WidthRequest="150"
                    HeightRequest="150"
                    HorizontalOptions="Center"
                    VerticalOptions="Start"
                    Source="emptystar.png" />


                <Label
                    Text="Custom program ready!"
                    Margin="0,30,0,0"
                    HorizontalOptions="Center"
                    FontSize="Medium"
                    FontAttributes="Bold"
                    TextColor="Black" />
                <Label
                    Text="Start working out to see your stats here."
                    HorizontalOptions="Center"
                    HorizontalTextAlignment="Center"
                    FontSize="17"
                    TextColor="Black"
                    FontAttributes="Bold" />

            </StackLayout>
            <BoxView
                HorizontalOptions="FillAndExpand"
                HeightRequest="0"
                BackgroundColor="Transparent"
                x:Name="BoxToolTip"
                Grid.Row="3" />
            <StackLayout
                Grid.Row="4"
                Margin="0,5,0,10"
                BackgroundColor="Transparent"
                VerticalOptions="EndAndExpand"
                HorizontalOptions="FillAndExpand"
                x:Name="stackOptions" />
            <StackLayout
                HorizontalOptions="FillAndExpand"
                x:Name="CongPopupStack"
                IsVisible="false"
                VerticalOptions="FillAndExpand"
                Padding="15,15,15,0"
                Grid.Row="0"
                Grid.RowSpan="5"
                BackgroundColor="#22000000">
                <StackLayout
                    BackgroundColor="Transparent"
                    VerticalOptions="FillAndExpand"
                    Margin="15,0">
                    <Frame
                        Padding="0"
                        CornerRadius="6"
                        IsClippedToBounds="true"
                        VerticalOptions="CenterAndExpand"
                        WidthRequest="290"
                        BackgroundColor="{OnPlatform Android='#195276',iOS='White'}">
                        <StackLayout
                            Spacing="0"
                            WidthRequest="290"
                            BackgroundColor="#195276"
                            HorizontalOptions="Center"
                            Orientation="Vertical"
                            Padding="0,15,0,5">
                            <Label
                                TextColor="{x:Static app:AppThemeConstants.DefaultColor}"
                                FontSize="Medium"
                                HorizontalTextAlignment="{OnPlatform Android='Start', iOS='Center'}"
                                FontAttributes="Bold"
                                HorizontalOptions="StartAndExpand"
                                Margin="15,0,15,15"
                                x:Name="LblCongTitle"
                                Text="Congratulations! You've been working out for XX month(s). How's your experience with Dr. Muscle?" />
                            <BoxView
                                HeightRequest="0.5"
                                BackgroundColor="Gray" />
                            <Label
                                x:Name="LblGreat"
                                Text="Great!"
                                Margin="15,0"
                                HeightRequest="40"
                                VerticalOptions="Center"
                                FontSize="Default"
                                TextColor="{OnPlatform Android='White',iOS='#007aff'}"
                                BackgroundColor="Transparent"
                                HorizontalOptions="FillAndExpand"
                                HorizontalTextAlignment="{OnPlatform Android='Start', iOS='Center'}"
                                VerticalTextAlignment="Center">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer
                                         Tapped="GreatAction_Tapped"/>
                                </Label.GestureRecognizers>
                            </Label>
                            <BoxView
                                HeightRequest="0.5"
                                BackgroundColor="Gray" />
                            <Label
                                x:Name="LblGoodButImprove"
                                Text="Good, but could be improved"
                                Margin="15,0"
                                LineBreakMode="TailTruncation"
                                HeightRequest="40"
                                VerticalOptions="Center"
                                FontSize="Default"
                                TextColor="{OnPlatform Android='White',iOS='#007aff'}"
                                BackgroundColor="Transparent"
                                HorizontalOptions="FillAndExpand"
                                HorizontalTextAlignment="{OnPlatform Android='Start', iOS='Center'}"
                                VerticalTextAlignment="Center">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer
                                         Tapped="GoodAction_Tapped"/>
                                </Label.GestureRecognizers>
                            </Label>
                            <BoxView
                                HeightRequest="0.5"
                                BackgroundColor="Gray" />
                            <Label
                                x:Name="LblBad"
                                Text="Bad"
                                Margin="15,0"
                                HeightRequest="40"
                                VerticalOptions="Center"
                                FontSize="Default"
                                TextColor="{OnPlatform Android='White',iOS='#007aff'}"
                                BackgroundColor="Transparent"
                                HorizontalOptions="FillAndExpand"
                                HorizontalTextAlignment="{OnPlatform Android='Start', iOS='Center'}"
                                VerticalTextAlignment="Center">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer
                                         Tapped="BadAction_Tapped"/>
                                </Label.GestureRecognizers>
                            </Label>
                        </StackLayout>
                    </Frame>
                </StackLayout>
            </StackLayout>
            <StackLayout
                HorizontalOptions="FillAndExpand"
                x:Name="welcomePopupStack"
                IsVisible="false"
                VerticalOptions="FillAndExpand"
                Padding="15,15,15,0"
                Grid.Row="0"
                Grid.RowSpan="5"
                BackgroundColor="#22000000">
                <StackLayout
                    BackgroundColor="Transparent"
                    VerticalOptions="FillAndExpand"
                    Margin="15,0">
                    <Frame
                        Padding="0"
                        CornerRadius="6"
                        IsClippedToBounds="true"
                        VerticalOptions="CenterAndExpand"
                        WidthRequest="290"
                        BackgroundColor="{OnPlatform Android='#195276',iOS='White'}">
                        <StackLayout
                            Spacing="0"
                            WidthRequest="290"
                            BackgroundColor="{OnPlatform Android='#195276',iOS='White'}"
                            HorizontalOptions="Center"
                            Orientation="Vertical"
                            Padding="0,15,0,5">
                            <Label
                                TextColor="{x:Static app:AppThemeConstants.DefaultColor}"
                                FontSize="Medium"
                                HorizontalTextAlignment="{OnPlatform Android='Start', iOS='Center'}"
                                FontAttributes="Bold"
                                HorizontalOptions="StartAndExpand"
                                Margin="15,0,15,15"
                                x:Name="LblWelcomeTitle"
                                Text="Congratulations! You've been working out for XX month(s). How's your experience with Dr. Muscle?" />
                            <BoxView
                                HeightRequest="0.5"
                                BackgroundColor="Gray" />
                            <Label
                                Text="Later"
                                Margin="15,0"
                                HeightRequest="40"
                                VerticalOptions="Center"
                                FontSize="Default"
                                TextColor="{OnPlatform Android='White',iOS='#007aff'}"
                                BackgroundColor="Transparent"
                                HorizontalOptions="FillAndExpand"
                                HorizontalTextAlignment="{OnPlatform Android='Start', iOS='Center'}"
                                VerticalTextAlignment="Center">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer
                                         Tapped="LaterAction_Tapped"/>
                                </Label.GestureRecognizers>
                            </Label>
                            <BoxView
                                HeightRequest="0.5"
                                BackgroundColor="Gray" />
                            <Label
                                Text="I'm tired or short on time"
                                Margin="15,0"
                                LineBreakMode="TailTruncation"
                                HeightRequest="40"
                                VerticalOptions="Center"
                                FontSize="Default"
                                TextColor="{OnPlatform Android='White',iOS='#007aff'}"
                                BackgroundColor="Transparent"
                                HorizontalOptions="FillAndExpand"
                                HorizontalTextAlignment="{OnPlatform Android='Start', iOS='Center'}"
                                VerticalTextAlignment="Center">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer
                                        Tapped="TiredTodayAction_Tapped" />
                                </Label.GestureRecognizers>
                            </Label>
                            <BoxView
                                HeightRequest="0.5"
                                BackgroundColor="Gray" />
                            <Label
                                Text="Start workout"
                                Margin="15,0"
                                HeightRequest="40"
                                VerticalOptions="Center"
                                FontSize="Default"
                                TextColor="{OnPlatform Android='White',iOS='#007aff'}"
                                BackgroundColor="Transparent"
                                HorizontalOptions="FillAndExpand"
                                HorizontalTextAlignment="{OnPlatform Android='Start', iOS='Center'}"
                                VerticalTextAlignment="Center">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer
                                        Tapped="StartAction_Tapped" />
                                </Label.GestureRecognizers>
                            </Label>
                        </StackLayout>
                    </Frame>
                </StackLayout>
            </StackLayout>

            <StackLayout
                HorizontalOptions="FillAndExpand"
                x:Name="welcomeRestPopupStack"
                IsVisible="false"
                VerticalOptions="FillAndExpand"
                Padding="15,15,15,0"
                Grid.Row="0"
                Grid.RowSpan="5"
                BackgroundColor="#22000000">
                <StackLayout
                    BackgroundColor="Transparent"
                    VerticalOptions="FillAndExpand"
                    Margin="15,0">
                    <Frame
                        Padding="0"
                        CornerRadius="6"
                        IsClippedToBounds="true"
                        VerticalOptions="CenterAndExpand"
                        WidthRequest="290"
                        BackgroundColor="{OnPlatform Android='#195276',iOS='White'}">
                        <StackLayout
                            Spacing="0"
                            WidthRequest="290"
                            BackgroundColor="{OnPlatform Android='#195276',iOS='White'}"
                            HorizontalOptions="Center"
                            Orientation="Vertical"
                            Padding="0,15,0,5">
                            <Label
                                TextColor="{x:Static app:AppThemeConstants.DefaultColor}"
                                FontSize="Medium"
                                HorizontalTextAlignment="Center"
                                FontAttributes="Bold"
                                HorizontalOptions="StartAndExpand"
                                Margin="15,0,15,15"
                                x:Name="LblWelcomeRestTitle"
                                Text="How are you today?" />
                            <BoxView
                                HeightRequest="0.5"
                                BackgroundColor="Gray" />
                            <Label
                                Text="Tired"
                                Margin="15,0"
                                HeightRequest="40"
                                VerticalOptions="Center"
                                FontSize="Default"
                                TextColor="{OnPlatform Android='White',iOS='#007aff'}"
                                BackgroundColor="Transparent"
                                HorizontalOptions="FillAndExpand"
                                HorizontalTextAlignment="{OnPlatform Android='Start', iOS='Center'}"
                                VerticalTextAlignment="Center">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer
                                         />
                                </Label.GestureRecognizers>
                            </Label>
                            <BoxView
                                HeightRequest="0.5"
                                BackgroundColor="Gray" />
                            <Label
                                Text="Short on time"
                                Margin="15,0"
                                LineBreakMode="TailTruncation"
                                HeightRequest="40"
                                VerticalOptions="Center"
                                FontSize="Default"
                                TextColor="{OnPlatform Android='White',iOS='#007aff'}"
                                BackgroundColor="Transparent"
                                HorizontalOptions="FillAndExpand"
                                HorizontalTextAlignment="{OnPlatform Android='Start', iOS='Center'}"
                                VerticalTextAlignment="Center">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer
                                         />
                                </Label.GestureRecognizers>
                            </Label>
                            <BoxView
                                HeightRequest="0.5"
                                BackgroundColor="Gray" />
                            <Label
                                Text="Good"
                                Margin="15,0"
                                HeightRequest="40"
                                VerticalOptions="Center"
                                FontSize="Default"
                                TextColor="{OnPlatform Android='White',iOS='#007aff'}"
                                BackgroundColor="Transparent"
                                HorizontalOptions="FillAndExpand"
                                HorizontalTextAlignment="{OnPlatform Android='Start', iOS='Center'}"
                                VerticalTextAlignment="Center">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer
                                         />
                                </Label.GestureRecognizers>
                            </Label>
                        </StackLayout>
                    </Frame>
                </StackLayout>
            </StackLayout>
        </Grid>
    </ContentPage.Content>
</ContentPage>