using NUnit.Framework;
using DrMuscle.UITests.Helpers;
using DrMuscle.UITests.Pages;
using System;
using System.Threading;

namespace DrMuscle.UITests.Tests
{
    /// <summary>
    /// Full workout flow tests that require Appium for UI interaction
    /// </summary>
    [TestFixture]
    public class WorkoutFlowWithAppiumTests : AppiumSetup
    {
        [Test]
        public void CompleteFullWorkoutFlow()
        {
            // Arrange
            var account = TestAccount.GenerateWithSeed("workout-appium");
            var loginPage = new LoginPage(Driver!);
            var mainPage = new MainPage(Driver!);
            var workoutPage = new WorkoutPage(Driver!);
            
            Console.WriteLine($"Testing full workout flow with account: {account.email}");
            
            // Step 1: Login
            Thread.Sleep(3000);
            loginPage.WaitForPageToLoad();
            TakeScreenshot("01-login-page");
            
            loginPage.Login(account.email, account.password);
            Thread.Sleep(5000);
            TakeScreenshot("02-after-login");
            
            // Step 2: Navigate to home and start workout
            mainPage.WaitForPageToLoad();
            mainPage.NavigateToHome();
            Thread.Sleep(2000);
            TakeScreenshot("03-home-page");
            
            // Wait for Start Workout button
            workoutPage.WaitForStartWorkout();
            Assert.That(workoutPage.IsStartWorkoutVisible(), 
                "Start Workout button should be visible on home page");
            
            // Step 3: Start workout
            workoutPage.StartWorkout();
            Thread.Sleep(3000);
            TakeScreenshot("04-workout-started");
            
            // Step 4: Wait for exercise list and select first exercise
            workoutPage.WaitForExerciseList();
            Assert.That(workoutPage.IsExerciseListVisible(), 
                "Exercise list should be visible after starting workout");
            
            workoutPage.SelectFirstExercise();
            Thread.Sleep(3000);
            TakeScreenshot("05-first-exercise-selected");
            
            // Step 5: Enter and save sets
            Console.WriteLine("Entering exercise sets...");
            
            // Set 1
            workoutPage.EnterSet("10", "135");
            workoutPage.SaveSet();
            Thread.Sleep(2000);
            TakeScreenshot("06-set1-saved");
            
            // Set 2
            workoutPage.EnterSet("8", "135");
            workoutPage.SaveSet();
            Thread.Sleep(2000);
            TakeScreenshot("07-set2-saved");
            
            // Set 3
            workoutPage.EnterSet("6", "135");
            workoutPage.SaveSet();
            Thread.Sleep(2000);
            TakeScreenshot("08-set3-saved");
            
            // Step 6: Finish exercise
            workoutPage.FinishExercise();
            Thread.Sleep(3000);
            TakeScreenshot("09-exercise-finished");
            
            // Step 7: Finish workout
            workoutPage.FinishWorkout();
            Thread.Sleep(3000);
            TakeScreenshot("10-workout-finishing");
            
            // Step 8: Verify workout summary
            workoutPage.WaitForWorkoutSummary();
            Assert.That(workoutPage.IsWorkoutSummaryVisible(), 
                "Workout summary should be displayed after finishing workout");
            
            TakeScreenshot("11-workout-summary");
            
            // Verify summary data
            var totalSets = workoutPage.GetTotalSets();
            var totalWeight = workoutPage.GetTotalWeight();
            
            Console.WriteLine($"Workout completed!");
            Console.WriteLine($"Total sets: {totalSets}");
            Console.WriteLine($"Total weight: {totalWeight}");
            
            Assert.That(totalSets, Is.Not.Empty, "Total sets should be displayed");
            Assert.That(totalWeight, Is.Not.Empty, "Total weight should be displayed");
            
            Assert.Pass("Successfully completed full workout flow");
        }
        
        [Test]
        public void StartAndAbandonWorkout()
        {
            // Test abandoning a workout mid-flow
            var account = TestAccount.GenerateWithSeed("workout-abandon");
            var loginPage = new LoginPage(Driver!);
            var workoutPage = new WorkoutPage(Driver!);
            
            // Login
            loginPage.WaitForPageToLoad();
            loginPage.Login(account.email, account.password);
            Thread.Sleep(5000);
            
            // Start workout
            workoutPage.WaitForStartWorkout();
            workoutPage.StartWorkout();
            Thread.Sleep(3000);
            
            // Select exercise but don't complete
            workoutPage.WaitForExerciseList();
            workoutPage.SelectFirstExercise();
            Thread.Sleep(3000);
            
            // Enter one set
            workoutPage.EnterSet("5", "100");
            workoutPage.SaveSet();
            Thread.Sleep(2000);
            
            TakeScreenshot("abandoned-workout-state");
            
            // Navigate away (simulate abandoning)
            // In real app, this might show a confirmation dialog
            
            Assert.Pass("Workout abandonment flow documented");
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private void TakeScreenshot(string name)
        {
            TakeScreenshot(Driver, name);
        }
        #pragma warning restore CA1822 // Mark members as static
        
        private static void TakeScreenshot(AppiumDriver? driver, string name)
        {
            try
            {
                var screenshot = driver?.GetScreenshot();
                if (screenshot != null)
                {
                    var screenshotPath = Path.Combine(
                        TestContext.CurrentContext.WorkDirectory, 
                        "Screenshots",
                        $"{TestContext.CurrentContext.Test.Name}_{name}.png"
                    );
                    
                    // Ensure directory exists
                    Directory.CreateDirectory(Path.GetDirectoryName(screenshotPath)!);
                    
                    screenshot.SaveAsFile(screenshotPath);
                    TestContext.AddTestAttachment(screenshotPath, name);
                    Console.WriteLine($"Screenshot saved: {name}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to take screenshot {name}: {ex.Message}");
            }
        }
    }
}