using NUnit.Framework;
using DrMuscle.UITests.Helpers;
using System;
using System.IO;
using System.Threading;

namespace DrMuscle.UITests.Tests
{
    /// <summary>
    /// Tests for set recording and weight/rep entry flows
    /// </summary>
    [TestFixture]
    public class SimCtlSetRecordingTests
    {
        private string screenshotDir = string.Empty;
        
        [OneTimeSetUp]
        public void OneTimeSetup()
        {
            if (!SimCtlTestRunner.IsAvailable())
            {
                Assert.Ignore("xcrun simctl is not available");
            }
        }
        
        [SetUp]
        public void Setup()
        {
            var testRunId = TestDataHelper.GenerateTestRunId();
            screenshotDir = Path.Combine(Directory.GetCurrentDirectory(), "Screenshots", "SetRecording", testRunId);
            Directory.CreateDirectory(screenshotDir);
        }
        
        [Test]
        [Order(1)]
        [FlakyTestRetry(2)]
        [User<PERSON><PERSON><PERSON>(TestCategories.WorkoutJourney, TestCategories.ExerciseExecution)]
        [Description("Tests recording a basic set with weight and rep entry")]
        public void RecordBasicSet()
        {
            TestContext.WriteLine("=== Testing Basic Set Recording ===");
            
            // Setup and navigate to exercise
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(TestTimings.AppLaunch);
            Thread.Sleep(TestTimings.LoginFlow);
            Thread.Sleep(TestTimings.WorkoutStart);
            
            // Start set
            TestContext.WriteLine("Starting first set...");
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "basic-01-ready.png"));
            
            // Simulate set execution
            Thread.Sleep(TestTimings.SetExecution);
            
            // Weight entry
            TestContext.WriteLine("Entering weight...");
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "basic-02-weight-picker.png"));
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "basic-03-weight-selected.png"));
            
            // Rep entry
            TestContext.WriteLine("Entering reps...");
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "basic-04-rep-picker.png"));
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "basic-05-reps-selected.png"));
            
            // Save set
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "basic-06-set-saved.png"));
            
            // Rest timer
            TestContext.WriteLine("Rest period started...");
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "basic-07-rest-timer.png"));
            
            Assert.Pass("Basic set recording completed successfully");
        }
        
        [Test]
        [Order(2)]
        [FlakyTestRetry(2)]
        [UserJourney(TestCategories.WorkoutJourney, TestCategories.ExerciseExecution)]
        [Description("Tests recording different types of sets (regular, drop, rest-pause, failure)")]
        public void RecordDifferentSetTypes()
        {
            TestContext.WriteLine("=== Testing Different Set Types ===");
            
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(TestTimings.AppLaunch);
            Thread.Sleep(TestTimings.LoginFlow);
            Thread.Sleep(TestTimings.WorkoutStart);
            
            // Regular set
            TestContext.WriteLine("Recording regular set...");
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "types-01-regular-start.png"));
            Thread.Sleep(TestTimings.SetExecution);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "types-02-regular-complete.png"));
            Thread.Sleep(TestTimings.ScreenTransition);
            
            // Drop set
            TestContext.WriteLine("Recording drop set...");
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "types-03-dropset-start.png"));
            Thread.Sleep(TestTimings.SetExecution);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "types-04-dropset-weight1.png"));
            Thread.Sleep(TestTimings.SetExecution / 2);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "types-05-dropset-weight2.png"));
            Thread.Sleep(TestTimings.ScreenTransition);
            
            // Rest-pause set
            TestContext.WriteLine("Recording rest-pause set...");
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "types-06-restpause-start.png"));
            Thread.Sleep(TestTimings.SetExecution / 2);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "types-07-restpause-mini1.png"));
            Thread.Sleep(TestTimings.SetExecution / 2);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "types-08-restpause-mini2.png"));
            Thread.Sleep(TestTimings.SetExecution / 2);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "types-09-restpause-complete.png"));
            
            // Failure set
            TestContext.WriteLine("Recording failure set...");
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "types-10-failure-start.png"));
            Thread.Sleep(TestTimings.SetExecution + TestTimings.UIElementRender); // Slightly longer for failure
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "types-11-failure-complete.png"));
            
            Assert.Pass("All set types recorded successfully");
        }
        
        [Test]
        [Order(3)]
        [UserJourney(TestCategories.WorkoutJourney, TestCategories.ExerciseExecution)]
        [Description("Tests weight and rep picker UI interactions")]
        public void WeightAndRepPickers()
        {
            TestContext.WriteLine("=== Testing Weight and Rep Pickers ===");
            
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(TestTimings.AppLaunch);
            Thread.Sleep(TestTimings.LoginFlow);
            Thread.Sleep(TestTimings.WorkoutStart);
            
            // Complete a set to trigger pickers
            Thread.Sleep(TestTimings.SetExecution);
            
            // Weight picker variations
            TestContext.WriteLine("Testing weight picker...");
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "picker-01-weight-default.png"));
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "picker-02-weight-scrolling.png"));
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "picker-03-weight-custom.png"));
            Thread.Sleep(TestTimings.UIElementRender);
            
            // Rep picker variations
            TestContext.WriteLine("Testing rep picker...");
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "picker-04-rep-default.png"));
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "picker-05-rep-scrolling.png"));
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "picker-06-rep-high.png"));
            
            // Special inputs
            TestContext.WriteLine("Testing special inputs...");
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "picker-07-bodyweight.png"));
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "picker-08-assisted.png"));
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "picker-09-time-based.png"));
            
            Assert.Pass("Weight and rep pickers tested successfully");
        }
        
        [Test]
        [Order(4)]
        [UserJourney(TestCategories.WorkoutJourney, TestCategories.ExerciseExecution)]
        [Description("Tests rest timer behavior including countdown and skip functionality")]
        public void RestTimerBehavior()
        {
            TestContext.WriteLine("=== Testing Rest Timer Behavior ===");
            
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(TestTimings.AppLaunch);
            Thread.Sleep(TestTimings.LoginFlow);
            Thread.Sleep(TestTimings.WorkoutStart);
            
            // Complete first set
            Thread.Sleep(TestTimings.SetExecution);
            Thread.Sleep(TestTimings.UIElementRender); // Save set
            
            // Capture rest timer states
            TestContext.WriteLine("Capturing rest timer states...");
            
            // Timer start
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "timer-01-start-120s.png"));
            
            // For fast mode, skip most of the timer
            if (TestConfiguration.UseFastMode)
            {
                Thread.Sleep(TestTimings.RestPeriod);
                SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "timer-02-fast-mode.png"));
            }
            else
            {
                // Timer at different intervals (full timing)
                Thread.Sleep(30000);
                SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "timer-02-at-90s.png"));
                
                Thread.Sleep(30000);
                SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "timer-03-at-60s.png"));
                
                Thread.Sleep(30000);
                SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "timer-04-at-30s.png"));
                
                Thread.Sleep(20000);
                SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "timer-05-at-10s.png"));
                
                Thread.Sleep(10000);
            }
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "timer-06-complete.png"));
            
            // Skip timer test
            TestContext.WriteLine("Testing timer skip...");
            Thread.Sleep(TestTimings.SetExecution); // Do another set
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "timer-07-skip-option.png"));
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "timer-08-skipped.png"));
            
            Assert.Pass("Rest timer behavior tested successfully");
        }
        
        [Test]
        [Order(5)]
        [UserJourney(TestCategories.WorkoutJourney, TestCategories.ExerciseExecution)]
        [Description("Tests modifying and deleting recorded sets")]
        public void SetModificationAndDeletion()
        {
            TestContext.WriteLine("=== Testing Set Modification ===");
            
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(TestTimings.AppLaunch);
            Thread.Sleep(TestTimings.LoginFlow);
            Thread.Sleep(TestTimings.WorkoutStart);
            
            // Complete a set
            Thread.Sleep(TestTimings.SetExecution);
            Thread.Sleep(TestTimings.UIElementRender); // Save
            
            // Modify last set
            TestContext.WriteLine("Modifying last set...");
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "modify-01-set-list.png"));
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "modify-02-edit-mode.png"));
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "modify-03-new-values.png"));
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "modify-04-saved.png"));
            
            // Delete set
            TestContext.WriteLine("Deleting set...");
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "delete-01-swipe.png"));
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "delete-02-confirm.png"));
            Thread.Sleep(TestTimings.UIElementRender);
            SimCtlTestRunner.TakeScreenshot(Path.Combine(screenshotDir, "delete-03-removed.png"));
            
            Assert.Pass("Set modification and deletion tested");
        }
        
        [TearDown]
        public void TearDown()
        {
            var screenshots = Directory.GetFiles(screenshotDir, "*.png");
            TestContext.WriteLine($"Set recording tests captured {screenshots.Length} screenshots");
            TestContext.WriteLine($"Screenshots saved to: {screenshotDir}");
        }
    }
}