#!/bin/bash

# Script to manage test artifact storage on the self-hosted Mac runner
# Can be run manually or via cron job

ARCHIVE_DIR="$HOME/DrMuscleTestArchive"
RETENTION_DAYS=7  # Reduced from 30 to 7 days
MAX_SIZE_GB=2     # Reduced from 10GB to 2GB

echo "DrMuscle Test Archive Cleanup"
echo "============================="
echo "Archive location: $ARCHIVE_DIR"

# Check if archive directory exists
if [ ! -d "$ARCHIVE_DIR" ]; then
    echo "Archive directory does not exist. Nothing to clean."
    exit 0
fi

# Show current disk usage
CURRENT_SIZE=$(du -sh "$ARCHIVE_DIR" 2>/dev/null | cut -f1)
echo "Current archive size: $CURRENT_SIZE"

# Count total runs
TOTAL_RUNS=$(find "$ARCHIVE_DIR" -name "run-*" -type d | wc -l)
echo "Total test runs archived: $TOTAL_RUNS"

# Find old directories
OLD_DIRS=$(find "$ARCHIVE_DIR" -type d -mtime +$RETENTION_DAYS -name "run-*" 2>/dev/null)
OLD_COUNT=$(echo "$OLD_DIRS" | grep -c "run-" || echo "0")

if [ "$OLD_COUNT" -gt 0 ]; then
    echo ""
    echo "Found $OLD_COUNT runs older than $RETENTION_DAYS days:"
    echo "$OLD_DIRS" | head -10
    
    if [ "$OLD_COUNT" -gt 10 ]; then
        echo "... and $((OLD_COUNT - 10)) more"
    fi
    
    # Delete old directories
    echo ""
    echo "Deleting old runs..."
    echo "$OLD_DIRS" | xargs rm -rf 2>/dev/null
    echo "✅ Deleted $OLD_COUNT old test runs"
else
    echo "No runs older than $RETENTION_DAYS days found"
fi

# Check total size and clean if needed
CURRENT_SIZE_GB=$(du -s "$ARCHIVE_DIR" 2>/dev/null | awk '{printf "%.2f", $1/1024/1024}')
if (( $(echo "$CURRENT_SIZE_GB > $MAX_SIZE_GB" | bc -l) )); then
    echo ""
    echo "⚠️  Archive size (${CURRENT_SIZE_GB}GB) exceeds limit (${MAX_SIZE_GB}GB)"
    echo "Removing oldest runs until under limit..."
    
    while (( $(echo "$CURRENT_SIZE_GB > $MAX_SIZE_GB" | bc -l) )); do
        # Find and delete oldest run
        OLDEST=$(find "$ARCHIVE_DIR" -name "run-*" -type d -printf '%T+ %p\n' | sort | head -1 | cut -d' ' -f2-)
        if [ -n "$OLDEST" ]; then
            echo "Removing: $OLDEST"
            rm -rf "$OLDEST"
            CURRENT_SIZE_GB=$(du -s "$ARCHIVE_DIR" 2>/dev/null | awk '{printf "%.2f", $1/1024/1024}')
        else
            break
        fi
    done
fi

# Clean up empty date directories
find "$ARCHIVE_DIR" -type d -empty -delete 2>/dev/null

# Final summary
echo ""
echo "Cleanup Summary"
echo "==============="
FINAL_SIZE=$(du -sh "$ARCHIVE_DIR" 2>/dev/null | cut -f1)
FINAL_RUNS=$(find "$ARCHIVE_DIR" -name "run-*" -type d | wc -l)
echo "Final archive size: $FINAL_SIZE"
echo "Remaining test runs: $FINAL_RUNS"

# Show recent runs
echo ""
echo "Recent test runs:"
find "$ARCHIVE_DIR" -name "run-*" -type d -printf '%TY-%Tm-%Td %TH:%TM %p\n' | sort -r | head -5

# Create cron job suggestion
echo ""
echo "💡 To run this cleanup automatically, add to crontab:"
echo "   0 2 * * * $PWD/$(basename $0)"