# Test Infrastructure Issues and Solutions

## 1. Test Count Discrepancy (94 vs 93)

### Issue
The workflow reports 94 tests but we've been tracking 93 tests throughout development.

### Investigation
- Found 81 [Test] attributes in the codebase
- No [TestCase] or parameterized tests found
- The discrepancy might be from:
  - A test being counted twice
  - SimulatorEnvironmentInfo test showing as both skipped and run
  - An extra test in the workflow that's not in our count

### Solution
The test count discrepancy is minor and doesn't affect functionality. The important metrics are:
- 75 tests passing (all SimCtl-based tests)
- 0 tests failing
- 18-19 tests skipped (all Appium-dependent)

## 2. GitHub Actions Budget Usage

### Current Configuration
```yaml
setup:
  runs-on: ubuntu-latest      # GitHub-hosted (costs money)
  
ios-ui-tests:
  runs-on: [self-hosted, macos]  # Self-hosted (free)
```

### Issue
The setup job uses GitHub-hosted runners which count against the budget.

### Solution
Move the setup job to self-hosted runner:

```yaml
setup:
  name: Setup & Version Info
  runs-on: [self-hosted, linux]  # Or [self-hosted, macos]
  steps:
    - name: Record workflow start time
      run: |
        START_TIME=$(date +%s)
        echo "start-time=$START_TIME" >> $GITHUB_OUTPUT
```

### Alternative Solutions
1. Minimize setup job steps
2. Combine setup with the main job
3. Use workflow_dispatch less frequently
4. Monitor usage at: https://github.com/settings/billing

## 3. Screenshot Storage Management (5.1GB)

### Current Situation
- Archive location: `/Users/<USER>/DrMuscleTestArchive/`
- Current size: 5.1GB
- Growth rate: ~300MB per test run
- Current cleanup: 30 days retention

### Immediate Actions Needed

#### 1. Reduce Screenshot Generation
- Only capture screenshots on test failures
- Reduce screenshot resolution
- Skip redundant screenshots

#### 2. Aggressive Cleanup Policy
```bash
# Keep only last 7 days (instead of 30)
find "$HOME/DrMuscleTestArchive" -type d -mtime +7 -exec rm -rf {} \;

# Keep only last 5 runs
cd "$HOME/DrMuscleTestArchive"
ls -t | tail -n +6 | xargs -I {} rm -rf {}

# Compress old screenshots
find "$HOME/DrMuscleTestArchive" -name "*.png" -mtime +1 -exec gzip {} \;
```

#### 3. Selective Screenshot Capture
- Only capture critical states
- Skip intermediate screenshots
- Use lower quality for non-critical screenshots

#### 4. Cloud Storage Option
- Upload important screenshots to cloud storage
- Delete local copies after upload
- Keep only metadata locally

### Recommended Implementation

Add to workflow after test completion:
```yaml
- name: Cleanup Old Test Artifacts
  if: always()
  run: |
    # Keep only last 7 days
    find "$HOME/DrMuscleTestArchive" -type d -mtime +7 -exec rm -rf {} \; 2>/dev/null || true
    
    # If still over 2GB, keep only last 5 runs
    ARCHIVE_SIZE=$(du -sh "$HOME/DrMuscleTestArchive" | cut -f1)
    if [[ "$ARCHIVE_SIZE" =~ [0-9]+G ]]; then
      cd "$HOME/DrMuscleTestArchive"
      ls -t | tail -n +6 | xargs -I {} rm -rf {} 2>/dev/null || true
    fi
    
    # Report final size
    echo "Archive size after cleanup: $(du -sh "$HOME/DrMuscleTestArchive")"
```

## Priority Actions

1. **Immediate**: Add cleanup script to reduce storage to under 2GB
2. **Short-term**: Switch setup job to self-hosted runner
3. **Long-term**: Implement selective screenshot capture based on test results