using NUnit.Framework;
using System;
using System.IO;
using System.Threading;
using DrMuscle.UITests.Helpers;

namespace DrMuscle.UITests.Tests
{
    /// <summary>
    /// Basic smoke tests to verify SimCtl functionality without app dependency
    /// </summary>
    [TestFixture]
    [User<PERSON><PERSON><PERSON>(TestCategories.FirstTimeUser, TestCategories.AppLaunch)]
    [Description("Basic smoke tests to verify SimCtl functionality and app readiness")]
    public class SimCtlSmokeTests
    {
        private string screenshotDir = string.Empty;

        [SetUp]
        public void Setup()
        {
            screenshotDir = Path.Combine(Directory.GetCurrentDirectory(), "Screenshots", "Smoke");
            Directory.CreateDirectory(screenshotDir);
        }

        [Test]
        [Order(1)]
        [Description("Verify SimCtl command-line tools are available")]
        public void SimCtlIsAvailable()
        {
            TestContext.WriteLine("=== Testing SimCtl Availability ===");
            
            var isAvailable = SimCtlTestRunner.IsAvailable();
            Assert.That(isAvailable, Is.True, "SimCtl should be available on macOS");
            
            TestContext.WriteLine("✅ SimCtl is available");
        }

        [Test]
        [Order(2)]
        [Description("Verify iOS simulator is booted and detectable")]
        public void CanGetBootedDevice()
        {
            TestContext.WriteLine("=== Testing Device Detection ===");
            
            string? deviceId = null;
            Assert.DoesNotThrow(() => {
                deviceId = SimCtlTestRunner.GetBootedDeviceId();
            }, "Should be able to get booted device ID");
            
            Assert.That(deviceId, Is.Not.Null.And.Not.Empty, "Device ID should not be null or empty");
            TestContext.WriteLine($"✅ Found booted device: {deviceId}");
        }

        [Test]
        [Order(3)]
        [Description("Verify screenshot capture functionality works")]
        public void CanTakeSimulatorScreenshot()
        {
            TestContext.WriteLine("=== Testing Screenshot Capability ===");
            
            var screenshotPath = Path.Combine(screenshotDir, "simulator-test.png");
            
            Assert.DoesNotThrow(() => {
                SimCtlTestRunner.TakeScreenshot(screenshotPath);
            }, "Should be able to take screenshot");
            
            Assert.That(File.Exists(screenshotPath), Is.True, "Screenshot file should exist");
            
            var fileInfo = new FileInfo(screenshotPath);
            Assert.That(fileInfo.Length, Is.GreaterThan(1000), "Screenshot should have content");
            
            TestContext.WriteLine($"✅ Screenshot saved: {screenshotPath} ({fileInfo.Length} bytes)");
        }

        [Test]
        [Order(4)]
        [Description("Verify app installation status can be checked")]
        public void CanCheckAppInstallationStatus()
        {
            TestContext.WriteLine("=== Testing App Installation Check ===");
            
            bool isInstalled = false;
            Assert.DoesNotThrow(() => {
                isInstalled = SimCtlTestRunner.IsAppInstalled("com.drmaxmuscle.max");
            }, "Should be able to check app installation status");
            
            TestContext.WriteLine($"✅ App installation check completed. Installed: {isInstalled}");
            
            // This test passes regardless of installation status
            // We're just verifying the check itself works
            Assert.Pass("App installation check completed successfully");
        }

        [Test]
        [Order(5)]
        [Description("Verify multiple screenshots can be taken sequentially")]
        public void CanTakeMultipleScreenshots()
        {
            TestContext.WriteLine("=== Testing Multiple Screenshots ===");
            
            for (int i = 1; i <= 3; i++)
            {
                var screenshotPath = Path.Combine(screenshotDir, $"multi-screenshot-{i}.png");
                
                SimCtlTestRunner.TakeScreenshot(screenshotPath);
                Thread.Sleep(1000); // Wait between screenshots
                
                Assert.That(File.Exists(screenshotPath), Is.True, $"Screenshot {i} should exist");
                TestContext.WriteLine($"✅ Screenshot {i} saved");
            }
            
            Assert.Pass("All screenshots taken successfully");
        }

        [Test]
        [Order(6)]
        [Description("Collect and verify simulator environment information")]
        public void SimulatorEnvironmentInfo()
        {
            TestContext.WriteLine("=== Simulator Environment Information ===");
            
            try
            {
                var deviceId = SimCtlTestRunner.GetBootedDeviceId();
                TestContext.WriteLine($"Device ID: {deviceId}");
                
                // Take a screenshot to show current simulator state
                var infoScreenshot = Path.Combine(screenshotDir, "environment-info.png");
                SimCtlTestRunner.TakeScreenshot(infoScreenshot);
                TestContext.WriteLine($"Environment screenshot: {infoScreenshot}");
                
                // Log test context information
                TestContext.WriteLine($"Test Directory: {TestContext.CurrentContext.TestDirectory}");
                TestContext.WriteLine($"Work Directory: {TestContext.CurrentContext.WorkDirectory}");
                TestContext.WriteLine($"OS Version: {Environment.OSVersion}");
                TestContext.WriteLine($"Machine Name: {Environment.MachineName}");
                
                Assert.Pass("Environment information collected successfully");
            }
            catch (Exception ex)
            {
                TestContext.WriteLine($"Failed to collect environment info: {ex.Message}");
                Assert.Inconclusive("Could not collect all environment information");
            }
        }
    }
} 