﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Acr.Support" version="2.1.0" targetFramework="xamarinios10" />
  <package id="Acr.UserDialogs" version="7.2.0.564" targetFramework="xamarinios10" />
  <package id="Branch-Xamarin-Linking-SDK" version="7.0.7" targetFramework="xamarinios10" />
  <package id="BTProgressHUD" version="1.3.5" targetFramework="xamarinios10" />
  <package id="EasyTipView" version="1.0.1" targetFramework="xamarinios10" />
  <package id="ImageFromXamarinUI" version="1.0.0" targetFramework="xamarinios10" />
  <package id="K4os.Compression.LZ4" version="1.3.5" targetFramework="xamarinios10" />
  <package id="Microcharts.Forms" version="1.0.0-preview1" targetFramework="xamarinios10" />
  <package id="Microcharts.iOS" version="1.0.0-preview1" targetFramework="xamarinios10" />
  <package id="Microsoft.AppCenter" version="4.1.0" targetFramework="xamarinios10" />
  <package id="Microsoft.AppCenter.Analytics" version="4.1.0" targetFramework="xamarinios10" />
  <package id="Microsoft.AppCenter.Crashes" version="4.1.0" targetFramework="xamarinios10" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="5.0.0" targetFramework="xamarinios10" />
  <package id="Microsoft.CSharp" version="4.7.0" targetFramework="xamarinios10" />
  <package id="Microsoft.NETCore.Platforms" version="5.0.1" targetFramework="xamarinios10" />
  <package id="Microsoft.Win32.Primitives" version="4.3.0" targetFramework="xamarinios10" />
  <package id="NETStandard.Library" version="2.0.3" targetFramework="xamarinios10" />
  <package id="Newtonsoft.Json" version="12.0.3" targetFramework="xamarinios10" />
  <package id="OxyPlot.Core" version="2.0.0" targetFramework="xamarinios10" />
  <package id="OxyPlot.Xamarin.Forms" version="1.1.0-unstable0011" targetFramework="xamarinios10" />
  <package id="OxyPlot.Xamarin.iOS" version="1.1.0-unstable0011" targetFramework="xamarinios10" />
  <package id="particle.forms" version="1.0.0" targetFramework="xamarinios10" />
  <package id="Plugin.FirebasePushNotification" version="3.3.10" targetFramework="xamarinios10" />
  <package id="Plugin.GoogleClient" version="2.1.12" targetFramework="xamarinios10" />
  <package id="Plugin.InAppBilling" version="4.0.2" targetFramework="xamarinios10" />
  <package id="Plugin.StoreReview" version="6.0.0" targetFramework="xamarinios10" />
  <package id="Plugin.Toast" version="2.2.0" targetFramework="xamarinios10" />
  <package id="Rg.Plugins.Popup" version="2.0.0.10" targetFramework="xamarinios10" />
  <package id="SegmentedControl.FormsPlugin" version="2.0.1" targetFramework="xamarinios10" />
  <package id="Sentry" version="3.33.1" targetFramework="xamarinios10" />
  <package id="Sentry.Android.AssemblyReader" version="3.33.1" targetFramework="xamarinios10" />
  <package id="Sentry.Xamarin" version="1.5.2" targetFramework="xamarinios10" />
  <package id="Sentry.Xamarin.Forms" version="1.5.2" targetFramework="xamarinios10" />
  <package id="SkiaSharp" version="2.80.2" targetFramework="xamarinios10" />
  <package id="SkiaSharp.Views" version="2.80.2" targetFramework="xamarinios10" />
  <package id="SkiaSharp.Views.Forms" version="2.80.2" targetFramework="xamarinios10" />
  <package id="SlideOverKit" version="2.1.6.2" targetFramework="xamarinios10" />
  <package id="Splat" version="10.0.1" targetFramework="xamarinios10" />
  <package id="sqlite-net-pcl" version="1.4.118" targetFramework="xamarinios10" />
  <package id="SQLitePCLRaw.bundle_green" version="1.1.9" targetFramework="xamarinios10" />
  <package id="SQLitePCLRaw.core" version="1.1.9" targetFramework="xamarinios10" />
  <package id="SQLitePCLRaw.provider.sqlite3.ios_unified" version="1.1.9" targetFramework="xamarinios10" />
  <package id="System.AppContext" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Buffers" version="4.5.1" targetFramework="xamarinios10" />
  <package id="System.Collections" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Collections.Concurrent" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Collections.Immutable" version="5.0.0" targetFramework="xamarinios10" />
  <package id="System.ComponentModel" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.ComponentModel.TypeConverter" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Console" version="4.3.1" targetFramework="xamarinios10" />
  <package id="System.Diagnostics.Debug" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Diagnostics.Tools" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Diagnostics.Tracing" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Dynamic.Runtime" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Globalization" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Globalization.Calendars" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.IO" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.IO.Compression" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.IO.Compression.ZipFile" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.IO.FileSystem" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.IO.FileSystem.Primitives" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Linq" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Linq.Expressions" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Memory" version="4.5.4" targetFramework="xamarinios10" />
  <package id="System.Net.Http" version="4.3.4" targetFramework="xamarinios10" />
  <package id="System.Net.Primitives" version="4.3.1" targetFramework="xamarinios10" />
  <package id="System.Net.Sockets" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="xamarinios10" />
  <package id="System.ObjectModel" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Reflection" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Reflection.Extensions" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Reflection.Metadata" version="5.0.0" targetFramework="xamarinios10" />
  <package id="System.Reflection.Primitives" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Resources.ResourceManager" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Runtime" version="4.3.1" targetFramework="xamarinios10" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="xamarinios10" />
  <package id="System.Runtime.Extensions" version="4.3.1" targetFramework="xamarinios10" />
  <package id="System.Runtime.Handles" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Runtime.InteropServices" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Runtime.InteropServices.RuntimeInformation" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Runtime.Numerics" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Runtime.Serialization.Formatters" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Runtime.Serialization.Primitives" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.1" targetFramework="xamarinios10" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.2" targetFramework="xamarinios10" />
  <package id="System.Text.Encoding" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Text.Encoding.Extensions" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Text.Encodings.Web" version="5.0.1" targetFramework="xamarinios10" />
  <package id="System.Text.Json" version="5.0.2" targetFramework="xamarinios10" />
  <package id="System.Text.RegularExpressions" version="4.3.1" targetFramework="xamarinios10" />
  <package id="System.Threading" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Threading.Tasks" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Threading.Timer" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="xamarinios10" />
  <package id="System.Xml.ReaderWriter" version="4.3.1" targetFramework="xamarinios10" />
  <package id="System.Xml.XDocument" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Xml.XmlDocument" version="4.3.0" targetFramework="xamarinios10" />
  <package id="VG.XFShapeView" version="1.0.5" targetFramework="xamarinios10" />
  <package id="WebP.Touch" version="1.0.8" targetFramework="xamarinios10" />
  <package id="Xam.Plugin.Connectivity" version="3.2.0" targetFramework="xamarinios10" />
  <package id="Xam.Plugin.LatestVersion" version="2.1.0" targetFramework="xamarinios10" />
  <package id="Xam.Plugins.Settings" version="3.1.1" targetFramework="xamarinios10" />
  <package id="Xam.Plugins.Vibrate" version="4.0.0.5" targetFramework="xamarinios10" />
  <package id="Xamarin.Build.Download" version="0.11.3" targetFramework="xamarinios10" />
  <package id="Xamarin.Essentials" version="1.6.1" targetFramework="xamarinios10" />
  <package id="Xamarin.FFImageLoading" version="2.4.11.982" targetFramework="xamarinios10" />
  <package id="Xamarin.FFImageLoading.Forms" version="2.4.11.982" targetFramework="xamarinios10" />
  <package id="Xamarin.FFImageLoading.Svg" version="2.4.11.982" targetFramework="xamarinios10" />
  <package id="Xamarin.FFImageLoading.Svg.Forms" version="2.4.11.982" targetFramework="xamarinios10" />
  <package id="Xamarin.FFImageLoading.Transformations" version="2.4.11.982" targetFramework="xamarinios10" />
  <package id="Xamarin.Firebase.iOS.ABTesting" version="4.2.0" targetFramework="xamarinios10" />
  <package id="Xamarin.Firebase.iOS.Analytics" version="6.9.0" targetFramework="xamarinios10" />
  <package id="Xamarin.Firebase.iOS.CloudMessaging" version="4.7.1" targetFramework="xamarinios10" />
  <package id="Xamarin.Firebase.iOS.Core" version="6.10.4" targetFramework="xamarinios10" />
  <package id="Xamarin.Firebase.iOS.Crashlytics" version="4.6.2" targetFramework="xamarinios10" />
  <package id="Xamarin.Firebase.iOS.Installations" version="1.7.0" targetFramework="xamarinios10" />
  <package id="Xamarin.Firebase.iOS.InstanceID" version="4.8.0" targetFramework="xamarinios10" />
  <package id="Xamarin.Firebase.iOS.RemoteConfig" version="4.9.1" targetFramework="xamarinios10" />
  <package id="Xamarin.Forms" version="5.0.0.2515" targetFramework="xamarinios10" />
  <package id="Xamarin.Forms.PancakeView" version="2.3.0.759" targetFramework="xamarinios10" />
  <package id="Xamarin.Forms.Visual.Material" version="5.0.0.2515" targetFramework="xamarinios10" />
  <package id="Xamarin.Google.iOS.SignIn" version="5.0.2.1" targetFramework="xamarinios10" />
  <package id="Xamarin.iOS.MaterialComponents" version="92.0.0" targetFramework="xamarinios10" />
  <package id="Xamarin.Plugin.Calendar" version="1.4.5304" targetFramework="xamarinios10" />
</packages>