using NUnit.Framework;
using DrMuscle.UITests.Helpers;
using DrMuscle.UITests.Pages;
using System;
using System.Threading;
using System.Linq;
using OpenQA.Selenium.Appium;

namespace DrMuscle.UITests.Tests
{
    /// <summary>
    /// Comprehensive tests for Superset functionality
    /// Tests creating, executing, and managing supersets
    /// </summary>
    [TestFixture]
    [Category(TestCategories.AdvancedFeatures)]
    public class SupersetTests : AppiumSetup
    {
        private WorkoutPage workoutPage = null!;
        private SupersetPage supersetPage = null!;
        private TimerPage timerPage = null!;
        
        [SetUp]
        public void TestSetup()
        {
            workoutPage = new WorkoutPage(Driver!);
            supersetPage = new SupersetPage(Driver!);
            timerPage = new TimerPage(Driver!);
            
            // Login and start workout
            LoginAndStartWorkout();
        }
        
        [Test]
        [Order(1)]
        [Description("Tests creating a superset pair")]
        public void TEST_Create_Superset_Pair()
        {
            TestContext.WriteLine("=== Testing Superset Creation ===");
            
            // Start first exercise
            workoutPage.SelectExerciseByName("Bench Press");
            Thread.Sleep(2000);
            
            TakeScreenshot("01-first-exercise-selected");
            
            // Tap superset button
            supersetPage.OpenSupersetOptions();
            Thread.Sleep(1000);
            
            // First time users should see superset explanation
            if (supersetPage.IsSupersetTutorialVisible())
            {
                var tutorialText = supersetPage.GetTutorialText();
                TestContext.WriteLine($"Tutorial text: {tutorialText}");
                
                Assert.That(tutorialText, Does.Contain("superset"),
                    "Tutorial should explain what supersets are");
                
                TakeScreenshot("02-superset-tutorial");
                
                supersetPage.DismissTutorial();
                Thread.Sleep(500);
            }
            
            // Select second exercise for superset
            supersetPage.SearchExercise("Bent Over Row");
            Thread.Sleep(1000);
            
            var availableExercises = supersetPage.GetAvailableExercises();
            TestContext.WriteLine($"Available exercises: {string.Join(", ", availableExercises.Take(5))}");
            
            Assert.That(availableExercises, Does.Contain("Bent Over Row"),
                "Should be able to find the target exercise");
            
            supersetPage.SelectExerciseForSuperset("Bent Over Row");
            Thread.Sleep(1000);
            
            TakeScreenshot("03-superset-pair-created");
            
            // Verify superset indicator
            Assert.That(supersetPage.IsSupersetActive(), Is.True,
                "Superset indicator should be visible");
            
            var supersetInfo = supersetPage.GetSupersetInfo();
            TestContext.WriteLine($"Superset info: {supersetInfo}");
            
            Assert.That(supersetInfo, Does.Contain("Bench Press"),
                "Should show first exercise in superset");
            Assert.That(supersetInfo, Does.Contain("Bent Over Row"),
                "Should show second exercise in superset");
        }
        
        [Test]
        [Order(2)]
        [Description("Tests executing superset with alternating exercises")]
        public void TEST_Execute_Superset_Flow()
        {
            TestContext.WriteLine("=== Testing Superset Execution ===");
            
            // Create superset
            workoutPage.SelectExerciseByName("Dumbbell Curl");
            Thread.Sleep(2000);
            
            supersetPage.OpenSupersetOptions();
            supersetPage.SelectExerciseForSuperset("Tricep Extension");
            Thread.Sleep(1000);
            
            // Complete first set of exercise A
            TestContext.WriteLine("Completing first set of Dumbbell Curl...");
            workoutPage.EnterSet("12", "30");
            workoutPage.SaveSet();
            Thread.Sleep(2000);
            
            // Verify switches to exercise B
            var currentExercise = workoutPage.GetCurrentExerciseName();
            Assert.That(currentExercise, Does.Contain("Tricep Extension"),
                "Should automatically switch to second exercise in superset");
            
            TakeScreenshot("04-switched-to-exercise-b");
            
            // Verify minimal rest between superset exercises
            if (timerPage.IsTimerRunning())
            {
                var restTime = timerPage.GetRemainingTime();
                var seconds = ParseTimeToSeconds(restTime);
                TestContext.WriteLine($"Rest between superset exercises: {restTime}");
                
                Assert.That(seconds, Is.LessThanOrEqualTo(30),
                    "Rest between superset exercises should be minimal (≤30 seconds)");
            }
            
            // Complete first set of exercise B
            TestContext.WriteLine("Completing first set of Tricep Extension...");
            workoutPage.EnterSet("15", "25");
            workoutPage.SaveSet();
            Thread.Sleep(2000);
            
            // Verify switches back to exercise A
            currentExercise = workoutPage.GetCurrentExerciseName();
            Assert.That(currentExercise, Does.Contain("Dumbbell Curl"),
                "Should switch back to first exercise after completing both");
            
            TakeScreenshot("05-switched-back-to-exercise-a");
            
            // Verify full rest after completing superset round
            if (timerPage.IsTimerRunning())
            {
                var fullRestTime = timerPage.GetRemainingTime();
                var fullSeconds = ParseTimeToSeconds(fullRestTime);
                TestContext.WriteLine($"Rest after superset round: {fullRestTime}");
                
                Assert.That(fullSeconds, Is.GreaterThan(60),
                    "Should have full rest period after completing a superset round");
            }
            
            // Complete second round
            TestContext.WriteLine("Completing second round of superset...");
            
            // Set 2 of exercise A
            timerPage.SkipTimer();
            workoutPage.EnterSet("10", "30");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            // Set 2 of exercise B
            workoutPage.EnterSet("12", "25");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            TakeScreenshot("06-second-round-complete");
        }
        
        [Test]
        [Order(3)]
        [Description("Tests superset with very different weight exercises")]
        public void TEST_Superset_Different_Weights()
        {
            TestContext.WriteLine("=== Testing Superset with Different Weights ===");
            
            // Create superset with exercises requiring very different weights
            workoutPage.SelectExerciseByName("Squat");
            Thread.Sleep(2000);
            
            var squatWeight = workoutPage.GetRecommendedWeight();
            TestContext.WriteLine($"Squat recommended weight: {squatWeight}");
            
            supersetPage.OpenSupersetOptions();
            supersetPage.SelectExerciseForSuperset("Lateral Raise");
            Thread.Sleep(1000);
            
            // Complete squat set
            workoutPage.EnterSet("8", "225");
            workoutPage.SaveSet();
            Thread.Sleep(2000);
            
            // Verify weight recommendation changes for lateral raise
            var lateralRaiseWeight = workoutPage.GetRecommendedWeight();
            TestContext.WriteLine($"Lateral Raise recommended weight: {lateralRaiseWeight}");
            
            Assert.That(ParseWeight(lateralRaiseWeight), Is.LessThan(ParseWeight(squatWeight) / 5),
                "Lateral raise weight should be much lighter than squat");
            
            TakeScreenshot("07-different-weight-recommendations");
            
            // Test plate calculator for each
            if (workoutPage.HasPlateCalculator())
            {
                var plateCalcPage = new PlateCalculatorPage(Driver!);
                
                // Check plate calculation for lateral raise
                plateCalcPage.OpenPlateCalculator();
                Thread.Sleep(1000);
                
                var plateBreakdown = plateCalcPage.GetPlateBreakdown();
                TestContext.WriteLine($"Lateral raise plates: {plateBreakdown}");
                
                TakeScreenshot("08-lateral-raise-plates");
                
                plateCalcPage.CloseCalculator();
            }
            
            // Complete lateral raise
            workoutPage.EnterSet("12", "15");
            workoutPage.SaveSet();
            Thread.Sleep(2000);
            
            // Back to squat - verify weight is remembered
            var nextSquatWeight = workoutPage.GetRecommendedWeight();
            Assert.That(nextSquatWeight, Is.EqualTo("225"),
                "Should remember the weight used for squat");
            
            TakeScreenshot("09-weight-memory");
        }
        
        [Test]
        [Order(4)]
        [Description("Tests rest timing in supersets")]
        public void TEST_Superset_Rest_Timing()
        {
            TestContext.WriteLine("=== Testing Superset Rest Timing ===");
            
            // Create a superset
            workoutPage.SelectExerciseByName("Cable Row");
            supersetPage.OpenSupersetOptions();
            supersetPage.SelectExerciseForSuperset("Cable Fly");
            Thread.Sleep(1000);
            
            var restTimes = new List<int>();
            
            // Complete multiple rounds and track rest times
            for (int round = 1; round <= 3; round++)
            {
                TestContext.WriteLine($"\n--- Round {round} ---");
                
                // Exercise A
                workoutPage.EnterSet("12", "120");
                workoutPage.SaveSet();
                Thread.Sleep(1000);
                
                // Track rest between A->B
                if (timerPage.IsTimerRunning())
                {
                    var abRest = ParseTimeToSeconds(timerPage.GetRemainingTime());
                    restTimes.Add(abRest);
                    TestContext.WriteLine($"Rest A->B: {abRest} seconds");
                }
                
                timerPage.SkipTimer();
                
                // Exercise B
                workoutPage.EnterSet("15", "80");
                workoutPage.SaveSet();
                Thread.Sleep(1000);
                
                // Track rest after B (full rest)
                if (timerPage.IsTimerRunning())
                {
                    var fullRest = ParseTimeToSeconds(timerPage.GetRemainingTime());
                    TestContext.WriteLine($"Full rest after round: {fullRest} seconds");
                    
                    Assert.That(fullRest, Is.GreaterThan(restTimes.Last()),
                        "Full rest should be longer than transition rest");
                }
                
                if (round < 3)
                {
                    timerPage.SkipTimer();
                }
            }
            
            // Verify consistent rest timing
            var avgTransitionRest = restTimes.Average();
            TestContext.WriteLine($"Average transition rest: {avgTransitionRest} seconds");
            
            Assert.That(avgTransitionRest, Is.LessThanOrEqualTo(30),
                "Transition rest between superset exercises should be minimal");
            
            TakeScreenshot("10-superset-rest-pattern");
        }
        
        [Test]
        [Order(5)]
        [Description("Tests canceling superset mid-workout")]
        public void TEST_Cancel_Superset_Mid_Workout()
        {
            TestContext.WriteLine("=== Testing Superset Cancellation ===");
            
            // Create and start superset
            workoutPage.SelectExerciseByName("Leg Press");
            supersetPage.OpenSupersetOptions();
            supersetPage.SelectExerciseForSuperset("Calf Raise");
            Thread.Sleep(1000);
            
            // Complete one full round
            workoutPage.EnterSet("12", "400");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            workoutPage.EnterSet("20", "300");
            workoutPage.SaveSet();
            Thread.Sleep(2000);
            
            // Now cancel the superset
            TestContext.WriteLine("Canceling superset...");
            supersetPage.OpenSupersetOptions();
            
            Assert.That(supersetPage.IsCancelSupersetVisible(), Is.True,
                "Cancel superset option should be available");
            
            TakeScreenshot("11-cancel-superset-option");
            
            supersetPage.CancelSuperset();
            Thread.Sleep(1000);
            
            // Verify superset is cancelled
            Assert.That(supersetPage.IsSupersetActive(), Is.False,
                "Superset indicator should be removed");
            
            // Continue with normal sets
            workoutPage.EnterSet("10", "450");
            workoutPage.SaveSet();
            Thread.Sleep(2000);
            
            // Verify doesn't switch to other exercise
            var currentExercise = workoutPage.GetCurrentExerciseName();
            Assert.That(currentExercise, Does.Contain("Leg Press"),
                "Should stay on same exercise after canceling superset");
            
            TakeScreenshot("12-normal-mode-restored");
            
            // Finish this exercise and verify can do the other one separately
            workoutPage.FinishExercise();
            Thread.Sleep(1000);
            
            workoutPage.SelectExerciseByName("Calf Raise");
            Thread.Sleep(1000);
            
            // Verify can complete calf raises independently
            workoutPage.EnterSet("15", "320");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            Assert.That(supersetPage.IsSupersetActive(), Is.False,
                "Should not be in superset mode");
            
            TakeScreenshot("13-exercises-independent");
        }
        
        [Test]
        [Order(6)]
        [Description("Tests custom rest times for supersets")]
        public void TEST_Superset_Custom_Rest_Times()
        {
            TestContext.WriteLine("=== Testing Custom Rest Times in Supersets ===");
            
            // Configure custom rest times
            workoutPage.NavigateToSettings();
            var settingsPage = new SettingsPage(Driver!);
            
            settingsPage.OpenTimerSettings();
            settingsPage.SetSupersetTransitionTime("10"); // 10 seconds between exercises
            settingsPage.SetSupersetRoundRestTime("120"); // 2 minutes between rounds
            settingsPage.SaveTimerSettings();
            settingsPage.NavigateBack();
            
            TakeScreenshot("14-custom-superset-times");
            
            // Create superset and test custom times
            workoutPage.SelectExerciseByName("Pull-ups");
            supersetPage.OpenSupersetOptions();
            supersetPage.SelectExerciseForSuperset("Dips");
            Thread.Sleep(1000);
            
            // Complete first exercise
            workoutPage.EnterSet("8", "BW+25");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            // Verify custom transition time
            if (timerPage.IsTimerRunning())
            {
                var transitionTime = ParseTimeToSeconds(timerPage.GetRemainingTime());
                Assert.That(transitionTime, Is.InRange(8, 12),
                    "Should use custom 10-second transition time");
            }
            
            TakeScreenshot("15-custom-transition-time");
            
            // Complete second exercise
            timerPage.SkipTimer();
            workoutPage.EnterSet("10", "BW+45");
            workoutPage.SaveSet();
            Thread.Sleep(1000);
            
            // Verify custom round rest time
            if (timerPage.IsTimerRunning())
            {
                var roundRestTime = ParseTimeToSeconds(timerPage.GetRemainingTime());
                Assert.That(roundRestTime, Is.InRange(115, 125),
                    "Should use custom 2-minute round rest time");
            }
            
            TakeScreenshot("16-custom-round-rest");
        }
        
        [Test]
        [Order(7)]
        [Description("Tests tri-sets and giant sets")]
        public void TEST_TriSets_And_GiantSets()
        {
            TestContext.WriteLine("=== Testing Tri-Sets and Giant Sets ===");
            
            // Start with first exercise
            workoutPage.SelectExerciseByName("Shoulder Press");
            Thread.Sleep(2000);
            
            // Add second exercise
            supersetPage.OpenSupersetOptions();
            supersetPage.SelectExerciseForSuperset("Lateral Raise");
            Thread.Sleep(1000);
            
            // Try to add third exercise (tri-set)
            supersetPage.AddAnotherExercise();
            Thread.Sleep(1000);
            
            if (supersetPage.CanAddMoreExercises())
            {
                supersetPage.SelectExerciseForSuperset("Rear Delt Fly");
                Thread.Sleep(1000);
                
                var supersetInfo = supersetPage.GetSupersetInfo();
                TestContext.WriteLine($"Tri-set created: {supersetInfo}");
                
                Assert.That(supersetInfo, Does.Contain("3 exercises"),
                    "Should show tri-set with 3 exercises");
                
                TakeScreenshot("17-tri-set-created");
                
                // Test execution flow
                for (int exercise = 1; exercise <= 3; exercise++)
                {
                    workoutPage.EnterSet("12", "20");
                    workoutPage.SaveSet();
                    Thread.Sleep(1000);
                    
                    if (exercise < 3)
                    {
                        // Verify minimal rest between tri-set exercises
                        if (timerPage.IsTimerRunning())
                        {
                            timerPage.SkipTimer();
                        }
                    }
                }
                
                TakeScreenshot("18-tri-set-complete");
            }
            else
            {
                TestContext.WriteLine("App doesn't support tri-sets/giant sets");
                Assert.Pass("Tri-sets not supported in this version");
            }
        }
        
        private void LoginAndStartWorkout()
        {
            Thread.Sleep(3000);
            
            workoutPage.WaitForStartWorkout();
            workoutPage.StartWorkout();
            Thread.Sleep(2000);
            
            workoutPage.WaitForExerciseList();
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private int ParseTimeToSeconds(string timeString)
        #pragma warning restore CA1822 // Mark members as static
        {
            if (string.IsNullOrEmpty(timeString)) return 0;
            
            var parts = timeString.Split(':');
            if (parts.Length == 2)
            {
                if (int.TryParse(parts[0], out int minutes) && int.TryParse(parts[1], out int seconds))
                {
                    return minutes * 60 + seconds;
                }
            }
            
            if (int.TryParse(timeString.Replace("s", ""), out int totalSeconds))
            {
                return totalSeconds;
            }
            
            return 0;
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private double ParseWeight(string weightText)
        #pragma warning restore CA1822 // Mark members as static
        {
            var cleanWeight = weightText.Replace("lbs", "").Replace("kg", "").Replace("BW+", "").Replace("BW-", "").Trim();
            return double.TryParse(cleanWeight, out double weight) ? weight : 0;
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private void TakeScreenshot(string name)
        {
            TestTimings.TakeScreenshot(Driver, name);
        }
        #pragma warning restore CA1822 // Mark members as static
    }
}