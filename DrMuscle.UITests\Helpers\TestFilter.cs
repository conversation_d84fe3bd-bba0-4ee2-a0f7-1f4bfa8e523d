using System;
using NUnit.Framework;

namespace DrMuscle.UITests.Helpers
{
    /// <summary>
    /// Controls which tests run based on environment settings
    /// </summary>
    public static class TestFilter
    {
        /// <summary>
        /// Skip long-running tests in fast mode
        /// </summary>
        public static void SkipIfFastMode(string reason = "Skipped in fast mode")
        {
            if (TestConfiguration.UseFastMode)
            {
                Assert.Ignore($"{reason} - Enable full tests with UI_TEST_FAST_MODE=false");
            }
        }
        
        /// <summary>
        /// Skip extremely long tests unless explicitly enabled
        /// </summary>
        public static void SkipIfNotFullSuite(string reason = "Skipped - long running test")
        {
            var runFullSuite = Environment.GetEnvironmentVariable("UI_TEST_FULL_SUITE") == "true";
            if (!runFullSuite)
            {
                Assert.Ignore($"{reason} - Enable with UI_TEST_FULL_SUITE=true");
            }
        }
    }
}