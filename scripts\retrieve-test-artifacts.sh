#!/bin/bash

# Script to retrieve test artifacts from the self-hosted Mac runner
# Usage: ./retrieve-test-artifacts.sh [run-number]

# Configuration
RUNNER_HOST="*************"
RUNNER_USER="m1"
RUNNER_ARCHIVE_PATH="~/DrMuscleTestArchive"
LOCAL_ARCHIVE_PATH="./test-artifacts"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}DrMuscle Test Artifact Retrieval Tool${NC}"
echo "======================================"

# Check if run number provided
if [ -z "$1" ]; then
    echo -e "${YELLOW}Usage: $0 <run-number>${NC}"
    echo ""
    echo "Listing recent test runs on runner..."
    ssh ${RUNNER_USER}@${RUNNER_HOST} "find ${RUNNER_ARCHIVE_PATH} -name 'run-*' -type d | sort -r | head -10" 2>/dev/null
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to connect to runner. Check SSH access.${NC}"
        exit 1
    fi
    exit 0
fi

RUN_NUMBER=$1

# Find the run directory
echo "Searching for run $RUN_NUMBER..."
RUN_DIR=$(ssh ${RUNNER_USER}@${RUNNER_HOST} "find ${RUNNER_ARCHIVE_PATH} -name 'run-${RUN_NUMBER}-*' -type d | head -1" 2>/dev/null)

if [ -z "$RUN_DIR" ]; then
    echo -e "${RED}Run $RUN_NUMBER not found on runner${NC}"
    exit 1
fi

echo -e "${GREEN}Found run at: $RUN_DIR${NC}"

# Create local directory
LOCAL_RUN_DIR="${LOCAL_ARCHIVE_PATH}/run-${RUN_NUMBER}"
mkdir -p "$LOCAL_RUN_DIR"

# Download artifacts
echo "Downloading artifacts..."
scp -r ${RUNNER_USER}@${RUNNER_HOST}:"$RUN_DIR"/* "$LOCAL_RUN_DIR/" 2>/dev/null

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Artifacts downloaded successfully${NC}"
    echo ""
    echo "Downloaded to: $LOCAL_RUN_DIR"
    echo ""
    
    # Show summary
    if [ -f "$LOCAL_RUN_DIR/summary.txt" ]; then
        echo "Test Run Summary:"
        echo "================"
        cat "$LOCAL_RUN_DIR/summary.txt"
    fi
    
    # Count files
    echo ""
    echo "Downloaded files:"
    echo "- Test results: $(find "$LOCAL_RUN_DIR/TestResults" -name "*.trx" 2>/dev/null | wc -l)"
    echo "- Screenshots: $(find "$LOCAL_RUN_DIR" -name "*.png" 2>/dev/null | wc -l)"
    echo "- Log files: $(find "$LOCAL_RUN_DIR" -name "*.log" 2>/dev/null | wc -l)"
    
    # Show directory structure
    echo ""
    echo "Directory structure:"
    tree "$LOCAL_RUN_DIR" -L 2 2>/dev/null || ls -la "$LOCAL_RUN_DIR"
    
else
    echo -e "${RED}Failed to download artifacts${NC}"
    exit 1
fi

# Optional: Open directory in finder/explorer
if [[ "$OSTYPE" == "darwin"* ]]; then
    open "$LOCAL_RUN_DIR"
elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
    explorer "$(cygpath -w "$LOCAL_RUN_DIR")"
fi