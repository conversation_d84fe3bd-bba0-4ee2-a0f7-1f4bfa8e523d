using NUnit.Framework;
using System;
using System.Linq;
using Xamarin.UITest;

namespace DrMuscle.UITests.Tests
{
    [TestFixture]
    public class AlternativeAppLaunchTest
    {
        [Test]
        public void TryDifferentLaunchApproaches()
        {
            var isCI = Environment.GetEnvironmentVariable("CI") == "true";
            if (!isCI)
            {
                Assert.Ignore("This test is only for CI debugging");
            }
            
            var appPath = Environment.GetEnvironmentVariable("IOS_APP_BUNDLE_PATH");
            Console.WriteLine($"Testing with app path: {appPath}");
            
            // Try 1: Without device identifier
            try
            {
                Console.WriteLine("Attempt 1: Without device identifier");
                var app1 = ConfigureApp
                    .iOS
                    .AppBundle(appPath)
                    .StartApp();
                    
                if (app1 != null)
                {
                    Assert.Pass("Success with approach 1: No device identifier");
                }
            }
            catch (Exception ex1)
            {
                Console.WriteLine($"Approach 1 failed: {ex1.Message}");
            }
            
            // Try 2: With UDID instead of name
            try
            {
                Console.WriteLine("Attempt 2: Using booted device");
                var app2 = ConfigureApp
                    .iOS
                    .AppBundle(appPath)
                    .DeviceIdentifier("booted")
                    .StartApp();
                    
                if (app2 != null)
                {
                    Assert.Pass("Success with approach 2: booted device");
                }
            }
            catch (Exception ex2)
            {
                Console.WriteLine($"Approach 2 failed: {ex2.Message}");
            }
            
            // Try 3: Using InstalledApp with bundle ID
            try
            {
                Console.WriteLine("Attempt 3: Using InstalledApp");
                var app3 = ConfigureApp
                    .iOS
                    .InstalledApp("com.drmaxmuscle.max")
                    .DeviceIdentifier("iPhone 14")
                    .StartApp();
                    
                if (app3 != null)
                {
                    Assert.Pass("Success with approach 3: InstalledApp");
                }
            }
            catch (Exception ex3)
            {
                Console.WriteLine($"Approach 3 failed: {ex3.Message}");
            }
            
            Assert.Fail("All launch approaches failed");
        }
    }
}