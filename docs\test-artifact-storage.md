# Test Artifact Storage Configuration

## Overview
To reduce GitHub storage costs, test artifacts are now saved locally on the self-hosted Mac runner instead of being uploaded to GitHub Actions artifacts.

## Storage Location
All test artifacts are saved to:
```
~/DrMuscleTestArchive/
  └── YYYY-MM-DD/
      └── run-{number}-HH-MM-SS/
          ├── TestResults/
          │   ├── test-results.trx
          │   └── coverage.cobertura.xml
          ├── Screenshots/
          │   ├── app-launch.png
          │   ├── welcome-screen.png
          │   └── ...
          ├── TestOutputScreenshots/
          ├── CoverageReport/
          ├── logs/ (only on failure)
          └── summary.txt
```

## Features

### Automatic Archiving
- Test results, screenshots, and logs are automatically saved after each run
- Organized by date and run number for easy retrieval
- Summary file created with run metadata

### Automatic Cleanup
- Archives older than 30 days are automatically deleted
- Maximum storage limit of 10GB enforced
- Empty directories cleaned up

### Storage Savings
- Screenshots: ~1.5MB per run (10 screenshots)
- Test results: ~50KB per run
- Coverage reports: ~200KB per run
- **Estimated savings**: ~50GB/month with 30 runs/day

## Scripts

### retrieve-test-artifacts.sh
Download artifacts from a specific test run:
```bash
# List recent runs
./scripts/retrieve-test-artifacts.sh

# Download specific run
./scripts/retrieve-test-artifacts.sh 12345
```

### cleanup-test-artifacts.sh
Manage storage on the Mac runner:
```bash
# Run cleanup manually
ssh m1@*************
cd ~/DrMuscle
./scripts/cleanup-test-artifacts.sh

# Or set up cron job for automatic cleanup
crontab -e
# Add: 0 2 * * * ~/DrMuscle/scripts/cleanup-test-artifacts.sh
```

## Workflow Changes

### Before (GitHub Artifacts)
```yaml
- uses: actions/upload-artifact@v4
  with:
    name: test-results-${{ github.run_number }}
    path: TestResults/**/*
    retention-days: 30
```

### After (Local Storage)
```yaml
- name: Save Test Artifacts Locally
  run: |
    ARCHIVE_DIR="$HOME/DrMuscleTestArchive/$(date +%Y-%m-%d)"
    RUN_DIR="$ARCHIVE_DIR/run-${{ github.run_number }}-$(date +%H-%M-%S)"
    mkdir -p "$RUN_DIR"
    cp -r TestResults "$RUN_DIR/"
    cp -r Screenshots "$RUN_DIR/"
```

## Benefits

1. **Cost Savings**: No GitHub storage fees for test artifacts
2. **Faster Access**: Direct file system access on runner
3. **Better Organization**: Date-based directory structure
4. **Flexible Retention**: Easily adjust retention period
5. **Local Analysis**: Can analyze trends directly on runner

## Accessing Artifacts

### From Development Machine
```bash
# SSH to runner and browse
ssh m1@*************
cd ~/DrMuscleTestArchive
ls -la

# Use retrieve script
./scripts/retrieve-test-artifacts.sh 12345
```

### From CI Summary
Each workflow run shows the archive location in the job summary:
```
## 💾 Test Artifacts Saved Locally
Location: `/Users/<USER>/DrMuscleTestArchive/2024-06-29/run-12345-14-30-45`
Screenshots saved: 10
```

## Monitoring

Check storage usage:
```bash
ssh m1@************* "du -sh ~/DrMuscleTestArchive"
```

List recent runs:
```bash
ssh m1@************* "find ~/DrMuscleTestArchive -name 'run-*' -type d | sort -r | head -10"
```

## Migration Notes

- GitHub artifacts are no longer uploaded automatically
- Existing GitHub artifacts will expire based on their retention settings
- Local archives are permanent until cleaned up by retention policy
- Consider backing up important test results separately