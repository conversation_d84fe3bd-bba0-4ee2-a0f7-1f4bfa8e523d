# Dr. Muscle MAUI App - Automated Testing Documentation

## Overview

The Dr. Muscle MAUI app uses SimCtl-based UI testing for comprehensive test coverage across authentication, workout flows, and edge cases. Tests run on self-hosted Mac runners in GitHub Actions.

## Test Architecture

### Testing Framework
- **Platform**: .NET 8 with NUnit
- **UI Testing**: SimCtl (xcrun simctl) for iOS simulator control
- **Test Runner**: GitHub Actions with self-hosted Mac mini
- **Screenshot Validation**: Automated screenshot capture for visual validation

### Test Organization
```
DrMuscle.UITests/
├── Helpers/
│   ├── SimCtlTestRunner.cs      # Core SimCtl wrapper
│   ├── UITestBase.cs            # Base class for tests
│   ├── TestDataHelper.cs        # Test user management
│   ├── WorkoutTestData.cs       # Workout templates
│   └── FlakyTestRetryAttribute.cs # Retry mechanism
├── Tests/
│   ├── SimCtlSmokeTests.cs      # Basic infrastructure tests
│   ├── SimCtlAuthTests.cs       # Authentication flows
│   ├── SimCtlWorkoutFlowTests.cs # Complete workout tests
│   ├── SimCtlExerciseNavigationTests.cs # Exercise UI tests
│   ├── SimCtlSetRecordingTests.cs # Set entry tests
│   ├── SimCtlWorkoutCompletionTests.cs # Completion flows
│   ├── SimCtlDataValidationTests.cs # Data persistence
│   └── SimCtlEdgeCaseTests.cs   # Error scenarios
└── DrMuscle.UITests.csproj

## Phase 1: Authentication & Infrastructure (Completed)

### SimCtl Smoke Tests
- `SimCtlIsAvailable` - Verifies SimCtl availability on macOS
- `CanGetBootedDevice` - Tests device detection
- `CanTakeSimulatorScreenshot` - Validates screenshot capability
- `CanCheckAppInstallationStatus` - Tests app installation check
- `CanTakeMultipleScreenshots` - Tests rapid screenshot capture
- `SimulatorEnvironmentInfo` - Collects environment information

### Authentication Tests
- `LoginWithAppleID` - Tests Apple Sign In flow
- `LoginFailure` - Tests authentication error handling
- `RememberMeFlow` - Tests credential persistence
- `LogoutAndRelogin` - Tests logout functionality
- `MultipleLoginAttempts` - Tests retry scenarios

### Retry Demonstration Tests
- `DemoTest_FailsFirstTimeSucceedsOnRetry` - Tests retry mechanism
- `DemoTest_RandomlyFails` - Tests flaky test handling
- `DemoTest_CreatesTempFiles` - Tests file system operations
- `DemoTest_AlwaysFails` - Tests failure scenarios
- `DemoTest_ThrowsException` - Tests exception handling
- `AppLaunch_WithRetry_ShouldHandleTransientFailures` - Tests app launch retry
- `SimulatorInteraction_WithRetry_ShouldBeResilient` - Tests simulator resilience

## Phase 2: Workout Flow Testing (Completed)

### Test Data Infrastructure
- **TestDataHelper**: Manages test users and timing constants
  - Pre-configured test users (<EMAIL>)
  - Expected timing constants for operations
  - Test run ID generation

- **WorkoutTestData**: Provides workout templates
  - Default workout (Bench Press, Squats, Pull-ups)
  - Quick workout (single exercise)
  - Complex workout (5+ exercises)
  - Volume and duration calculations

### Workout Flow Tests
- `CompleteWorkoutHappyPath` - Full workout from start to finish
- `StartAndAbandonWorkout` - Tests workout abandonment
- `QuickWorkoutFlow` - Single exercise workout
- `CaptureWorkoutScreenStates` - Documents all UI states

### Exercise Navigation Tests
- `NavigateBetweenExercises` - Forward/back navigation
- `ExerciseDetailsAndInstructions` - Exercise info screens
- `ExerciseSubstitution` - Exercise replacement flow
- `ExerciseSkipAndReorder` - Skip and reorder functionality
- `SupersetNavigation` - Superset exercise flows

### Set Recording Tests
- `RecordBasicSet` - Basic weight/rep entry
- `RecordDifferentSetTypes` - Drop sets, rest-pause, failure sets
- `WeightAndRepPickers` - Picker UI interactions
- `RestTimerBehavior` - Rest timer states and skip
- `SetModificationAndDeletion` - Edit and delete sets

### Workout Completion Tests
- `CompleteFullWorkoutWithSummary` - Summary screen validation
- `WorkoutMetricsValidation` - Volume, sets, duration metrics
- `PersonalRecordDetection` - PR detection and display
- `WorkoutHistoryUpdate` - History persistence
- `WorkoutNotes` - Adding workout notes
- `ShareWorkoutSummary` - Social sharing functionality

### Data Validation Tests
- `ValidateWorkoutDataPersistence` - Cross-session persistence
- `ValidateExerciseHistory` - Exercise tracking over time
- `ValidateProgressTracking` - Progress charts and metrics
- `ValidateOfflineDataSync` - Offline/online sync
- `ValidateDataIntegrity` - Data accuracy checks
- `ValidateUserPreferences` - Settings persistence

### Edge Case Tests
- `RecoverFromAppCrashDuringWorkout` - Crash recovery
- `HandleBackgroundTermination` - Background state handling
- `EmptyWorkoutScenarios` - No exercises edge case
- `ExtremeDurationWorkout` - 2+ hour workouts
- `RapidActionStress` - Rapid UI interactions
- `InvalidDataEntry` - Invalid weight/rep entries
- `ConcurrentWorkoutAttempt` - Multiple workout prevention
- `MemoryPressureScenario` - Large workout handling

## Running Tests

### GitHub Actions (Automatic)
Tests run on every push to Development_MAUI_Carl_Automated_testing:
1. Build iOS app bundle
2. Install on simulator
3. Run all test suites
4. Capture screenshots
5. Generate JUnit test report

### Local Development (WSL/Linux)
```bash
# Run all tests
dotnet test DrMuscle.UITests/DrMuscle.UITests.csproj

# Run specific test suite
dotnet test --filter "FullyQualifiedName~SimCtlWorkoutFlowTests"

# Run with detailed output
dotnet test -v d --logger "console;verbosity=detailed"
```

### Remote Mac Execution
```bash
# SSH to Mac mini
ssh m1@*************

# Navigate to project
cd ~/DrMuscle

# Run tests
dotnet test DrMuscle.UITests/DrMuscle.UITests.csproj
```

## Test Data

### Test Users
- **Primary**: <EMAIL> (Password: Test123!)
- **Secondary**: <EMAIL>
- **Tertiary**: <EMAIL>

### Test Workouts
1. **UI Test Workout**: 3 exercises, 9 total sets
2. **Quick Test**: 1 exercise, 2 sets
3. **Complex Test Workout**: 5 exercises, 18 total sets

## Screenshot Organization
```
Screenshots/
├── Smoke/           # Infrastructure tests
├── Auth/            # Authentication flows
├── WorkoutFlow/     # Complete workout journeys
├── ExerciseNav/     # Exercise navigation
├── SetRecording/    # Weight/rep entry
├── WorkoutCompletion/ # Summary and completion
├── DataValidation/  # Data persistence checks
└── EdgeCases/       # Error scenarios
```

## Success Metrics

### Test Coverage
- ✅ 50+ SimCtl tests implemented
- ✅ 200+ screenshots captured per run
- ✅ All critical user paths covered
- ✅ Authentication flows validated
- ✅ Complete workout lifecycle tested
- ✅ Edge cases and error scenarios handled

### Quality Metrics
- Average test execution: <10 seconds per test
- Flaky test rate: <5% (with retry mechanism)
- Screenshot validation: 100% capture rate
- CI/CD integration: Fully automated

## Future Enhancements

### Phase 3 (Planned)
- Workout plan testing (multi-day programs)
- Advanced features (supersets, circuits, tempo)
- Performance testing (100+ exercise workouts)
- Apple Watch companion app integration
- Real device testing via Device Farm

### Technical Improvements
- Visual regression testing with screenshot comparison
- API mocking for deterministic tests
- Parallel test execution
- Test result trending and analytics

---

# Dr. Muscle Apple Watch App - Automated Tests

## Purpose

This document lists the automated tests implemented for the project and their current status. Tests should follow BDD-style (Given/When/Then) using XCTest.

## Instructions

*   Add tests here as they are created during the RED stage of the TDD cycle.
*   Update status from "Failing" to "Passing" during the DOCUMENTATION stage.
*   Group tests by feature or component.

## Test Suites

### Project Setup

*   `testAppInitialization` - Status: Passing
    * Tests that the app initializes correctly with SwiftUI App lifecycle
*   `testContentViewInitialization` - Status: Passing
    * Tests that the ContentView initializes correctly and displays the basic UI

### Add Set / Next Exercise Choice Screen

*   `testChoiceScreenAppearsAfterLastPlannedSet` - Status: Passing
    * Tests that the choice screen appears after saving the last planned set
*   `testChoiceScreenDoesNotAppearAfterNonLastSet` - Status: Passing
    * Tests that the choice screen does not appear after saving a non-last set
*   `testAddSetAction` - Status: Passing
    * Tests that tapping "Add Set" reverts to the Set Screen with the same values
*   `testAddSetPreservesRepsAndWeight` - Status: Passing
    * Tests that tapping "Add Set" preserves the reps and weight from the completed set
*   `testNextExerciseAction` - Status: Passing
    * Tests that tapping "Next Exercise" navigates to the next exercise
*   `testTimerExpiryBehavior` - Status: Passing
    * Tests that the timer expiry behavior works correctly

### Exercise Transition

*   `testNextExerciseActionShowsCheckmarkAnimation` - Status: Passing
    * Tests that the "Next Exercise" action shows the checkmark animation
*   `testCheckmarkAnimationCompletesAndNavigatesToNextExercise` - Status: Passing
    * Tests that when the checkmark animation completes, it navigates to the next exercise
*   `testSkippingInterExerciseRest` - Status: Passing
    * Tests that tapping "Next Exercise" during the rest timer skips the timer and shows the checkmark animation
*   `testInterExerciseRestTimerCompletes` - Status: Passing
    * Tests that when the inter-exercise rest timer completes, the choice screen remains visible
*   `testProceedingAfterTimerExpiry` - Status: Passing
    * Tests that tapping "Next Exercise" after the timer expires navigates to the next exercise
*   `testNavigationToWorkoutCompleteScreen` - Status: Passing
    * Tests that when the last exercise is completed, it navigates to the workout complete screen

### Workout Complete

*   `testWorkoutCompleteViewDisplaysCorrectContent` - Status: Passing
    * Tests that the workout complete view displays the correct content
*   `testFinishButtonCallsViewModel` - Status: Passing
    * Tests that tapping the finish button calls the view model's finishWorkout method
*   `testFinishWorkout` - Status: Passing
    * Tests that the finishWorkout method marks the workout as completed locally and syncs with the server
*   `testFinishWorkoutHandlesStorageError` - Status: Passing
    * Tests that the finishWorkout method handles storage errors gracefully
*   `testFinishWorkoutHandlesAPIError` - Status: Passing
    * Tests that the finishWorkout method handles API errors gracefully

### Authentication

#### AuthenticationManager Tests (AuthenticationManagerTests.swift)
*   `testAuthenticationManagerInitialState` - Status: Failing
    * Tests that the authentication manager initializes with no authenticated user
*   `testHandleSuccessfulSignInWithApple` - Status: Failing
    * Tests that the authentication manager can handle a successful Sign in with Apple
*   `testHandleSignInWithAppleError` - Status: Failing
    * Tests that the authentication manager handles Sign in with Apple errors correctly
*   `testStoreAndRetrieveAuthState` - Status: Failing
    * Tests that the authentication manager can store and retrieve authentication state securely
*   `testClearAuthState` - Status: Failing
    * Tests that the authentication manager can clear stored authentication state
*   `testAuthorizationControllerDidCompleteWithAuthorization` - Status: Failing
    * Tests the ASAuthorizationControllerDelegate method for successful authorization
*   `testAuthorizationControllerDidCompleteWithError` - Status: Failing
    * Tests the ASAuthorizationControllerDelegate method for authorization errors

#### LoginView Tests (LoginViewTests.swift)
*   `testLoginViewDisplaysSignInWithAppleButton` - Status: Failing
    * Tests that the login view displays the Sign in with Apple button
*   `testSignInWithAppleButtonHasCorrectStyle` - Status: Failing
    * Tests that the Sign in with Apple button follows watchOS styling guidelines
*   `testTappingSignInWithAppleButtonInitiatesSignIn` - Status: Failing
    * Tests that tapping the Sign in with Apple button initiates the sign-in process
*   `testLoginViewDoesNotShowQuickSignInButton` - Status: Failing
    * Tests that the mock "Quick Sign In" button has been removed
*   `testLoginViewShowsAppBranding` - Status: Failing
    * Tests that the login view displays app logo and name
*   `testLoginViewShowsErrorMessage` - Status: Failing
    * Tests that the login view displays error messages when authentication fails
*   `testLoginViewShowsLoadingIndicatorDuringAuth` - Status: Passing
    * Tests that the login view shows loading state during authentication
*   `testSignInButtonDisabledDuringAuthentication` - Status: Passing
    * Tests that the Sign in button is disabled while authenticating
*   `testLoginViewDisplaysAICoachTitle` - Status: Passing
    * Tests that the login view displays "AI Coach" as the main title instead of "IT'S TIME TO LIFT"
*   `testLoginViewDisplaysRealGainsSubtitle` - Status: Passing
    * Tests that the login view displays "Real Gains" as the subtitle
*   `testLoginViewTitleStyling` - Status: Passing
    * Tests that the login view maintains the Nike-style heavy font for the title

#### LoginViewModel Tests (LoginViewModelTests.swift)
*   `testLoginViewModelInitialState` - Status: Failing
    * Tests that the login view model initializes with correct default state
*   `testStartSignInWithApple` - Status: Failing
    * Tests that starting Sign in with Apple sets the correct state
*   `testHandleSuccessfulSignIn` - Status: Failing
    * Tests handling of successful Sign in with Apple credential
*   `testHandleSignInCancellation` - Status: Failing
    * Tests that user cancellation is handled gracefully without error
*   `testHandleSignInError` - Status: Failing
    * Tests that Sign in with Apple errors are displayed appropriately
*   `testHandleNetworkError` - Status: Failing
    * Tests that network errors show appropriate messages
*   `testHandleBackendValidationSuccess` - Status: Failing
    * Tests successful backend validation of Apple credentials
*   `testHandleBackendValidationError` - Status: Failing
    * Tests handling of backend validation errors
*   `testCreateAuthorizationRequest` - Status: Failing
    * Tests that authorization requests are properly configured
*   `testShouldRemoveQuickSignInMethod` - Status: Failing
    * Tests that the mock quickSignIn method has been removed

### API Client

*   `testAPIClientInitialization` - Status: Passing
    * Tests that the API client initializes correctly with the correct base URL
*   `testTokenStorageAndRetrieval` - Status: Passing
    * Tests that the API client can store and clear authentication tokens

### Authentication Integration Tests (January 27, 2025)

*   `testOAuthTokenEndpointFormat` - Status: Passing
    * Tests that OAuth /token endpoint format matches iOS app implementation
*   `testParameterOrderAndEncoding` - Status: Passing
    * Tests that parameters are in correct order: grant_type, accesstoken, provider, email, name, bodyweight, massunit, userid
*   `testAPIClientImplementation` - Status: Passing
    * Tests that DrMuscleAPIClient signInWithApple method matches apple-auth-integration.md requirements
*   `testLiveAPIEndpoint` - Status: Passing
    * Tests connectivity to actual Dr. Muscle authentication API endpoint
*   `testDocumentationCompliance` - Status: Passing
    * Tests that implementation matches documented authentication flow

### Workout List

*   `testFetchWorkoutsSuccess` - Status: Passing
    * Tests that the WorkoutListViewModel can fetch workouts from the API successfully
*   `testFetchWorkoutsEmpty` - Status: Passing
    * Tests that the WorkoutListViewModel handles empty workout lists correctly
*   `testFetchWorkoutsError` - Status: Passing
    * Tests that the WorkoutListViewModel handles API errors correctly
*   `testInitialState` - Status: Passing
    * Tests that the WorkoutListViewModel initializes with the correct state
*   `testWorkoutListViewDisplaysWorkouts` - Status: Passing
    * Tests that the WorkoutListView displays workouts correctly
*   `testWorkoutListViewDisplaysEmptyState` - Status: Passing
    * Tests that the WorkoutListView displays the empty state correctly
*   `testWorkoutListViewDisplaysErrorState` - Status: Passing
    * Tests that the WorkoutListView displays the error state correctly
*   `testWorkoutListViewDisplaysLoadingState` - Status: Passing
    * Tests that the WorkoutListView displays the loading state correctly

### Core Data Storage

*   `testCoreDataModelInitialization` - Status: Passing
    * Tests that the Core Data model initializes correctly with the proper entities
*   `testSavingWorkout` - Status: Passing
    * Tests saving a workout to Core Data and retrieving it
*   `testSavingExercise` - Status: Passing
    * Tests saving an exercise to Core Data and retrieving it
*   `testSavingSetLog` - Status: Passing
    * Tests saving a set log to Core Data and retrieving it
*   `testFetchingWithPredicates` - Status: Passing
    * Tests fetching data with various predicates for filtering
*   `testPersistenceControllerInitialization` - Status: Passing
    * Tests that the PersistenceController initializes correctly with the proper configuration

### Storage Service

*   `testSaveAndRetrieveWorkout` - Status: Passing
    * Tests saving and retrieving a workout using the StorageService
*   `testSaveAndRetrieveExercise` - Status: Passing
    * Tests saving and retrieving an exercise using the StorageService
*   `testSaveAndRetrieveSetLog` - Status: Passing
    * Tests saving and retrieving a set log using the StorageService
*   `testFetchSetLogsNeedingSync` - Status: Passing
    * Tests fetching set logs that need to be synced with the server
*   `testMarkSetLogAsSynced` - Status: Passing
    * Tests marking a set log as synced
*   `testDeleteWorkout` - Status: Passing
    * Tests deleting a workout and its associated exercises and set logs

### Workout Detail

*   `testFetchWorkoutDetailsSuccess` - Status: Passing
    * Tests that the WorkoutDetailViewModel can fetch workout details from the API successfully
*   `testFetchWorkoutDetailsError` - Status: Passing
    * Tests that the WorkoutDetailViewModel handles API errors correctly
*   `testStartWorkoutSuccess` - Status: Passing
    * Tests that the WorkoutDetailViewModel can start a workout successfully
*   `testStartWorkoutWithoutWorkout` - Status: Passing
    * Tests that the WorkoutDetailViewModel handles starting a workout without loaded workout details
*   `testWorkoutDetailViewDisplaysWorkoutDetails` - Status: Passing
    * Tests that the WorkoutDetailView displays workout details correctly
*   `testWorkoutDetailViewDisplaysErrorState` - Status: Passing
    * Tests that the WorkoutDetailView displays the error state correctly
*   `testWorkoutDetailViewDisplaysLoadingState` - Status: Passing
    * Tests that the WorkoutDetailView displays the loading state correctly

### Set Screen

*   `testFetchExerciseDetailsSuccess` - Status: Passing
    * Tests that the SetViewModel can fetch exercise details from the API successfully
*   `testFetchExerciseDetailsError` - Status: Passing
    * Tests that the SetViewModel handles API errors correctly
*   `testMoveToNextSet` - Status: Passing
    * Tests that the SetViewModel can move to the next set correctly
*   `testMoveToNextSetAtEnd` - Status: Passing
    * Tests that the SetViewModel handles moving to the next set when at the end of the sets
*   `testUpdateReps` - Status: Passing
    * Tests that the SetViewModel can update the current reps correctly
*   `testUpdateWeight` - Status: Passing
    * Tests that the SetViewModel can update the current weight correctly
*   `testComputedProperties` - Status: Passing
    * Tests that the SetViewModel's computed properties work correctly
*   `testSaveCurrentSet` - Status: Passing
    * Tests that the SetViewModel can save the current set data to local storage
*   `testSaveCurrentSetWithRIR` - Status: Passing
    * Tests that the SetViewModel can save the current set data with RIR to local storage
*   `testSetViewDisplaysExerciseDetails` - Status: Passing
    * Tests that the SetView displays exercise details correctly
*   `testSetViewDisplaysErrorState` - Status: Passing
    * Tests that the SetView displays the error state correctly
*   `testSetViewDisplaysLoadingState` - Status: Passing
    * Tests that the SetView displays the loading state correctly
*   `testWeightPickerUpdatesViewModel` - Status: Passing
    * Tests that the weight picker updates the view model correctly when a new weight is selected

### RIR Picker

*   `testRIRPickerInitialState` - Status: Passing
    * Tests that the RIRPicker component initializes correctly with the provided initial RIR value
*   `testRIRPickerOptions` - Status: Passing
    * Tests that the RIRPicker component has the correct descriptive options with the correct values
*   `testRIRPickerSelection` - Status: Passing
    * Tests that the RIRPicker component correctly handles selection of an option
*   `testRIRPickerDisplayedForFirstWorkSet` - Status: Passing
    * Tests that the RIR picker is displayed only for the first work set
*   `testRIRPickerNotDisplayedForWarmupSet` - Status: Passing
    * Tests that the RIR picker is not displayed for warmup sets
*   `testRIRPickerNotDisplayedForSubsequentWorkSet` - Status: Passing
    * Tests that the RIR picker is not displayed for subsequent work sets
*   `testSaveSetWithRIRValue` - Status: Passing
    * Tests that the RIR value is saved correctly with the set
*   `testHandleMissingAPIFlagGracefully` - Status: Passing
    * Tests that missing API flags are handled gracefully
*   `testShouldShowRIRPickerLogic` - Status: Passing
    * Tests the shouldShowRIRPicker method with various combinations of isFirstWorkSet and isWarmup

### Timer Functionality

*   `testTimerButtonInitialState` - Status: Passing
    * Tests that the TimerButton component initializes correctly with the provided initial values
*   `testTimerButtonTimerActiveState` - Status: Passing
    * Tests that the TimerButton component correctly displays the timer when active
*   `testTimerButtonFormatting` - Status: Passing
    * Tests that the TimerButton component correctly formats different time values
*   `testSetViewModelTimerInitialization` - Status: Passing
    * Tests that the SetViewModel correctly initializes timer values from a set recommendation
*   `testSetViewTimerActivation` - Status: Passing
    * Tests that the SetViewModel correctly activates the timer
*   `testTimerCountdown` - Status: Passing
    * Tests that the timer correctly counts down each second
*   `testTimerCompletion` - Status: Passing
    * Tests that the timer correctly completes when it reaches zero
*   `testSkipTimer` - Status: Passing
    * Tests that the timer can be skipped by the user

### Performance Calculation

*   `testPerformanceCalculationWithNoHistoricalData` - Status: Passing
    * Tests that performance percentage is calculated correctly when no historical data is available
*   `testPerformanceCalculationWithHistoricalData` - Status: Passing
    * Tests that performance percentage is calculated correctly when historical data is available
*   `testPositivePerformanceChange` - Status: Passing
    * Tests that positive performance changes are calculated correctly
*   `testNegativePerformanceChange` - Status: Passing
    * Tests that negative performance changes are calculated correctly
*   `testNoPerformanceChange` - Status: Passing
    * Tests that zero performance change is calculated correctly
*   `testPerformanceRecalculationAfterRepsChange` - Status: Passing
    * Tests that performance percentage is recalculated correctly when reps change
*   `testPerformanceRecalculationAfterWeightChange` - Status: Passing
    * Tests that performance percentage is recalculated correctly when weight changes
*   `testOneRMCalculationUsingEpleyFormula` - Status: Passing
    * Tests that one-rep max is calculated correctly using the Epley formula

### Offline Functionality

*   `testStartWorkoutRequiresConnection` - Status: Passing
    * Tests that starting a workout requires network connectivity
*   `testWorkoutInProgressContinuesOffline` - Status: Passing
    * Tests that a workout in progress can continue if connection is lost
*   `testLoseConnectionDuringRestTimer` - Status: Passing
    * Tests that the app can handle losing connection during rest timer
*   `testLoseConnectionDuringChoiceScreen` - Status: Passing
    * Tests that the app can handle losing connection during the "Add Set" / "Next Exercise" choice screen
*   `testCompleteWorkoutOffline` - Status: Passing
    * Tests that a workout can be completed offline

### HealthKit Integration

*   `testRequestAuthorizationOnFirstWorkoutStart` - Status: Passing
    * Tests that the HealthKitService requests authorization on first workout start
*   `testAuthorizationGranted` - Status: Passing
    * Tests that the HealthKitService handles authorization granted
*   `testAuthorizationDenied` - Status: Passing
    * Tests that the HealthKitService handles authorization denied
*   `testAuthorizationAlreadyDetermined` - Status: Passing
    * Tests that the HealthKitService handles authorization already determined
*   `testHealthKitUnavailable` - Status: Passing
    * Tests that the HealthKitService handles unavailable HealthKit
*   `testStartWorkoutSessionSuccessfully` - Status: Passing
    * Tests that the HealthKitService starts a workout session successfully
*   `testStartWorkoutSessionFailure` - Status: Passing
    * Tests that the HealthKitService handles workout session start failure
*   `testEndWorkoutSessionSuccessfully` - Status: Passing
    * Tests that the HealthKitService ends a workout session successfully
*   `testEndWorkoutSessionFailure` - Status: Passing
    * Tests that the HealthKitService handles workout session end failure
*   `testEndNonExistentWorkoutSession` - Status: Passing
    * Tests that the HealthKitService handles ending a non-existent workout session
*   `testEndWorkoutCollectionSuccess` - Status: Passing
    * Tests that the HealthKitService ends workout collection successfully
*   `testFinishWorkoutSuccess` - Status: Passing
    * Tests that the HealthKitService finishes workout successfully
*   `testCleanupWorkoutResources` - Status: Passing
    * Tests that the HealthKitService cleans up workout resources properly
*   `testCreateWorkoutConfiguration` - Status: Passing
    * Tests that the HealthKitService creates a workout configuration with the correct activity type and location type
*   `testEndExistingWorkoutSessionIfNeeded` - Status: Passing
    * Tests that the HealthKitService ends any existing workout session before starting a new one
*   `testSaveWorkoutDataWithEnergyAndHeartRate` - Status: Passing
    * Tests that the HealthKitService saves workout data to HealthKit with energy burned and heart rate
*   `testSaveWorkoutDataWithoutEnergyAndHeartRate` - Status: Passing
    * Tests that the HealthKitService saves workout data to HealthKit without energy burned or heart rate when permissions are denied

# Dr. Muscle Watch App - Testing Documentation

## Overview

The Dr. Muscle Watch App uses a comprehensive testing strategy with both unit/integration tests and end-to-end UI tests to ensure reliable authentication and user experience.

## Test Types

### 1. Unit/Integration Tests (`DrMuscleWatchAppTests`)

Located in: `DrMuscleWatch/v1/DrMuscleWatchAppTests/`

**Purpose**: Test individual components and API integration
**Framework**: XCTest
**Target**: DrMuscleWatchAppTests

#### Test Files:
- `AuthenticationIntegrationTests.swift` - API parameter formatting, response decoding
- `APIMockTests.swift` - Mock API responses and error handling

#### What's Tested:
- OAuth parameter order and encoding
- API request/response format validation
- Error response parsing
- Backend endpoint connectivity

### 2. End-to-End UI Tests (`DrMuscleWatchAppUITests`)

Located in: `DrMuscleWatch/v1/DrMuscleWatchApp/DrMuscleWatchAppUITests/`

**Purpose**: Test complete user login flow through the actual UI
**Framework**: XCUITest
**Target**: DrMuscleWatchAppUITests

#### Test Files:
- `LoginFlowUITests.swift` - Complete login flow scenarios

#### What's Tested:
- ✅ Complete Apple Sign In flow (button tap → authentication → workouts)
- ✅ Error handling (network errors, auth failures)
- ✅ Already authenticated user flow (skip login)
- ✅ Loading states and UI feedback
- ✅ Accessibility compliance
- ✅ Performance metrics

## Running Tests

### Via GitHub Actions (Automatic)

Tests run automatically on every push to Development_Watch_Carl_v1.2 branch:

1. **Unit/Integration Tests**: Run first, test API components
2. **End-to-End UI Tests**: Run after unit tests, test complete user flow
3. **Results**: Displayed in GitHub Actions Summary with pass/fail counts

### Via Xcode (Manual)

#### Run Unit Tests:
```bash
xcodebuild test \
  -project DrMuscleWatchApp.xcodeproj \
  -scheme DrMuscleWatchApp \
  -destination 'platform=watchOS Simulator,name=Apple Watch Series 9 (45mm)' \
  -only-testing:DrMuscleWatchAppTests
```

#### Run UI Tests:
```bash
xcodebuild test \
  -project DrMuscleWatchApp.xcodeproj \
  -scheme DrMuscleWatchApp \
  -destination 'platform=watchOS Simulator,name=Apple Watch Series 9 (45mm)' \
  -only-testing:DrMuscleWatchAppUITests
```

#### Run All Tests:
```bash
xcodebuild test \
  -project DrMuscleWatchApp.xcodeproj \
  -scheme DrMuscleWatchApp \
  -destination 'platform=watchOS Simulator,name=Apple Watch Series 9 (45mm)'
```

## UI Test Architecture

### Test Modes

The UI tests use special launch arguments and environment variables for reliable testing:

#### Launch Arguments:
- `-UITestMode`: Enables test mode
- `-ResetAuthState`: Start with clean authentication state
- `-AppleSignInMockMode`: Enable Apple Sign In mocking
- `-MockExistingAuth`: Simulate already authenticated user

#### Environment Variables:
- `UITEST_APPLE_SIGNIN_RESULT`: `success`, `error`, `network_error`, `delayed_success`
- `UITEST_USER_EMAIL`: Test email address
- `UITEST_USER_NAME`: Test user name
- `UITEST_ERROR_MESSAGE`: Custom error message for testing
- `UITEST_DELAY_SECONDS`: Delay for testing loading states

### Mock Implementation

The `AuthenticationManager` detects UI test mode and provides mock responses instead of real Apple Sign In:

```swift
// In AuthenticationManager.swift
if ProcessInfo.processInfo.arguments.contains("-UITestMode") {
    handleMockAppleSignIn()
    return
}
```

This allows testing all scenarios without depending on external services.

## Test Scenarios

### 1. Successful Login Flow
- **Test**: `testCompleteSuccessfulLoginFlow()`
- **Scenario**: User taps Apple Sign In → successful auth → workouts screen
- **Verifies**: Complete happy path works end-to-end

### 2. Authentication Error
- **Test**: `testLoginFlowWithError()`
- **Scenario**: User taps Apple Sign In → auth error → error message shown
- **Verifies**: Error handling and user feedback

### 3. Network Error
- **Test**: `testNetworkErrorDuringLogin()`
- **Scenario**: User taps Apple Sign In → network error → retry option
- **Verifies**: Network error handling and recovery

### 4. Already Authenticated
- **Test**: `testAlreadyAuthenticatedUser()`
- **Scenario**: App launch with existing auth → skip login → workouts
- **Verifies**: Authentication persistence works

### 5. Loading States
- **Test**: `testLoadingStateDuringAuthentication()`
- **Scenario**: User taps Apple Sign In → loading indicator → success
- **Verifies**: Loading states and user feedback during auth

### 6. Accessibility
- **Test**: `testLoginFlowAccessibility()`
- **Scenario**: Check all UI elements have proper accessibility labels
- **Verifies**: VoiceOver and accessibility support

### 7. Performance
- **Test**: `testLoginFlowPerformance()`
- **Scenario**: Measure app launch and authentication response times
- **Verifies**: Performance metrics within acceptable ranges

## GitHub Actions Integration

### Workflow Configuration

The GitHub Actions workflow (`apple-watch-build-workflow.yml`) includes:

1. **Authentication Integration Tests** - API-level testing
2. **End-to-End UI Tests** - Complete user flow testing
3. **Test Results Summary** - Formatted results in PR/Action summary

### Test Results in GitHub Summary

The workflow automatically generates a comprehensive test summary showing:

```markdown
## 🧪 Authentication Test Results
✅ All tests passed!

## 🧪 End-to-End UI Test Results
✅ All UI tests passed!

### UI Test Summary
|Total|Passed|Failed|
|-----|------|------|
|7    |7     |0     |

✅ Complete login flow working correctly - all user scenarios passed
```

## Adding New Tests

### Adding Unit Tests

1. Add test methods to existing test classes in `DrMuscleWatchAppTests/`
2. Follow naming convention: `testSomethingSpecific()`
3. Use XCTAssert methods for verification
4. Tests run automatically in CI

### Adding UI Tests

1. Add test methods to `LoginFlowUITests.swift`
2. Use XCUIApplication and XCUIElement for UI interaction
3. Set up test environment using launch arguments/environment
4. Follow pattern:
   ```swift
   func testNewScenario() throws {
       // GIVEN: Set up test environment
       app.launchEnvironment["UITEST_APPLE_SIGNIN_RESULT"] = "custom_scenario"
       app.launch()
       
       // WHEN: User performs action
       let button = app.buttons["Some Button"]
       button.tap()
       
       // THEN: Verify expected result
       let expectedElement = app.staticTexts["Expected Text"]
       XCTAssertTrue(expectedElement.waitForExistence(timeout: 10))
   }
   ```

## Test Data Management

### Test Isolation

- Each UI test starts with clean state via `-ResetAuthState`
- Tests don't interfere with each other
- Mock data is generated per test run

### Test Credentials

- No real credentials used in UI tests
- Mock data prevents external service dependencies
- Tests are deterministic and reliable

## Debugging Tests

### Viewing Test Logs

1. **In Xcode**: Test navigator → Right-click test → View in Report Navigator
2. **In GitHub Actions**: Check "Test Output Summary" section in workflow
3. **Local Development**: Console output includes detailed test logs

### Common Issues

#### UI Tests Not Running
- **Cause**: UI test target not configured in Xcode project
- **Solution**: Add `DrMuscleWatchAppUITests` target to Xcode project

#### Tests Failing in CI but Passing Locally
- **Cause**: Simulator differences or timing issues
- **Solution**: Increase timeout values, check simulator compatibility

#### Mock Authentication Not Working
- **Cause**: Launch arguments not properly set
- **Solution**: Verify `app.launchArguments` includes `-UITestMode`

## Future Enhancements

### Email/Password Testing
When email/password authentication is implemented:
- Add UI tests for email/password login flow
- Test switching between Apple Sign In and email/password
- Test form validation and error states

### Integration Testing
- Add tests that interact with staging backend
- Test real network requests (non-mock)
- Add tests for API versioning and compatibility

### Device Testing
- Expand to test on different watch sizes
- Test on different iOS/watchOS version combinations
- Add tests for various network conditions

## Test Metrics

### Coverage Goals
- **Unit Tests**: 80%+ coverage of authentication logic
- **UI Tests**: 100% coverage of critical user flows
- **Integration Tests**: All API endpoints tested

### Performance Targets
- **App Launch**: < 3 seconds
- **Authentication**: < 5 seconds
- **Navigation**: < 1 second between screens

### Quality Gates
- All tests must pass before merge
- No new tests should be flaky
- Critical paths must have both unit and UI tests