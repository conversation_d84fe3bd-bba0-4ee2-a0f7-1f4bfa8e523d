using NUnit.Framework;
using System;
using System.Diagnostics;
using System.IO;
using System.Threading;
using DrMuscle.UITests.Helpers;

namespace DrMuscle.UITests.Tests
{
    /// <summary>
    /// Network simulation tests using SimCtl
    /// Tests app behavior under various network conditions
    /// </summary>
    [TestFixture]
    public class SimCtlNetworkTests
    {
        private string screenshotDir = string.Empty;
        private readonly string appPath;

        public SimCtlNetworkTests()
        {
            // Try to get app path from environment variable first (set by CI)
            var envAppPath = Environment.GetEnvironmentVariable("IOS_APP_BUNDLE_PATH");
            if (!string.IsNullOrEmpty(envAppPath))
            {
                appPath = envAppPath;
                TestContext.WriteLine($"Using app path from environment: {appPath}");
            }
            else
            {
                // Fallback to searching for the app bundle
                var possiblePaths = new[]
                {
                    Path.Combine(TestContext.CurrentContext.TestDirectory, "..", "..", "..", "..", "..", "DrMaxMuscle", "bin", "Release", "net8.0-ios", "iossimulator-x64", "DrMaxMuscle.app"),
                    Path.Combine(TestContext.CurrentContext.TestDirectory, "..", "..", "..", "..", "..", "DrMaxMuscle", "bin", "Release", "net8.0-ios", "DrMaxMuscle.app"),
                    Path.Combine(TestContext.CurrentContext.TestDirectory, "..", "..", "..", "..", "..", "DrMaxMuscle", "bin", "iPhone", "Release", "DrMaxMuscle.app"),
                };

                foreach (var path in possiblePaths)
                {
                    if (Directory.Exists(path))
                    {
                        appPath = path;
                        TestContext.WriteLine($"Found app at: {appPath}");
                        break;
                    }
                }

                if (string.IsNullOrEmpty(appPath))
                {
                    appPath = Path.Combine(TestContext.CurrentContext.TestDirectory, "..", "..", "..", "..", "..", "DrMaxMuscle.app");
                    TestContext.WriteLine($"Warning: Using fallback app path: {appPath}");
                }
            }
        }

        [SetUp]
        public void Setup()
        {
            screenshotDir = Path.Combine(Directory.GetCurrentDirectory(), "Screenshots", "Network");
            Directory.CreateDirectory(screenshotDir);
            
            // Reset network to normal conditions
            ResetNetworkConditions();
            
            // Ensure app is installed
            if (!SimCtlTestRunner.IsAppInstalled())
            {
                if (File.Exists(appPath) || Directory.Exists(appPath))
                {
                    SimCtlTestRunner.InstallApp(appPath);
                }
                else
                {
                    TestContext.WriteLine($"Warning: App not found at {appPath}");
                }
            }
        }

        [TearDown]
        public void TearDown()
        {
            // Terminate app using simctl directly
            try
            {
                var deviceId = SimCtlTestRunner.GetBootedDeviceId();
                using var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "xcrun",
                        Arguments = $"simctl terminate {deviceId} com.drmaxmuscle.max",
                        UseShellExecute = false,
                        CreateNoWindow = true
                    }
                };
                process.Start();
                process.WaitForExit();
            }
            catch (Exception ex)
            {
                TestContext.WriteLine($"Warning: Could not terminate app: {ex.Message}");
            }
            
            ResetNetworkConditions();
        }

        private static void SetNetworkCondition(string condition)
        {
            try
            {
                // Use simctl to set network conditions
                var startInfo = new ProcessStartInfo
                {
                    FileName = "xcrun",
                    Arguments = $"simctl status_bar booted override --network {condition}",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using var process = Process.Start(startInfo);
                if (process != null)
                {
                    process.WaitForExit();
                    TestContext.WriteLine($"Network condition set to: {condition}");
                }
            }
            catch (Exception ex)
            {
                TestContext.WriteLine($"Warning: Could not set network condition: {ex.Message}");
            }
        }

        private static void DisableNetwork()
        {
            try
            {
                // Airplane mode simulation
                var startInfo = new ProcessStartInfo
                {
                    FileName = "xcrun",
                    Arguments = "simctl status_bar booted override --network wifi-bars-0 --cellularMode airplane",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using var process = Process.Start(startInfo);
                if (process != null)
                {
                    process.WaitForExit();
                    TestContext.WriteLine("Network disabled (airplane mode)");
                }
            }
            catch (Exception ex)
            {
                TestContext.WriteLine($"Warning: Could not disable network: {ex.Message}");
            }
        }

        private static void ResetNetworkConditions()
        {
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = "xcrun",
                    Arguments = "simctl status_bar booted clear",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using var process = Process.Start(startInfo);
                if (process != null)
                {
                    process.WaitForExit();
                }
            }
            catch (Exception ex)
            {
                TestContext.WriteLine($"Warning: Could not reset network: {ex.Message}");
            }
        }

        [Test]
        [UserJourney(TestCategories.EdgeCases, TestCategories.NetworkErrors)]
        [Description("Tests app behavior when launched without network connectivity")]
        public void TestOfflineAppLaunch()
        {
            TestContext.WriteLine("=== Testing App Launch in Offline Mode ===");
            
            // Disable network before launch
            DisableNetwork();
            Thread.Sleep(2000);
            
            // Launch app
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(5000);
            
            // Capture offline state
            var offlineScreen = Path.Combine(screenshotDir, "01-offline-launch.png");
            SimCtlTestRunner.TakeScreenshot(offlineScreen);
            TestContext.WriteLine($"✅ Offline launch captured: {offlineScreen}");
            
            // Verify offline UI elements (banner, retry button, etc.)
            Thread.Sleep(2000);
            var offlineUIScreen = Path.Combine(screenshotDir, "02-offline-ui-elements.png");
            SimCtlTestRunner.TakeScreenshot(offlineUIScreen);
            TestContext.WriteLine($"✅ Offline UI elements captured: {offlineUIScreen}");
            
            Assert.That(File.Exists(offlineScreen), Is.True);
            Assert.That(File.Exists(offlineUIScreen), Is.True);
        }

        [Test]
        [UserJourney(TestCategories.EdgeCases, TestCategories.NetworkErrors)]
        [Description("Tests app behavior when network connection is lost and restored")]
        public void TestNetworkReconnection()
        {
            TestContext.WriteLine("=== Testing Network Reconnection Flow ===");
            
            // Launch app with network
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(5000);
            
            // Capture normal state
            var normalScreen = Path.Combine(screenshotDir, "10-normal-network.png");
            SimCtlTestRunner.TakeScreenshot(normalScreen);
            
            // Disable network
            DisableNetwork();
            Thread.Sleep(3000);
            
            // Capture offline state
            var offlineScreen = Path.Combine(screenshotDir, "11-went-offline.png");
            SimCtlTestRunner.TakeScreenshot(offlineScreen);
            TestContext.WriteLine($"✅ Offline state captured: {offlineScreen}");
            
            // Re-enable network
            ResetNetworkConditions();
            Thread.Sleep(3000);
            
            // Capture reconnected state
            var reconnectedScreen = Path.Combine(screenshotDir, "12-reconnected.png");
            SimCtlTestRunner.TakeScreenshot(reconnectedScreen);
            TestContext.WriteLine($"✅ Reconnected state captured: {reconnectedScreen}");
            
            Assert.That(File.Exists(normalScreen), Is.True);
            Assert.That(File.Exists(offlineScreen), Is.True);
            Assert.That(File.Exists(reconnectedScreen), Is.True);
        }

        [Test]
        [UserJourney(TestCategories.EdgeCases, TestCategories.NetworkErrors)]
        [Description("Tests app behavior under various slow network conditions")]
        public void TestSlowNetworkConditions()
        {
            TestContext.WriteLine("=== Testing Slow Network Conditions ===");
            
            // Launch app
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(5000);
            
            // Test various network conditions
            var conditions = new[]
            {
                ("wifi-bars-1", "20-poor-wifi.png", "Poor WiFi"),
                ("wifi-bars-2", "21-fair-wifi.png", "Fair WiFi"),
                ("wifi-bars-3", "22-good-wifi.png", "Good WiFi")
            };
            
            foreach (var (condition, filename, description) in conditions)
            {
                SetNetworkCondition(condition);
                Thread.Sleep(3000);
                
                var screenPath = Path.Combine(screenshotDir, filename);
                SimCtlTestRunner.TakeScreenshot(screenPath);
                TestContext.WriteLine($"✅ {description} captured: {screenPath}");
                Assert.That(File.Exists(screenPath), Is.True);
            }
        }

        [Test]
        [UserJourney(TestCategories.EdgeCases, TestCategories.NetworkErrors)]
        [Description("Tests data persistence when network is unavailable")]
        public void TestOfflineDataPersistence()
        {
            TestContext.WriteLine("=== Testing Offline Data Persistence ===");
            
            // Launch app with network
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(5000);
            
            // Capture initial state
            var initialScreen = Path.Combine(screenshotDir, "30-data-initial.png");
            SimCtlTestRunner.TakeScreenshot(initialScreen);
            
            // Simulate user interaction (would be actual in Appium)
            Thread.Sleep(3000);
            
            // Disable network
            DisableNetwork();
            Thread.Sleep(2000);
            
            // Capture offline data state
            var offlineDataScreen = Path.Combine(screenshotDir, "31-data-offline.png");
            SimCtlTestRunner.TakeScreenshot(offlineDataScreen);
            TestContext.WriteLine($"✅ Offline data state captured: {offlineDataScreen}");
            
            // Terminate app
            try
            {
                var deviceId = SimCtlTestRunner.GetBootedDeviceId();
                using var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "xcrun",
                        Arguments = $"simctl terminate {deviceId} com.drmaxmuscle.max",
                        UseShellExecute = false,
                        CreateNoWindow = true
                    }
                };
                process.Start();
                process.WaitForExit();
            }
            catch (Exception ex)
            {
                TestContext.WriteLine($"Warning: Could not terminate app: {ex.Message}");
            }
            
            Thread.Sleep(2000);
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(5000);
            
            // Capture persisted data
            var persistedScreen = Path.Combine(screenshotDir, "32-data-persisted.png");
            SimCtlTestRunner.TakeScreenshot(persistedScreen);
            TestContext.WriteLine($"✅ Persisted data captured: {persistedScreen}");
            
            Assert.That(File.Exists(initialScreen), Is.True);
            Assert.That(File.Exists(offlineDataScreen), Is.True);
            Assert.That(File.Exists(persistedScreen), Is.True);
        }

        [Test]
        [UserJourney(TestCategories.EdgeCases, TestCategories.NetworkErrors)]
        [Description("Tests app handling of various network error scenarios")]
        public void TestNetworkErrorHandling()
        {
            TestContext.WriteLine("=== Testing Network Error Handling ===");
            
            // Launch app
            SimCtlTestRunner.LaunchApp();
            Thread.Sleep(5000);
            
            // Test various error scenarios
            var errorScenarios = new[]
            {
                ("40-timeout-error.png", "Network timeout error"),
                ("41-connection-refused.png", "Connection refused error"),
                ("42-dns-failure.png", "DNS resolution failure"),
                ("43-ssl-error.png", "SSL certificate error")
            };
            
            foreach (var (filename, scenario) in errorScenarios)
            {
                TestContext.WriteLine($"Simulating: {scenario}");
                
                // In real test, we'd trigger these conditions
                // For now, we capture the current state
                Thread.Sleep(2000);
                
                var screenPath = Path.Combine(screenshotDir, filename);
                SimCtlTestRunner.TakeScreenshot(screenPath);
                TestContext.WriteLine($"✅ {scenario} captured: {screenPath}");
                Assert.That(File.Exists(screenPath), Is.True);
            }
        }
    }
} 