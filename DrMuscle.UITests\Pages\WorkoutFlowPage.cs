using OpenQA.Selenium;
using OpenQA.Selenium.Appium;
using OpenQA.Selenium.Support.UI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

namespace DrMuscle.UITests.Pages
{
    /// <summary>
    /// Page object for Workout Flow Variations functionality
    /// </summary>
    public class WorkoutFlowPage
    {
        private readonly AppiumDriver? _driver;
        private readonly WebDriverWait _wait;
        
        public WorkoutFlowPage(AppiumDriver driver)
        {
            _driver = driver ?? throw new ArgumentNullException(nameof(driver));
            _wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
        }
        
        // Pause/Resume elements
        private AppiumElement? ExitWorkoutButton => FindElement(MobileBy.AccessibilityId("ExitWorkout"));
        private AppiumElement? ResumePrompt => FindElement(MobileBy.AccessibilityId("ResumeWorkoutPrompt"));
        private AppiumElement? ResumePromptMessage => FindElement(MobileBy.AccessibilityId("ResumeMessage"));
        private AppiumElement? ResumeWorkoutButton => FindElement(MobileBy.AccessibilityId("ResumeWorkout"));
        private AppiumElement? StartNewWorkoutButton => FindElement(MobileBy.AccessibilityId("StartNewWorkout"));
        private AppiumElement? WorkoutStateIndicator => FindElement(MobileBy.AccessibilityId("WorkoutState"));
        
        // Skip exercise elements
        private AppiumElement? SkipExerciseButton => FindElement(MobileBy.AccessibilityId("SkipExercise"));
        private AppiumElement? SkipReasonModal => FindElement(MobileBy.AccessibilityId("SkipReasonModal"));
        private AppiumElement? SkipReasonList => FindElement(MobileBy.AccessibilityId("SkipReasons"));
        private AppiumElement? SkipNoteInput => FindElement(MobileBy.AccessibilityId("SkipNote"));
        private AppiumElement? ConfirmSkipButton => FindElement(MobileBy.AccessibilityId("ConfirmSkip"));
        
        // Add exercise elements
        private AppiumElement? AddExerciseButton => FindElement(MobileBy.AccessibilityId("AddExercise"));
        private AppiumElement? ExerciseSearchInput => FindElement(MobileBy.AccessibilityId("AddExerciseSearch"));
        private AppiumElement? SearchResultsList => FindElement(MobileBy.AccessibilityId("ExerciseSearchResults"));
        private AppiumElement? AddedExerciseSetsInput => FindElement(MobileBy.AccessibilityId("AddedSets"));
        private AppiumElement? AddedExerciseRepsInput => FindElement(MobileBy.AccessibilityId("AddedReps"));
        private AppiumElement? ConfirmAddButton => FindElement(MobileBy.AccessibilityId("ConfirmAddExercise"));
        
        // Finish early elements
        private AppiumElement? FinishEarlyButton => FindElement(MobileBy.AccessibilityId("FinishWorkoutEarly"));
        private AppiumElement? FinishEarlyConfirmation => FindElement(MobileBy.AccessibilityId("FinishEarlyConfirm"));
        private AppiumElement? FinishEarlyMessage => FindElement(MobileBy.AccessibilityId("FinishEarlyMessage"));
        private AppiumElement? FinishReasonList => FindElement(MobileBy.AccessibilityId("FinishReasons"));
        private AppiumElement? FinishNoteInput => FindElement(MobileBy.AccessibilityId("FinishNote"));
        private AppiumElement? ConfirmFinishButton => FindElement(MobileBy.AccessibilityId("ConfirmFinishEarly"));
        
        // Summary elements
        private AppiumElement? WorkoutSummary => FindElement(MobileBy.AccessibilityId("WorkoutSummary"));
        private AppiumElement? TotalVolumeLabel => FindElement(MobileBy.AccessibilityId("TotalVolume"));
        private AppiumElement? CompletionRateLabel => FindElement(MobileBy.AccessibilityId("CompletionRate"));
        
        // Actions - Pause/Resume
        public void ExitWorkout()
        {
            ExitWorkoutButton?.Click();
        }
        
        public void ResumeWorkout()
        {
            ResumeWorkoutButton?.Click();
        }
        
        public void StartNewWorkout()
        {
            StartNewWorkoutButton?.Click();
        }
        
        // Actions - Skip Exercise
        public void SkipCurrentExercise()
        {
            SkipExerciseButton?.Click();
            _wait.Until(d => SkipReasonModal != null && SkipReasonModal.Displayed);
        }
        
        public void SelectSkipReason(string reason)
        {
            var reasonOption = FindElement(MobileBy.XPath($"//XCUIElementTypeButton[@name='{reason}']"));
            reasonOption?.Click();
        }
        
        public void AddSkipNote(string note)
        {
            SkipNoteInput?.Clear();
            SkipNoteInput?.SendKeys(note);
        }
        
        public void ConfirmSkip()
        {
            ConfirmSkipButton?.Click();
        }
        
        // Actions - Add Exercise
        public void AddExercise()
        {
            AddExerciseButton?.Click();
            _wait.Until(d => ExerciseSearchInput != null);
        }
        
        public void SearchExercise(string name)
        {
            ExerciseSearchInput?.Clear();
            ExerciseSearchInput?.SendKeys(name);
            Thread.Sleep(500);
        }
        
        public void SelectExerciseToAdd(string exerciseName)
        {
            var exercise = FindElement(MobileBy.XPath($"//XCUIElementTypeCell[contains(@name, '{exerciseName}')]"));
            exercise?.Click();
        }
        
        public void SetAddedExerciseSets(string sets)
        {
            AddedExerciseSetsInput?.Clear();
            AddedExerciseSetsInput?.SendKeys(sets);
        }
        
        public void SetAddedExerciseReps(string reps)
        {
            AddedExerciseRepsInput?.Clear();
            AddedExerciseRepsInput?.SendKeys(reps);
        }
        
        public void ConfirmAddExercise()
        {
            ConfirmAddButton?.Click();
        }
        
        // Actions - Finish Early
        public void FinishWorkoutEarly()
        {
            FinishEarlyButton?.Click();
            _wait.Until(d => FinishEarlyConfirmation != null && FinishEarlyConfirmation.Displayed);
        }
        
        public void SelectFinishReason(string reason)
        {
            var reasonOption = FindElement(MobileBy.XPath($"//XCUIElementTypeButton[@name='{reason}']"));
            reasonOption?.Click();
        }
        
        public void AddFinishNote(string note)
        {
            FinishNoteInput?.Clear();
            FinishNoteInput?.SendKeys(note);
        }
        
        public void ConfirmFinishEarly()
        {
            ConfirmFinishButton?.Click();
        }
        
        // Verifications
        public bool IsResumePromptVisible()
        {
            return ResumePrompt != null && ResumePrompt.Displayed;
        }
        
        public bool IsSkipReasonVisible()
        {
            return SkipReasonModal != null && SkipReasonModal.Displayed;
        }
        
        public bool CanAddSkipNote()
        {
            return SkipNoteInput != null && SkipNoteInput.Displayed;
        }
        
        public bool CanConfigureAddedExercise()
        {
            return AddedExerciseSetsInput != null && AddedExerciseSetsInput.Displayed;
        }
        
        public bool IsFinishEarlyConfirmationVisible()
        {
            return FinishEarlyConfirmation != null && FinishEarlyConfirmation.Displayed;
        }
        
        public bool IsWorkoutActive()
        {
            var state = WorkoutStateIndicator?.Text ?? "";
            return state.Contains("Active") || state.Contains("In Progress");
        }
        
        // Data retrieval
        public string GetResumePromptMessage()
        {
            return ResumePromptMessage?.Text ?? "";
        }
        
        public List<string> GetSkipReasons()
        {
            var reasons = new List<string>();
            
            if (SkipReasonList != null)
            {
                var reasonButtons = SkipReasonList.FindElements(MobileBy.ClassName("XCUIElementTypeButton"));
                reasons = reasonButtons.Select(r => r.Text).ToList();
            }
            
            return reasons;
        }
        
        public List<string> GetSearchResults()
        {
            var results = new List<string>();
            
            if (SearchResultsList != null)
            {
                var cells = SearchResultsList.FindElements(MobileBy.ClassName("XCUIElementTypeCell"));
                results = cells.Select(c => c.Text).ToList();
            }
            
            return results;
        }
        
        public string GetFinishEarlyMessage()
        {
            return FinishEarlyMessage?.Text ?? "";
        }
        
        public List<string> GetFinishEarlyReasons()
        {
            var reasons = new List<string>();
            
            if (FinishReasonList != null)
            {
                var reasonButtons = FinishReasonList.FindElements(MobileBy.ClassName("XCUIElementTypeButton"));
                reasons = reasonButtons.Select(r => r.Text).ToList();
            }
            
            return reasons;
        }
        
        public string GetWorkoutSummary()
        {
            return WorkoutSummary?.Text ?? "";
        }
        
        public string GetTotalVolume()
        {
            return TotalVolumeLabel?.Text ?? "";
        }
        
        public string GetCompletionRate()
        {
            return CompletionRateLabel?.Text ?? "";
        }
        
        // Helper methods
        private AppiumElement? FindElement(By by)
        {
            try
            {
                return _driver?.FindElement(by) as AppiumElement;
            }
            catch (NoSuchElementException)
            {
                return null;
            }
        }
    }
}