using NUnit.Framework;
using OpenQA.Selenium;
using OpenQA.Selenium.Support.UI;
using DrMuscle.UITests.Helpers;
using DrMuscle.UITests.Pages;
using System;
using System.Threading;

namespace DrMuscle.UITests.Tests
{
    [TestFixture]
    public class AuthenticationTests : AppiumSetup
    {
        [Test]
        public void CreateNewAccountAndLogin()
        {
            // Generate test account
            var (email, password) = TestAccount.Generate();
            Console.WriteLine($"Testing with account: {email}");
            
            // Wait for app to load
            Thread.Sleep(3000);
            
            // Take initial screenshot
            TakeScreenshot("01-welcome-screen");
            
            // Tap "Create new account"
            var createAccountButton = Driver?.FindElement(MobileBy.AccessibilityId("GetStartedButton"));
            Assert.That(createAccountButton, Is.Not.Null, "Create account button should be found");
            createAccountButton?.Click();
            
            Thread.Sleep(2000);
            TakeScreenshot("02-after-create-account-tap");
            
            // The app should show registration fields
            // Enter email
            var emailField = Driver?.FindElement(MobileBy.AccessibilityId("EmailEntry"));
            if (emailField == null)
            {
                // Try alternative selectors
                emailField = Driver?.FindElement(By.XPath("//XCUIElementTypeTextField[@value='Enter email']"));
            }
            Assert.That(emailField, Is.Not.Null, "Email field should be found");
            emailField?.Clear();
            emailField?.SendKeys(email);
            
            // Enter password
            var passwordField = Driver?.FindElement(MobileBy.AccessibilityId("PasswordEntry"));
            if (passwordField == null)
            {
                // Try alternative selectors
                passwordField = Driver?.FindElement(By.XPath("//XCUIElementTypeSecureTextField[@value='Enter password']"));
            }
            Assert.That(passwordField, Is.Not.Null, "Password field should be found");
            passwordField?.Clear();
            passwordField?.SendKeys(password);
            
            TakeScreenshot("03-filled-registration-form");
            
            // Submit registration
            var loginButton = Driver?.FindElement(MobileBy.AccessibilityId("LoginButton"));
            if (loginButton == null)
            {
                // Try to find by text
                loginButton = Driver?.FindElement(By.XPath("//XCUIElementTypeStaticText[@name='Log in']"));
            }
            Assert.That(loginButton, Is.Not.Null, "Login/Register button should be found");
            loginButton?.Click();
            
            // Wait for registration to complete
            Thread.Sleep(5000);
            TakeScreenshot("04-after-registration");
            
            // Verify we're logged in by checking for elements that appear after login
            // This will depend on your app's flow after registration
            Assert.Pass("Account creation test completed. Check screenshots for verification.");
        }
        
        [Test]
        public void LoginWithApple()
        {
            // This test only runs on iOS
            if (Driver?.Capabilities.GetCapability("platformName")?.ToString() != "iOS")
            {
                Assert.Ignore("Apple Sign In is only available on iOS");
            }
            
            // Wait for app to load
            Thread.Sleep(3000);
            TakeScreenshot("01-welcome-screen-apple");
            
            // Find and tap Apple Sign In button
            var appleButton = Driver?.FindElement(MobileBy.AccessibilityId("LoginWithAppleButton"));
            if (appleButton == null)
            {
                // Try alternative selector
                appleButton = Driver?.FindElement(By.XPath("//XCUIElementTypeStaticText[@name='Join free with Apple']"));
            }
            
            Assert.That(appleButton, Is.Not.Null, "Apple Sign In button should be found");
            appleButton?.Click();
            
            Thread.Sleep(3000);
            TakeScreenshot("02-apple-signin-dialog");
            
            // Note: Automating Apple Sign In requires special handling
            // as it opens system dialogs that may not be accessible to Appium
            Assert.Pass("Apple Sign In button clicked. Manual verification required for system dialogs.");
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private void TakeScreenshot(string name)
        {
            TakeScreenshot(Driver, name);
        }
        #pragma warning restore CA1822 // Mark members as static
        
        private static void TakeScreenshot(AppiumDriver? driver, string name)
        {
            try
            {
                var screenshot = driver?.GetScreenshot();
                if (screenshot != null)
                {
                    var screenshotPath = Path.Combine(
                        TestContext.CurrentContext.WorkDirectory, 
                        $"{TestContext.CurrentContext.Test.Name}_{name}.png"
                    );
                    screenshot.SaveAsFile(screenshotPath);
                    TestContext.AddTestAttachment(screenshotPath, name);
                    Console.WriteLine($"Screenshot saved: {name}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to take screenshot {name}: {ex.Message}");
            }
        }
    }
}