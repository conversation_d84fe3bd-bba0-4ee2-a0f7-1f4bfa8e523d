using DrMuscle.UITests.Extensions;

namespace DrMuscle.UITests.PageObjects
{
    public class WelcomePage
    {
        private readonly IApp _app;

        // Automation IDs for page elements
        private const string _welcomeTitleId = "WelcomeTitle";
        private const string _getStartedButtonId = "GetStartedButton";
        private const string _loginButtonId = "LoginButton";

        public WelcomePage(IApp app)
        {
            _app = app;
        }

        public void WaitForPageToLoad()
        {
            _app.WaitForElement(_welcomeTitleId);
        }

        public RegistrationPage TapGetStarted()
        {
            _app.WaitAndTap(_getStartedButtonId);
            return new RegistrationPage(_app);
        }

        public void TapLogin()
        {
            _app.WaitAndTap(_loginButtonId);
        }
    }
}
