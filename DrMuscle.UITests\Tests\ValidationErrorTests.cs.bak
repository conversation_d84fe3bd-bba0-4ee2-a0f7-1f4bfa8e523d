using DrMuscle.UITests.Extensions;
using DrMuscle.UITests.Helpers;
using DrMuscle.UITests.PageObjects;

namespace DrMuscle.UITests.Tests
{
    [TestFixture]
    public class ValidationErrorTests
    {
        private IApp? _app;

        [SetUp]
        public void SetUp()
        {
            _app = AppInitializer.StartApp();
        }

        [Test]
        public void RegistrationFailsWithInvalidEmail()
        {
            // Arrange
            var welcomePage = new WelcomePage(_app!);
            var invalidEmails = new[] { "notanemail", "missing@domain", "@nodomain.com", "<NAME_EMAIL>" };

            // Act
            welcomePage.WaitForPageToLoad();
            var registrationPage = welcomePage.TapGetStarted();
            registrationPage.WaitForPageToLoad();

            foreach (var invalidEmail in invalidEmails)
            {
                registrationPage.EnterEmail(invalidEmail);
                registrationPage.EnterPassword("ValidPassword123");
                registrationPage.TapSignUp();

                // Assert - Should still be on registration page with error
                Thread.Sleep(1000); // Wait for validation
                Assert.That(registrationPage.IsErrorMessageVisible() ||
                           _app!.ElementExists("EmailEntry"), Is.True,
                           $"Registration should fail for invalid email: {invalidEmail}");
            }
        }

        [Test]
        public void RegistrationFailsWithShortPassword()
        {
            // Arrange
            var welcomePage = new WelcomePage(_app!);
            var (email, _) = TestAccount.Generate();

            // Act
            welcomePage.WaitForPageToLoad();
            var registrationPage = welcomePage.TapGetStarted();
            registrationPage.WaitForPageToLoad();

            registrationPage.EnterEmail(email);
            registrationPage.EnterPassword("12345"); // Less than 6 characters
            registrationPage.TapSignUp();

            // Assert - Should still be on registration page
            Thread.Sleep(1000); // Wait for validation
            Assert.That(_app!.ElementExists("PasswordEntry"), Is.True,
                "Should still be on registration page after short password");
        }

        [Test]
        public void LoginFailsWithIncorrectCredentials()
        {
            // Arrange
            var welcomePage = new WelcomePage(_app!);
            var loginPage = new LoginPage(_app!);

            // Act
            welcomePage.WaitForPageToLoad();
            loginPage.EnterEmail("<EMAIL>");
            loginPage.EnterPassword("WrongPassword123");
            loginPage.TapLogin();

            // Assert - Should show error or stay on login page
            Thread.Sleep(2000); // Wait for login attempt
            Assert.That(loginPage.IsErrorMessageVisible() ||
                       _app!.ElementExists("EmailEntry"), Is.True,
                "Login should fail with incorrect credentials");
        }

        [Test]
        public void RegistrationFailsWithExistingEmail()
        {
            // Arrange - Use a known test account
            var existingEmail = "<EMAIL>";
            var welcomePage = new WelcomePage(_app!);

            // Act
            welcomePage.WaitForPageToLoad();
            var registrationPage = welcomePage.TapGetStarted();
            registrationPage.WaitForPageToLoad();

            registrationPage.EnterEmail(existingEmail);
            registrationPage.EnterPassword("ValidPassword123");
            registrationPage.TapSignUp();

            // Assert - Should show error about existing email
            Thread.Sleep(2000); // Wait for server response
            Assert.That(registrationPage.IsErrorMessageVisible() ||
                       _app!.ElementExists("EmailEntry"), Is.True,
                "Registration should fail for existing email");
        }
    }
}
