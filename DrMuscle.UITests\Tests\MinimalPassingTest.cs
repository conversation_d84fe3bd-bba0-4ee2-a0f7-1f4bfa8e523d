using NUnit.Framework;

namespace DrMuscle.UITests.Tests
{
    /// <summary>
    /// Minimal test to verify test infrastructure
    /// </summary>
    [TestFixture]
    public class MinimalPassingTest
    {
        [Test]
        public void SimplePassingTest()
        {
            TestContext.WriteLine("This is a simple passing test");
            Assert.That(1 + 1, Is.EqualTo(2));
        }
        
        [Test]
        public void AnotherPassingTest()
        {
            TestContext.WriteLine("Another simple test");
            var text = "Hello, World!";
            Assert.That(text, Is.Not.Null);
            Assert.That(text.Length, Is.GreaterThan(0));
        }
        
        [Test]
        public void TestWithMultipleAssertions()
        {
            TestContext.WriteLine("Test with multiple assertions");
            
            // Test basic math
            Assert.That(2 * 3, Is.EqualTo(6));
            
            // Test string operations
            var str = "DrMuscle";
            Assert.That(str.ToUpper(), Is.EqualTo("DRMUSCLE"));
            Assert.That(str.Contains("Muscle"), Is.True);
            
            // Test collections
            var numbers = new[] { 1, 2, 3, 4, 5 };
            Assert.That(numbers.Length, Is.EqualTo(5));
            Assert.That(numbers, Contains.Item(3));
            
            TestContext.WriteLine("All assertions passed!");
        }
    }
}