using DrMuscle.UITests.Extensions;
using DrMuscle.UITests.Helpers;
using DrMuscle.UITests.PageObjects;

namespace DrMuscle.UITests.Tests
{
    [TestFixture]
    public class LogoutLoginFlow
    {
        private IApp? _app;

        [SetUp]
        public void SetUp()
        {
            _app = AppInitializer.StartApp();
        }

        [Test]
        public void UserCanLogoutAndLoginAgain()
        {
            // Arrange - First complete signup to have an account
            var (email, password) = TestAccount.Generate();
            var welcomePage = new WelcomePage(_app!);

            // Act - Sign up first
            _app!.Screenshot("Welcome Page");
            welcomePage.WaitForPageToLoad();

            var registrationPage = welcomePage.TapGetStarted();
            registrationPage.WaitForPageToLoad();
            registrationPage.EnterEmail(email);
            registrationPage.EnterPassword(password);

            var onboardingPage = registrationPage.TapSignUp();
            var mainPage = onboardingPage.CompleteOnboarding();
            _app.Screenshot("Main Page After Signup");

            // Now test logout
            var sideMenu = new SideMenuPage(_app);
            sideMenu.OpenMenu();
            _app.Screenshot("Side Menu Open");

            sideMenu.TapLogout();
            _app.Screenshot("After Logout");

            // Should be back at welcome/login page
            welcomePage.WaitForPageToLoad();

            // Now login with the same credentials
            var loginPage = new LoginPage(_app);
            loginPage.EnterEmail(email);
            loginPage.EnterPassword(password);

            var mainPageAfterLogin = loginPage.TapLogin();
            _app.Screenshot("Main Page After Login");

            // Assert - Verify we're back on the main page
            mainPageAfterLogin.WaitForPageToLoad();
            Assert.That(mainPageAfterLogin.IsStartWorkoutButtonVisible(), Is.True,
                "Start Workout button should be visible after login");
        }
    }
}
