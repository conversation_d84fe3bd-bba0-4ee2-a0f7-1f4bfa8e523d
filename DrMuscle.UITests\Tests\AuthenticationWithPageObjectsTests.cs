using NUnit.Framework;
using DrMuscle.UITests.Helpers;
using DrMuscle.UITests.Pages;
using System;
using System.Threading;

namespace DrMuscle.UITests.Tests
{
    [TestFixture]
    public class AuthenticationWithPageObjectsTests : AppiumSetup
    {
        [Test]
        public void CreateAccountUsingPageObjects()
        {
            // Arrange
            var (email, password) = TestAccount.Generate();
            var loginPage = new LoginPage(Driver!);
            
            Console.WriteLine($"Testing account creation with: {email}");
            
            // Wait for page to load
            Thread.Sleep(3000);
            loginPage.WaitForPageToLoad();
            
            // Act - Create account flow
            TakeScreenshot("01-welcome-page");
            
            // First tap create account to show the registration form
            loginPage.TapCreateAccount();
            Thread.Sleep(2000);
            TakeScreenshot("02-registration-form");
            
            // Fill in registration details
            loginPage.EnterEmail(email);
            loginPage.EnterPassword(password);
            TakeScreenshot("03-filled-form");
            
            // Submit registration
            loginPage.TapLogin();
            Thread.Sleep(5000);
            TakeScreenshot("04-after-registration");
            
            // Assert
            Assert.That(!loginPage.IsDisplayed(), "Login page should no longer be visible after successful registration");
        }
        
        [Test]
        public void LoginWithExistingAccountUsingPageObjects()
        {
            // Arrange
            var account = TestAccount.GenerateWithSeed("pageobjecttest");
            var loginPage = new LoginPage(Driver!);
            
            Console.WriteLine($"Testing login with: {account.email}");
            
            // Wait for page to load
            Thread.Sleep(3000);
            loginPage.WaitForPageToLoad();
            
            // Act
            TakeScreenshot("01-login-page");
            loginPage.Login(account.email, account.password);
            
            Thread.Sleep(5000);
            TakeScreenshot("02-after-login");
            
            // Assert
            Assert.That(!loginPage.IsDisplayed(), "Login page should no longer be visible after successful login");
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private void TakeScreenshot(string name)
        {
            TakeScreenshot(Driver, name);
        }
        #pragma warning restore CA1822 // Mark members as static
        
        private static void TakeScreenshot(AppiumDriver? driver, string name)
        {
            try
            {
                var screenshot = driver?.GetScreenshot();
                if (screenshot != null)
                {
                    var screenshotPath = Path.Combine(
                        TestContext.CurrentContext.WorkDirectory, 
                        $"{TestContext.CurrentContext.Test.Name}_{name}.png"
                    );
                    screenshot.SaveAsFile(screenshotPath);
                    TestContext.AddTestAttachment(screenshotPath, name);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to take screenshot: {ex.Message}");
            }
        }
    }
}