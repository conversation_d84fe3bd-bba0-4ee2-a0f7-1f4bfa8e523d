using NUnit.Framework;
using System;
using System.Diagnostics;
using Xamarin.UITest;

namespace DrMuscle.UITests.Tests
{
    [TestFixture]
    public class DeviceIdTest
    {
        [Test]
        public void TryWithActualDeviceId()
        {
            var isCI = Environment.GetEnvironmentVariable("CI") == "true";
            if (!isCI)
            {
                Assert.Ignore("This test is only for CI debugging");
            }
            
            // Try to get the actual device ID from simulator list
            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "xcrun",
                        Arguments = "simctl list devices booted -j",
                        RedirectStandardOutput = true,
                        UseShellExecute = false,
                        CreateNoWindow = true
                    }
                };
                
                process.Start();
                var output = process.StandardOutput.ReadToEnd();
                process.WaitForExit();
                
                Console.WriteLine($"Booted devices: {output}");
                
                // Try using the actual UDID if we can extract it
                // For now, let's try with the installed app approach
                var app = ConfigureApp
                    .iOS
                    .InstalledApp("com.drmaxmuscle.max")
                    .StartApp();
                    
                if (app != null)
                {
                    Assert.Pass("Successfully launched app using InstalledApp without device identifier");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to launch with device ID approach: {ex.Message}");
                Assert.Fail($"Could not launch app: {ex.Message}");
            }
        }
    }
}