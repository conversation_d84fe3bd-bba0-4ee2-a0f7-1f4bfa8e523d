# Dr. Muscle Web Application - API Reference Guide

## Overview

This comprehensive API reference guide documents all endpoints, data models, and authentication mechanisms used by the Dr. Muscle MAUI application. This guide enables Web application developers to implement the same functionality without reverse-engineering API calls.

**Base URLs:**
- Production: `https://drmuscle.azurewebsites.net/`
- Development: `https://drmuscle2.azurewebsites.net/`

**API Version:** 1

## Authentication

### OAuth Token Authentication

The primary authentication method uses OAuth 2.0 with form-encoded parameters.

**Endpoint:** `POST /token`

**Content-Type:** `application/x-www-form-urlencoded`

**Parameters:**
```
grant_type=password
username={email}
password={password}
```

**Response:**
```json
{
  "access_token": "string",
  "token_type": "bearer",
  "expires_in": 3600
}
```

**Authorization Header:**
All authenticated requests must include:
```
Authorization: bearer {access_token}
```

### Apple Sign In (Alternative)

**Endpoint:** `POST /api/Account/RegisterWithApple`

**Request:**
```json
{
  "token": "apple_id_token"
}
```

**Response:** `UserInfosModel`

## Core Data Models

### UserInfosModel
```json
{
  "Id": "string",
  "Email": "string", 
  "Token": "string",
  "FirstName": "string",
  "LastName": "string",
  "WeightUnit": "string",
  "Height": "number",
  "Age": "number",
  "Gender": "string"
}
```

### WorkoutTemplateGroupModel
```json
{
  "Id": "number",
  "UserId": "string",
  "Label": "string",
  "WorkoutTemplates": ["WorkoutTemplateModel"],
  "IsSystemExercise": "boolean",
  "IsFeaturedProgram": "boolean",
  "RequiredWorkoutToLevelUp": "number",
  "Level": "number",
  "RemainingToLevelUp": "number",
  "NextProgramId": "number",
  "ProgramId": "number"
}
```

### WorkoutTemplateModel
```json
{
  "Id": "number",
  "UserId": "string",
  "Label": "string",
  "Exercises": ["ExerciceModel"],
  "IsSystemExercise": "boolean",
  "WorkoutSettingsModel": "WorkoutTemplateSettingsModel"
}
```

### ExerciceModel
```json
{
  "Id": "number",
  "Label": "string",
  "IsSystemExercise": "boolean",
  "IsSwapTarget": "boolean",
  "IsFinished": "boolean",
  "BodyPartId": "number",
  "IsUnilateral": "boolean",
  "IsTimeBased": "boolean",
  "EquipmentId": "number",
  "IsEasy": "boolean",
  "IsMedium": "boolean",
  "IsBodyweight": "boolean",
  "VideoUrl": "string",
  "IsNextExercise": "boolean",
  "IsPlate": "boolean",
  "IsWeighted": "boolean",
  "IsPyramid": "boolean",
  "RepsMaxValue": "number",
  "RepsMinValue": "number",
  "Timer": "number",
  "IsNormalSets": "boolean",
  "WorkoutGroupId": "number",
  "IsBodypartPriority": "boolean",
  "IsFlexibility": "boolean",
  "IsOneHanded": "boolean",
  "LocalVideo": "string",
  "IsAssisted": "boolean"
}
```

### RecommendationModel
```json
{
  "Series": "number",
  "Reps": "number",
  "Weight": "MultiUnityWeight",
  "OneRMProgress": "number",
  "RecommendationInKg": "number",
  "OneRMPercentage": "number",
  "WarmUpReps1": "number",
  "WarmUpReps2": "number",
  "WarmUpWeightSet1": "MultiUnityWeight",
  "WarmUpWeightSet2": "MultiUnityWeight",
  "WarmUpsList": ["WarmUps"],
  "WarmupsCount": "number",
  "RpRest": "number",
  "NbPauses": "number",
  "NbRepsPauses": "number",
  "IsEasy": "boolean",
  "IsMedium": "boolean",
  "IsBodyweight": "boolean",
  "Increments": "MultiUnityWeight",
  "Max": "MultiUnityWeight",
  "Min": "MultiUnityWeight",
  "IsNormalSets": "boolean",
  "IsDeload": "boolean",
  "IsBackOffSet": "boolean",
  "IsDefaultUnilateral": "boolean",
  "BackOffSetWeight": "MultiUnityWeight",
  "IsMaxChallenge": "boolean",
  "IsLightSession": "boolean",
  "LastLogDate": "datetime",
  "FirstWorkSetReps": "number",
  "FirstWorkSetWeight": "MultiUnityWeight",
  "FirstWorkSet1RM": "MultiUnityWeight",
  "IsPyramid": "boolean",
  "IsReversePyramid": "boolean",
  "HistorySet": ["WorkoutLogSerieModel"],
  "ReferenceSetHistory": "WorkoutLogSerieModel",
  "MinReps": "number",
  "MaxReps": "number",
  "isPlateAvailable": "boolean",
  "isDumbbellAvailable": "boolean",
  "isPulleyAvailable": "boolean",
  "isBandsAvailable": "boolean",
  "Speed": "number",
  "days": "number",
  "RIR": "number",
  "IsManual": "boolean",
  "Id": "number",
  "ReferenseReps": "number",
  "ReferenseWeight": "MultiUnityWeight",
  "IsDropSet": "boolean"
}
```

### WorkoutLogSerieModel
```json
{
  "Id": "number",
  "Exercice": "ExerciceModel",
  "BodypartId": "number",
  "UserId": "string",
  "LogDate": "datetime",
  "Reps": "number",
  "Weight": "MultiUnityWeight",
  "OneRM": "MultiUnityWeight",
  "IsWarmups": "boolean",
  "Isbodyweight": "boolean",
  "IsPlateAvailable": "boolean",
  "IsDumbbellAvailable": "boolean",
  "IsBandsAvailable": "boolean",
  "IsPulleyAvailable": "boolean",
  "NbPause": "number",
  "RIR": "number",
  "IsOneHanded": "boolean",
  "IsReferenceSet": "boolean"
}
```

### MultiUnityWeight
```json
{
  "Value": "number",
  "Unit": "string"
}
```

### BooleanModel
```json
{
  "Result": "boolean",
  "IsFreePlan": "boolean",
  "IsTraining": "boolean",
  "IsMealPlan": "boolean"
}
```

## Account Management Endpoints

### User Registration
**Endpoint:** `POST /api/Account/Register`
**Request:** `RegisterModel`
**Response:** `BooleanModel`

### User Registration (Demo)
**Endpoint:** `POST /api/Account/RegisterUserBeforeDemo`
**Request:** `RegisterModel`
**Response:** `BooleanModel`

### User Registration (After Demo)
**Endpoint:** `POST /api/Account/RegisterUserAfterDemoV2`
**Request:** `RegisterModel`
**Response:** `UserInfosModel`

### User Registration (With User Info)
**Endpoint:** `POST /api/Account/RegisterWithUser`
**Request:** `RegisterModel`
**Response:** `UserInfosModel`

### Check V1 User
**Endpoint:** `POST /api/Account/IsV1User`
**Request:** `null`
**Response:** `BooleanModel`

### Check V1 User by Email
**Endpoint:** `POST /api/Account/IsV1UserEmail?email={email}`
**Request:** `string`
**Response:** `BooleanModel`

### Get User Weights
**Endpoint:** `POST /api/Account/GetUserWeights`
**Request:** `null`
**Response:** `List<UserWeight>`

### Check Email Exists
**Endpoint:** `POST /api/Account/IsEmailAlreadyExist`
**Request:** `IsEmailAlreadyExistModel`
**Response:** `BooleanModel`

### Forgot Password
**Endpoint:** `POST /api/Account/ForgotPassword`
**Request:** `ForgotPasswordModel`
**Response:** `BooleanModel`

### Delete Account
**Endpoint:** `POST /api/Account/DeleteUser`
**Request:** `null`
**Response:** `BooleanModel`

### Check Monthly Subscription
**Endpoint:** `POST /api/Account/IsMonthly`
**Request:** `null`
**Response:** `BooleanModel`

### Subscription Details
**Endpoint:** `POST /api/Account/SubscriptionDetail`
**Request:** `SubscriptionModel`
**Response:** `BooleanModel`

### Subscription Details with ID
**Endpoint:** `POST /api/Account/SubscriptionDetailWithId`
**Request:** `SubscriptionModel`
**Response:** `BooleanModel`

### User Profile Updates
**Endpoint:** `POST /api/Account/SetUserAge`
**Request:** `UserInfosModel`
**Response:** `UserInfosModel`

**Endpoint:** `POST /api/Account/SetUserHeight`
**Request:** `UserInfosModel`
**Response:** `UserInfosModel`

**Endpoint:** `POST /api/Account/SetUserWorkoutDuration`
**Request:** `UserInfosModel`
**Response:** `UserInfosModel`

**Endpoint:** `POST /api/Account/SetUserAB`
**Request:** `UserInfosModel`
**Response:** `UserInfosModel`

**Endpoint:** `POST /api/Account/SetUserTimerOptionsV2`
**Request:** `UserInfosModel`
**Response:** `UserInfosModel`

**Endpoint:** `POST /api/Account/SetUserTimerOptionsV3`
**Request:** `UserInfosModel`
**Response:** `UserInfosModel`

### Device Token Management
**Endpoint:** `POST /api/Account/AddDeviceToken`
**Request:** `DeviceModel`
**Response:** `BooleanModel`

**Endpoint:** `POST /api/Account/RemoveDeviceToken`
**Request:** `DeviceModel`
**Response:** `BooleanModel`

### Health Check
**Endpoint:** `POST /api/Account/IsAlive`
**Request:** `null`
**Response:** `BooleanModel`

### Satisfaction Survey
**Endpoint:** `POST /api/Account/AddSatisfactionSurvey`
**Request:** `SatisfactionSurveyModel`
**Response:** `BooleanModel`

## Workout Management Endpoints

### Get User Workout Groups
**Endpoint:** `POST /api/Workout/GetUserWorkoutTemplateGroup`
**Request:** `null`
**Response:** `GetUserWorkoutTemplateGroupResponseModel`

### Get Featured Programs
**Endpoint:** `POST /api/Workout/GetFeaturedProgramForUserV2`
**Request:** `null`
**Response:** `GetUserWorkoutTemplateGroupResponseModel`

### Get System Workout Groups
**Endpoint:** `POST /api/Workout/GetSystemWorkoutTemplateGroup`
**Request:** `null`
**Response:** `GetUserWorkoutTemplateGroupResponseModel`

### Get Only System Workout Groups
**Endpoint:** `POST /api/Workout/GetOnlySystemWorkoutTemplateGroup`
**Request:** `null`
**Response:** `GetUserWorkoutTemplateGroupResponseModel`

### Get Customized System Workout Groups
**Endpoint:** `POST /api/Workout/GetCustomizedSystemWorkoutGroup`
**Request:** `EquipmentModel`
**Response:** `GetUserWorkoutTemplateGroupResponseModel`

### Get Only Customized System Workout Groups
**Endpoint:** `POST /api/Workout/GetOnlyCustomizedSystemWorkoutGroup`
**Request:** `EquipmentModel`
**Response:** `GetUserWorkoutTemplateGroupResponseModel`

### Get User Workout
**Endpoint:** `POST /api/Workout/GetUserWorkout`
**Request:** `null`
**Response:** `GetUserWorkoutTemplateResponseModel`

### Get Custom Workouts for User
**Endpoint:** `POST /api/Workout/GetCustomWorkoutsForUser`
**Request:** `null`
**Response:** `GetUserWorkoutTemplateResponseModel`

### Get User Customized Current Workout
**Endpoint:** `POST /api/Workout/GetUserCustomizedCurrentWorkout`
**Request:** `number` (workoutid)
**Response:** `WorkoutTemplateModel`

### Save Workout (V3)
**Endpoint:** `POST /api/Workout/SaveWorkoutV3Pro`
**Request:** `SaveWorkoutModel`
**Response:** `BooleanModel`

### Save and Get Workout Info
**Endpoint:** `POST /api/Workout/SaveGetWorkoutInfoPro`
**Request:** `SaveWorkoutModel`
**Response:** `GetUserProgramInfoResponseModel`

### Create New Workout Template Group
**Endpoint:** `POST /api/Workout/CreateNewWorkoutTemplateGroup`
**Request:** `WorkoutTemplateGroupModel`
**Response:** `BooleanModel`

### Restore User Workout Template
**Endpoint:** `POST /api/Workout/RestoreUserWorkoutTemplate`
**Request:** `WorkoutTemplateGroupModel`
**Response:** `BooleanModel`

### Rename Workout Template
**Endpoint:** `POST /api/Workout/RenameWorkoutTemplate`
**Request:** `WorkoutTemplateModel`
**Response:** `BooleanModel`

## Exercise Management Endpoints

### Get Exercise Recommendation
**Endpoint:** `POST /api/Exercise/GetRecommendationForExercise`
**Request:** `GetRecommendationForExerciseModel`
**Response:** `RecommendationModel`

### Get Rest Pause Recommendation
**Endpoint:** `POST /api/Exercise/GetRecommendationRestPauseForExercise`
**Request:** `GetRecommendationForExerciseModel`
**Response:** `RecommendationModel`

### Add New Exercise Log
**Endpoint:** `POST /api/Exercise/AddNewExerciseLog`
**Request:** `NewExerciceLogModel`
**Response:** `BooleanModel`

### Add New Exercise Log with More Sets
**Endpoint:** `POST /api/Exercise/AddNewExerciseLogWithMoreSet`
**Request:** `NewExerciceLogModel`
**Response:** `BooleanModel`

### Add Workout Log Serie
**Endpoint:** `POST /api/Exercise/AddWorkoutLogSerieNew`
**Request:** `WorkoutLogSerieModel`
**Response:** `BooleanModel`

### Add Workout Log Serie List
**Endpoint:** `POST /api/Exercise/AddWorkoutLogSerieListNew`
**Request:** `List<WorkoutLogSerieModel>`
**Response:** `BooleanModel`

### Get Workout History (All Time)
**Endpoint:** `POST /api/Exercise/GetWorkoutHistoryForAlltime`
**Request:** `GetUserWorkoutLogAverageForExerciseRequest`
**Response:** `List<HistoryModel>`

### Get Workout History (By Date)
**Endpoint:** `POST /api/Exercise/GetWorkoutHistoryForDate`
**Request:** `GetUserWorkoutLogAverageForExerciseRequest`
**Response:** `List<HistoryModel>`

### Get One RM for Exercise
**Endpoint:** `POST /api/Exercise/GetOneRMForExercise`
**Request:** `GetOneRMforExerciseModel`
**Response:** `OneRMModel`

### Get Custom Exercises for User
**Endpoint:** `POST /api/Exercise/GetCustomExerciseForUser`
**Request:** `string` (userName)
**Response:** `GetUserExerciseResponseModel`

### Delete Exercise
**Endpoint:** `POST /api/Exercise/DeleteExercise`
**Request:** `ExerciceModel`
**Response:** `BooleanModel`

### Validate Featured Program Code
**Endpoint:** `POST /api/Exercise/IsValidFeatureProgramCode`
**Request:** `FeaturedProgramModel`
**Response:** `BooleanModel`

### Validate Featured Program Code V2
**Endpoint:** `POST /api/Exercise/IsValidFeatureProgramCodeV2`
**Request:** `FeaturedProgramModel`
**Response:** `UnlockCodeResponseModel`

## Workout Log Endpoints

### Get User Workout Log Average
**Endpoint:** `POST /api/WorkoutLog/GetUserWorkoutLogAverageV2`
**Request:** `null`
**Response:** `GetUserWorkoutLogAverageResponse`

### Get User Workout Log Average with User Stats
**Endpoint:** `POST /api/WorkoutLog/GetUserWorkoutLogAverageWithUserStatsV2`
**Request:** `null`
**Response:** `GetUserWorkoutLogAverageResponse`

### Get User Workout Log Average with Sets
**Endpoint:** `POST /api/WorkoutLog/GetUserWorkoutLogAverageWithSetsTimeZoneInfoV2`
**Request:** `TimeZoneInfo`
**Response:** `GetUserWorkoutLogAverageResponse`

### Get User Workout Log Average for Exercise
**Endpoint:** `POST /api/WorkoutLog/GetUserWorkoutLogAverageForExercise`
**Request:** `GetUserWorkoutLogAverageForExerciseRequest`
**Response:** `GetUserWorkoutLogAverageResponse`

### Get User Workout Log Average for Exercise (Period)
**Endpoint:** `POST /api/WorkoutLog/GetUserWorkoutLogAverageForExerciseForPeriod`
**Request:** `GetUserWorkoutLogAverageForExerciseRequest`
**Response:** `GetUserWorkoutLogAverageResponse`

### Get User Workout Stats
**Endpoint:** `POST /api/WorkoutLog/GetUserWorkoutStats`
**Request:** `null`
**Response:** `HistoryExerciseModel`

### Get User Workout Log Date
**Endpoint:** `POST /api/WorkoutLog/GetUserWorkoutLogDate`
**Request:** `null`
**Response:** `GetUserWorkoutLogDate`

## Chat and Messaging Endpoints

### Send Message
**Endpoint:** `POST /api/Account/SendMessage`
**Request:** `ChatModel`
**Response:** `BooleanModel`

### Send Admin Message
**Endpoint:** `POST /api/Account/SendAdminMessage`
**Request:** `ChatModel`
**Response:** `BooleanModel`

### Fetch Inbox
**Endpoint:** `POST /api/Account/FetchInbox`
**Request:** `null`
**Response:** `List<ChatRoomModel>`

### Fetch Inbox by Type
**Endpoint:** `POST /api/Account/FetchInboxByType`
**Request:** `number` (0 for unread, 1 for read)
**Response:** `List<ChatRoomModel>`

### Fetch Chat Box
**Endpoint:** `POST /api/Account/FetchChatBox`
**Request:** `ChatModel`
**Response:** `List<ChatModel>`

### Fetch Group Messages
**Endpoint:** `POST /api/Account/FetchGroupMessages`
**Request:** `GroupChatModel`
**Response:** `List<GroupChatModel>`

### Delete Group Chat Message
**Endpoint:** `POST /api/Account/DeleteChatMessage`
**Request:** `GroupChatModel`
**Response:** `BooleanModel`

### Mark Message as Read
**Endpoint:** `POST /api/Account/MarkMessageAsRead`
**Request:** `ChatModel`
**Response:** `BooleanModel`

### Get Muted Users
**Endpoint:** `POST /api/Account/GetMutedUserList`
**Request:** `null`
**Response:** `List<string>`

## Request Models

### RegisterModel
```json
{
  "Firstname": "string",
  "Lastname": "string",
  "EmailAddress": "string",
  "Password": "string",
  "ConfirmPassword": "string",
  "SelectedGender": "string",
  "MassUnit": "string",
  "RepsMinimum": "number",
  "RepsMaximum": "number",
  "Age": "number",
  "Increments": "number",
  "Min": "number",
  "Max": "number",
  "IsQuickMode": "boolean",
  "SetStyle": "boolean",
  "IsHumanSupport": "boolean",
  "IsPyramid": "boolean",
  "IsDropSet": "boolean",
  "IsCardio": "boolean",
  "MainGoal": "string",
  "BodyPartPrioriy": "string",
  "AIMessage": "string",
  "ReminderDays": "string",
  "ReminderTime": "timespan",
  "TimeBeforeWorkout": "number",
  "IsReminderEmail": "boolean",
  "BodyWeight": "MultiUnityWeight",
  "WeightGoal": "MultiUnityWeight",
  "LearnMoreDetails": "LearnMore",
  "EquipmentModel": "EquipmentModel",
  "ProgramId": "number",
  "IsMobility": "boolean",
  "MobilityLevel": "string",
  "IsExerciseQuickMode": "boolean",
  "IsRecommendedReminder": "boolean"
}
```

### LoginModel
```json
{
  "Username": "string",
  "Password": "string",
  "NewPassword": "string",
  "Validation": "string"
}
```

### GetRecommendationForExerciseModel
```json
{
  "Username": "string",
  "ExerciseId": "number",
  "WorkoutId": "number",
  "IsQuickMode": "boolean",
  "LightSessionDays": "number",
  "SwapedExId": "number",
  "IsStrengthPhashe": "boolean",
  "IsFreePlan": "boolean",
  "IsFirstWorkoutOfStrengthPhase": "boolean",
  "VersionNo": "number"
}
```

### SaveWorkoutModel
```json
{
  "WorkoutId": "number",
  "WorkoutTemplateId": "number"
}
```

### ChatModel
```json
{
  "Id": "number",
  "Message": "string",
  "UserId": "string",
  "UserName": "string",
  "MessageDate": "datetime",
  "IsRead": "boolean",
  "IsFromAdmin": "boolean"
}
```

### DeviceModel
```json
{
  "DeviceToken": "string",
  "DeviceType": "string",
  "UserId": "string"
}
```

## Error Handling

### HTTP Status Codes

- **200 OK**: Request successful
- **400 Bad Request**: Invalid request format or parameters
- **401 Unauthorized**: Authentication required or token expired
- **500 Internal Server Error**: Server-side error

### Error Response Format

```json
{
  "error": "string",
  "error_description": "string"
}
```

### Common Error Scenarios

1. **Token Expiration**: Returns 401 status code
2. **Network Connectivity**: Implement retry logic with exponential backoff
3. **Server Timeout**: Default timeout is 100 seconds, 200 seconds for heavy operations
4. **Rate Limiting**: Implement appropriate delays between requests

### Retry Logic Implementation

The MAUI app implements retry logic for failed requests:

```csharp
// Retry up to 4 times for failed requests
if (attempNr > 0)
    return await PostJson<T>(route, model, attempNr - 1);

// Show user-friendly error dialog
var result = await DisplayCustomPopupForResult(
    "Loading error",
    "Slow or no connection. Please check and try again.",
    "Retry loading",
    "Cancel"
);

if (result == PopupAction.OK) {
    attempNr = 4;
    return await PostJson<T>(route, model, attempNr - 1);
}
```

## Implementation Guidelines

### HTTP Client Configuration

1. **Base Address**: Set to appropriate environment URL
2. **Authorization Header**: Include bearer token for authenticated requests
3. **Content-Type**: Use `application/json` for POST requests
4. **Timeout**: Configure appropriate timeouts (100-200 seconds)

### Request/Response Handling

1. **Serialization**: Use JSON serialization for request/response bodies
2. **Error Handling**: Implement proper error handling for all status codes
3. **Logging**: Log requests and responses for debugging (exclude sensitive data)
4. **Retry Logic**: Implement retry mechanism for transient failures

### Authentication Flow

1. **Login**: Call `/token` endpoint with credentials
2. **Store Token**: Securely store the access token
3. **Include Token**: Add bearer token to all subsequent requests
4. **Token Refresh**: Handle token expiration and refresh as needed
5. **Logout**: Clear stored tokens and session data

### Data Validation

1. **Input Validation**: Validate all user inputs before sending to API
2. **Response Validation**: Validate API responses before processing
3. **Type Safety**: Use strongly-typed models for all API interactions
4. **Null Handling**: Handle null/undefined values appropriately

## Example Implementation (JavaScript/TypeScript)

```typescript
class DrMuscleApiClient {
  private baseUrl: string;
  private accessToken: string | null = null;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
  }

  async login(username: string, password: string): Promise<LoginSuccessResult> {
    const formData = new URLSearchParams();
    formData.append('grant_type', 'password');
    formData.append('username', username);
    formData.append('password', password);

    const response = await fetch(`${this.baseUrl}/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData,
    });

    if (!response.ok) {
      throw new Error('Login failed');
    }

    const result = await response.json();
    this.accessToken = result.access_token;
    return result;
  }

  async postJson<T>(route: string, model: any): Promise<T> {
    const response = await fetch(`${this.baseUrl}/${route}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': this.accessToken ? `bearer ${this.accessToken}` : '',
      },
      body: JSON.stringify(model),
    });

    if (!response.ok) {
      if (response.status === 401) {
        // Handle unauthorized - redirect to login
        throw new Error('Unauthorized');
      }
      throw new Error(`API Error: ${response.status}`);
    }

    return await response.json();
  }

  async getUserWorkoutGroup(): Promise<GetUserWorkoutTemplateGroupResponseModel> {
    return this.postJson('api/Workout/GetUserWorkoutTemplateGroup', null);
  }

  async getRecommendationForExercise(model: GetRecommendationForExerciseModel): Promise<RecommendationModel> {
    return this.postJson('api/Exercise/GetRecommendationForExercise', model);
  }

  async addWorkoutLogSerie(model: WorkoutLogSerieModel): Promise<BooleanModel> {
    return this.postJson('api/Exercise/AddWorkoutLogSerieNew', model);
  }
}
```

## Testing Recommendations

1. **Unit Tests**: Test API client methods with mock responses
2. **Integration Tests**: Test against actual API endpoints
3. **Error Scenarios**: Test error handling and retry logic
4. **Performance Tests**: Test with large datasets and slow connections
5. **Authentication Tests**: Test token handling and refresh scenarios

## Security Considerations

1. **Token Storage**: Store access tokens securely (encrypted storage)
2. **HTTPS Only**: Always use HTTPS for API communication
3. **Input Sanitization**: Sanitize all user inputs before sending to API
4. **Sensitive Data**: Never log sensitive information (passwords, tokens)
5. **Token Expiration**: Handle token expiration gracefully

---

*This API reference guide is based on the Dr. Muscle MAUI application codebase analysis. For the most up-to-date API specifications, consult the backend API documentation or contact the development team.*
