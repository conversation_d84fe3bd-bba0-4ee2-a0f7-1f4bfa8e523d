﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectTypeGuids>{EFBA0AD7-5A72-4C68-AF49-83D382785DCF};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <ProjectGuid>{25CAE135-CB1B-4BBC-A3B7-F5A1AE6F1841}</ProjectGuid>
    <TemplateGuid>{0af826e4-e8b7-435f-b02e-4101b634e385}</TemplateGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>DrMuscleWear</RootNamespace>
    <AssemblyName>DrMuscleWear</AssemblyName>
    <Deterministic>True</Deterministic>
    <MonoAndroidAssetsPrefix>Assets</MonoAndroidAssetsPrefix>
    <MonoAndroidResourcePrefix>Resources</MonoAndroidResourcePrefix>
    <AndroidResgenClass>Resource</AndroidResgenClass>
    <AndroidResgenFile>Resources\Resource.designer.cs</AndroidResgenFile>
    <AndroidApplication>True</AndroidApplication>
    <AndroidManifest>Properties\AndroidManifest.xml</AndroidManifest>
    <IsAppExtension>true</IsAppExtension>
    <TargetFrameworkVersion>v9.0</TargetFrameworkVersion>
    <AndroidEnableSGenConcurrent>true</AndroidEnableSGenConcurrent>
    <AndroidUseAapt2>true</AndroidUseAapt2>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>True</DebugSymbols>
    <DebugType>portable</DebugType>
    <Optimize>False</Optimize>
    <OutputPath>bin\Debug</OutputPath>
    <DefineConstants>DEBUG;</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AndroidLinkMode>None</AndroidLinkMode>
    <ConsolePause>False</ConsolePause>
    <EmbedAssembliesIntoApk>False</EmbedAssembliesIntoApk>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>True</DebugSymbols>
    <DebugType>portable</DebugType>
    <Optimize>True</Optimize>
    <OutputPath>bin\Release</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AndroidUseSharedRuntime>False</AndroidUseSharedRuntime>
    <ConsolePause>False</ConsolePause>
    <EmbedAssembliesIntoApk>True</EmbedAssembliesIntoApk>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="Mono.Android" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="MainActivity.cs" />
    <Compile Include="Resources\Resource.designer.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\AboutResources.txt" />
    <None Include="Assets\AboutAssets.txt" />
    <None Include="Properties\AndroidManifest.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\values\strings.xml" />
    <AndroidResource Include="Resources\layout\custom_spinner_adapter.xml">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\layout\rect_layout.xml">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\layout\round_layout.xml">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\layout\activity_main.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\mipmap-hdpi\ic_launcher.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\mipmap-mdpi\ic_launcher.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\mipmap-xhdpi\ic_launcher.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\mipmap-xxhdpi\ic_launcher.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\values\dimens.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\values-round\strings.xml" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Resources\drawable\" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Xamarin.Android.Support.Percent" Version="********" />
    <PackageReference Include="Xamarin.Android.Support.Wear" Version="********" />
    <PackageReference Include="Xamarin.Android.Support.Core.Utils" Version="********" />
    <PackageReference Include="Xamarin.Android.Support.Fragment" Version="********" />
    <PackageReference Include="Xamarin.Android.Support.Media.Compat" Version="********" />
    <PackageReference Include="Xamarin.Android.Wear" Version="2.2.0" />
    <PackageReference Include="Xamarin.GooglePlayServices.Wearable">
      <Version>71.1601.4</Version>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json">
      <Version>13.0.3</Version>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\DrMuscleWebApiSharedModel\DrMuscleWebApiSharedModel\DrMuscleWebApiSharedModel.csproj">
      <Project>{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}</Project>
      <Name>DrMuscleWebApiSharedModel2</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath)\Xamarin\Android\Xamarin.Android.CSharp.targets" />
</Project>